<?php
// Test script để thử các endpoint command khác nhau
echo "<h2>Test Command Endpoints</h2>";

$base_url = 'https://panel.gamehosting.vn';
$api_key = 'ptlc_DzHfXQUGVnsUhaA9cz6M27J47hRIEyAGo9t7YYMl0Gs';
$server_id = '8661ab4b';
$test_command = 'list';

// Các endpoint có thể có
$endpoints_to_test = [
    'Standard API' => "$base_url/api/client/servers/$server_id/command",
    'Without client' => "$base_url/api/servers/$server_id/command", 
    'Direct servers' => "$base_url/servers/$server_id/command",
    'With console' => "$base_url/api/client/servers/$server_id/console/command",
    'Application API' => "$base_url/api/application/servers/$server_id/command",
    'Admin API' => "$base_url/api/admin/servers/$server_id/command"
];

foreach ($endpoints_to_test as $name => $endpoint) {
    echo "<h3>Test: $name</h3>";
    echo "<p><strong>URL:</strong> <code>$endpoint</code></p>";
    
    $data = json_encode(['command' => $test_command]);
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $endpoint,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $data,
        CURLOPT_HTTPHEADER => [
            'Accept: application/json',
            'Content-Type: application/json',
            'Authorization: Bearer ' . $api_key
        ],
        CURLOPT_TIMEOUT => 10
    ]);
    
    $start_time = microtime(true);
    $response = curl_exec($ch);
    $end_time = microtime(true);
    
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    $response_time = round(($end_time - $start_time) * 1000, 2);
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    
    if ($error) {
        echo "❌ <strong>cURL Error:</strong> $error<br>";
    } else {
        echo "📊 <strong>Response Time:</strong> {$response_time}ms<br>";
        echo "📋 <strong>HTTP Code:</strong> $http_code<br>";
        
        if ($http_code === 204) {
            echo "✅ <strong>Status:</strong> Command sent successfully!<br>";
            echo "🎉 <strong>FOUND WORKING ENDPOINT!</strong><br>";
        } elseif ($http_code === 401) {
            echo "❌ <strong>Status:</strong> Unauthorized - API key invalid<br>";
        } elseif ($http_code === 404) {
            echo "❌ <strong>Status:</strong> Endpoint not found<br>";
        } elseif ($http_code === 405) {
            echo "⚠️ <strong>Status:</strong> Method not allowed (try GET instead of POST)<br>";
        } elseif ($http_code === 502) {
            echo "⚠️ <strong>Status:</strong> Server offline<br>";
        } elseif ($http_code === 403) {
            echo "❌ <strong>Status:</strong> Forbidden<br>";
        } else {
            echo "❌ <strong>Status:</strong> Error HTTP $http_code<br>";
        }
        
        if (!empty($response) && strlen($response) < 500) {
            echo "<strong>Response:</strong><br>";
            echo "<pre>" . htmlspecialchars($response) . "</pre>";
        } elseif (!empty($response)) {
            echo "<strong>Response (first 300 chars):</strong><br>";
            echo "<pre>" . htmlspecialchars(substr($response, 0, 300)) . "...</pre>";
        }
    }
    
    echo "</div>";
}

echo "<hr>";
echo "<h3>Test với GET method (một số endpoint có thể cần GET):</h3>";

$get_endpoints = [
    'GET Standard' => "$base_url/api/client/servers/$server_id/command?command=" . urlencode($test_command),
    'GET Console' => "$base_url/api/client/servers/$server_id/console?command=" . urlencode($test_command)
];

foreach ($get_endpoints as $name => $endpoint) {
    echo "<h4>$name</h4>";
    echo "<p><strong>URL:</strong> <code>$endpoint</code></p>";
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $endpoint,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => [
            'Accept: application/json',
            'Authorization: Bearer ' . $api_key
        ],
        CURLOPT_TIMEOUT => 10
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<div style='background: #f0f0f0; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
    
    if ($error) {
        echo "❌ <strong>Error:</strong> $error<br>";
    } else {
        echo "📋 <strong>HTTP Code:</strong> $http_code<br>";
        
        if ($http_code === 200 || $http_code === 204) {
            echo "✅ <strong>Success!</strong><br>";
        } else {
            echo "❌ <strong>Failed</strong><br>";
        }
        
        if (!empty($response) && strlen($response) < 300) {
            echo "<strong>Response:</strong> " . htmlspecialchars($response) . "<br>";
        }
    }
    
    echo "</div>";
}

echo "<hr>";
echo "<h3>Kết luận:</h3>";
echo "<ul>";
echo "<li>Tìm endpoint trả về <strong>HTTP 204</strong> = Thành công</li>";
echo "<li>Nếu tất cả đều 404 = Endpoint không tồn tại</li>";
echo "<li>Nếu có 502 = Server offline nhưng endpoint đúng</li>";
echo "<li>Nếu có 401 = API key sai</li>";
echo "<li>Nếu có 405 = Method sai (POST vs GET)</li>";
echo "</ul>";

echo "<p><strong>Sau khi tìm được endpoint đúng, cập nhật vào code chính!</strong></p>";
echo "<p><strong>Xóa file này sau khi test xong!</strong></p>";
?>

<style>
pre {
    background: #e9ecef;
    padding: 8px;
    border-radius: 3px;
    overflow-x: auto;
    max-height: 150px;
    font-size: 12px;
}

code {
    background: #e9ecef;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 12px;
}

div {
    margin: 5px 0;
}
</style>
