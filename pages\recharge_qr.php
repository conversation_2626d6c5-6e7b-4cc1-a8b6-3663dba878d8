<?php
// Ki<PERSON>m tra đăng nhập
if (!isset($_SESSION['user_id'])) {
    $_SESSION['message'] = 'Vui lòng đăng nhập để nạp tiền.';
    $_SESSION['message_type'] = 'danger';
    header('Location: /login');
    exit;
}

// Lấy thông tin người dùng
$user = getUserData($_SESSION['user_id']);

// Nếu không tìm thấy thông tin người dùng, chuyển hướng đến trang đăng nhập
if (!$user) {
    $_SESSION['message'] = 'Không tìm thấy thông tin tài khoản. Vui lòng đăng nhập lại.';
    $_SESSION['message_type'] = 'danger';
    header('Location: /logout');
    exit;
}

// Cấu hình QR
$bank_id = 'mbbank';
$account_no = '**********';
$template = 'compact2';
$account_name = 'DPTMC Server';

// <PERSON><PERSON> lý form nạp tiền
$qr_url = '';
$amount = '';
$server = '';
$transaction_code = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $amount = (int)($_POST['amount'] ?? 0);
    $server = $_POST['server'] ?? '';

    if ($amount >= 1000 && !empty($server)) {
        // Tạo mã giao dịch unique
        $transaction_code = $server . strtoupper(substr(md5(uniqid()), 0, 8));

        // Tạo nội dung chuyển khoản
        $content = urlencode($transaction_code);

        // Tạo URL QR
        $qr_url = "https://img.vietqr.io/image/{$bank_id}-{$account_no}-{$template}.jpg?amount={$amount}&addInfo={$content}&accountName=" . urlencode($account_name);

        // Lưu thông tin giao dịch tạm thời
        $_SESSION['pending_transaction'] = [
            'user_id' => $_SESSION['user_id'],
            'amount' => $amount,
            'server' => $server,
            'transaction_code' => $transaction_code,
            'created_at' => time()
        ];
    } else {
        $error_message = 'Vui lòng nhập số tiền tối thiểu 1.000 VNĐ và chọn server.';
    }
}
?>

<!-- Custom CSS cho trang nạp QR -->
<style>
.recharge-container {
    margin-top: 2rem;
    margin-bottom: 3rem;
}

.recharge-card {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.recharge-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.recharge-header {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.recharge-body {
    padding: 2rem;
}

.amount-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.amount-btn {
    padding: 1rem;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    font-weight: 600;
}

.amount-btn:hover {
    border-color: var(--primary);
    background: rgba(var(--primary-rgb), 0.1);
    transform: translateY(-2px);
}

.amount-btn.active {
    border-color: var(--primary);
    background: var(--primary);
    color: white;
}

.qr-container {
    text-align: center;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 15px;
    margin-top: 2rem;
}

.qr-image {
    max-width: 300px;
    width: 100%;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.transaction-info {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

.btn-primary {
    border-radius: 10px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(var(--primary-rgb), 0.4);
}

.server-select {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.server-btn {
    padding: 1rem;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    font-weight: 600;
}

.server-btn:hover {
    border-color: var(--success);
    background: rgba(40, 167, 69, 0.1);
    transform: translateY(-2px);
}

.server-btn.active {
    border-color: var(--success);
    background: var(--success);
    color: white;
}

@media (max-width: 768px) {
    .amount-buttons {
        grid-template-columns: repeat(2, 1fr);
    }

    .server-select {
        grid-template-columns: 1fr;
    }
}
</style>

<div class="container recharge-container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="recharge-card">
                <div class="recharge-header">
                    <h3><i class="fas fa-qrcode mr-2"></i>Nạp tiền bằng QR Code</h3>
                    <p class="mb-0">Quét mã QR để nạp tiền vào tài khoản game</p>
                </div>

                <div class="recharge-body">
                    <?php if (isset($error_message)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" id="rechargeForm">
                        <div class="form-group">
                            <label class="font-weight-bold">
                                <i class="fas fa-server mr-2"></i>Chọn Server:
                            </label>
                            <div class="server-select">
                                <div class="server-btn" data-server="earth">
                                    <i class="fas fa-globe mr-2"></i>Earth
                                </div>
                                <div class="server-btn" data-server="ultrapvp">
                                    <i class="fas fa-sword mr-2"></i>UltraPvP
                                </div>
                                <div class="server-btn" data-server="skyblock">
                                    <i class="fas fa-cube mr-2"></i>Skyblock
                                </div>
                            </div>
                            <input type="hidden" name="server" id="serverInput" value="<?php echo htmlspecialchars($server); ?>">
                        </div>

                        <div class="form-group">
                            <label class="font-weight-bold">
                                <i class="fas fa-money-bill-wave mr-2"></i>Chọn số tiền:
                            </label>
                            <div class="amount-buttons">
                                <div class="amount-btn" data-amount="10000">10.000 VNĐ</div>
                                <div class="amount-btn" data-amount="20000">20.000 VNĐ</div>
                                <div class="amount-btn" data-amount="50000">50.000 VNĐ</div>
                                <div class="amount-btn" data-amount="100000">100.000 VNĐ</div>
                                <div class="amount-btn" data-amount="200000">200.000 VNĐ</div>
                                <div class="amount-btn" data-amount="500000">500.000 VNĐ</div>
                            </div>

                            <div class="form-group">
                                <label for="customAmount">Hoặc nhập số tiền khác:</label>
                                <input type="number" class="form-control" id="customAmount" name="amount"
                                       placeholder="Nhập số tiền (tối thiểu 1.000 VNĐ)"
                                       min="1000" step="1000" value="<?php echo htmlspecialchars($amount); ?>">
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-qrcode mr-2"></i>Tạo mã QR
                        </button>
                    </form>

                    <?php if (!empty($qr_url)): ?>
                        <div class="qr-container">
                            <h5><i class="fas fa-mobile-alt mr-2"></i>Quét mã QR để thanh toán</h5>
                            <img src="<?php echo htmlspecialchars($qr_url); ?>" alt="QR Code" class="qr-image">

                            <div class="transaction-info">
                                <h6><i class="fas fa-info-circle mr-2"></i>Thông tin giao dịch:</h6>
                                <p><strong>Số tiền:</strong> <?php echo number_format($amount, 0, ',', '.'); ?> VNĐ</p>
                                <p><strong>Server:</strong> <?php echo htmlspecialchars($server); ?></p>
                                <p><strong>Mã giao dịch:</strong> <code><?php echo htmlspecialchars($transaction_code); ?></code></p>
                                <p><strong>Nội dung chuyển khoản:</strong> <code><?php echo htmlspecialchars($transaction_code); ?></code></p>

                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-lightbulb mr-2"></i>
                                    <strong>Hướng dẫn:</strong><br>
                                    1. Mở ứng dụng ngân hàng trên điện thoại<br>
                                    2. Quét mã QR hoặc chuyển khoản thủ công<br>
                                    3. Kiểm tra thông tin và xác nhận thanh toán<br>
                                    4. Tiền sẽ được cộng vào tài khoản game trong vòng 1-5 phút
                                </div>

                                <button type="button" class="btn btn-success btn-block" onclick="checkPayment()">
                                    <i class="fas fa-sync-alt mr-2"></i>Kiểm tra thanh toán
                                </button>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Xử lý chọn server
document.querySelectorAll('.server-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        document.querySelectorAll('.server-btn').forEach(b => b.classList.remove('active'));
        this.classList.add('active');
        document.getElementById('serverInput').value = this.dataset.server;
    });
});

// Xử lý chọn số tiền
document.querySelectorAll('.amount-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        document.querySelectorAll('.amount-btn').forEach(b => b.classList.remove('active'));
        this.classList.add('active');
        document.getElementById('customAmount').value = this.dataset.amount;
    });
});

// Khôi phục trạng thái đã chọn
<?php if (!empty($server)): ?>
document.querySelector(`[data-server="<?php echo $server; ?>"]`)?.classList.add('active');
<?php endif; ?>

<?php if (!empty($amount)): ?>
document.querySelector(`[data-amount="<?php echo $amount; ?>"]`)?.classList.add('active');
<?php endif; ?>

// Hàm kiểm tra thanh toán
function checkPayment() {
    const btn = event.target;
    const originalText = btn.innerHTML;

    btn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Đang kiểm tra...';
    btn.disabled = true;

    fetch('/api/check-payment.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            transaction_code: '<?php echo $transaction_code; ?>'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Thanh toán thành công! Tiền đã được cộng vào tài khoản game.');
            window.location.href = '/recharge-history';
        } else {
            let message = data.message || 'Chưa nhận được thanh toán. Vui lòng thử lại sau.';

            // Hiển thị debug info nếu có
            if (data.debug) {
                console.log('Debug info:', data.debug);
                message += '\n\nThông tin debug (xem Console để biết chi tiết):';
                message += '\nMã giao dịch: ' + data.debug.transaction_code;
                message += '\nSố tiền: ' + data.debug.expected_amount;
                message += '\nServer: ' + data.debug.server;

                if (data.debug.recent_payments && data.debug.recent_payments.length > 0) {
                    message += '\n\nGiao dịch gần nhất:';
                    data.debug.recent_payments.forEach((payment, index) => {
                        message += '\n' + (index + 1) + '. ' + payment.amount + ' VNĐ - ' + payment.content.substring(0, 50) + '...';
                    });
                }
            }

            alert(message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi kiểm tra thanh toán.');
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}
</script>
