<?php
// Ki<PERSON>m tra đăng nhập
if (!isset($_SESSION['user_id'])) {
    $_SESSION['message'] = 'Vui lòng đăng nhập để nạp tiền.';
    $_SESSION['message_type'] = 'danger';
    header('Location: /login');
    exit;
}

// Lấy thông tin người dùng
$user = getUserData($_SESSION['user_id']);

// Nếu không tìm thấy thông tin người dùng, chuyển hướng đến trang đăng nhập
if (!$user) {
    $_SESSION['message'] = 'Không tìm thấy thông tin tài khoản. Vui lòng đăng nhập lại.';
    $_SESSION['message_type'] = 'danger';
    header('Location: /logout');
    exit;
}

// Cấu hình QR
$bank_id = 'mbbank';
$account_no = '**********';
$template = 'compact2';
$account_name = 'DPTMC Server';

// <PERSON><PERSON> lý form nạp tiền
$qr_url = '';
$amount = '';
$server = '';
$transaction_code = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $amount = (int)($_POST['amount'] ?? 0);
    $server = $_POST['server'] ?? '';

    if ($amount >= 1000 && !empty($server)) {
        // Tạo mã giao dịch unique
        $transaction_code = $server . strtoupper(substr(md5(uniqid()), 0, 8));

        // Tạo nội dung chuyển khoản
        $content = urlencode($transaction_code);

        // Tạo URL QR
        $qr_url = "https://img.vietqr.io/image/{$bank_id}-{$account_no}-{$template}.jpg?amount={$amount}&addInfo={$content}&accountName=" . urlencode($account_name);

        // Hủy tất cả giao dịch pending cũ của user này
        try {
            $stmt = $conn->prepare("
                UPDATE nlogin_qr_pending
                SET processed = 1, processed_at = NOW()
                WHERE user_id = :user_id AND processed = 0
            ");
            $stmt->bindParam(':user_id', $_SESSION['user_id']);
            $stmt->execute();
        } catch(PDOException $e) {
            error_log("Error canceling old transactions: " . $e->getMessage());
        }

        // Lưu thông tin giao dịch tạm thời vào session
        $_SESSION['pending_transaction'] = [
            'user_id' => $_SESSION['user_id'],
            'amount' => $amount,
            'server' => $server,
            'transaction_code' => $transaction_code,
            'created_at' => time()
        ];

        // Lưu giao dịch mới vào bảng pending
        try {
            $stmt = $conn->prepare("
                INSERT INTO nlogin_qr_pending
                (user_id, username, amount, server, transaction_code, created_at)
                VALUES (:user_id, :username, :amount, :server, :transaction_code, NOW())
            ");

            $stmt->bindParam(':user_id', $_SESSION['user_id']);
            $stmt->bindParam(':username', $user['last_name']);
            $stmt->bindParam(':amount', $amount);
            $stmt->bindParam(':server', $server);
            $stmt->bindParam(':transaction_code', $transaction_code);
            $stmt->execute();
        } catch(PDOException $e) {
            error_log("Error saving pending transaction: " . $e->getMessage());
        }
    } else {
        $error_message = 'Vui lòng nhập số tiền tối thiểu 1.000 VNĐ và chọn server.';
    }
}
?>

<!-- Custom CSS cho trang nạp QR -->
<style>
.recharge-container {
    margin-top: 2rem;
    margin-bottom: 3rem;
}

.recharge-card {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.recharge-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.recharge-header {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.recharge-body {
    padding: 2rem;
}

.amount-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.amount-btn {
    padding: 1rem;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    font-weight: 600;
}

.amount-btn:hover {
    border-color: var(--primary);
    background: rgba(var(--primary-rgb), 0.1);
    transform: translateY(-2px);
}

.amount-btn.active {
    border-color: var(--primary);
    background: var(--primary);
    color: white;
}

.qr-container {
    text-align: center;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 15px;
    margin-top: 2rem;
}

.qr-image {
    max-width: 300px;
    width: 100%;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.transaction-info {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

.btn-primary {
    border-radius: 10px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(var(--primary-rgb), 0.4);
}

.server-select {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.server-btn {
    padding: 1rem;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    font-weight: 600;
}

.server-btn:hover {
    border-color: var(--success);
    background: rgba(40, 167, 69, 0.1);
    transform: translateY(-2px);
}

.server-btn.active {
    border-color: var(--success);
    background: var(--success);
    color: white;
}

@media (max-width: 768px) {
    .amount-buttons {
        grid-template-columns: repeat(2, 1fr);
    }

    .server-select {
        grid-template-columns: 1fr;
    }
}

/* Loading spinner */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Animation cho thông báo thành công */
@keyframes fadeInBounce {
    0% {
        opacity: 0;
        transform: translateY(-20px) scale(0.9);
    }
    50% {
        opacity: 1;
        transform: translateY(5px) scale(1.05);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

#success-notification {
    border-left: 5px solid #28a745;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

/* QR loading state */
#qr-loading {
    min-height: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}
</style>

<div class="container recharge-container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="recharge-card">
                <div class="recharge-header">
                    <h3><i class="fas fa-qrcode mr-2"></i>Nạp tiền bằng QR Code</h3>
                    <p class="mb-0">Quét mã QR để nạp tiền vào tài khoản game</p>
                </div>

                <div class="recharge-body">
                    <?php if (isset($error_message)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" id="rechargeForm">
                        <div class="form-group">
                            <label class="font-weight-bold">
                                <i class="fas fa-server mr-2"></i>Chọn Server:
                            </label>
                            <div class="server-select">
                                <div class="server-btn" data-server="earth">
                                    <i class="fas fa-globe mr-2"></i>Earth
                                </div>
                                <div class="server-btn" data-server="ultrapvp">
                                    <i class="fas fa-sword mr-2"></i>UltraPvP
                                </div>
                                <div class="server-btn" data-server="skyblock">
                                    <i class="fas fa-cube mr-2"></i>Skyblock
                                </div>
                            </div>
                            <input type="hidden" name="server" id="serverInput" value="<?php echo htmlspecialchars($server); ?>">
                        </div>

                        <div class="form-group">
                            <label class="font-weight-bold">
                                <i class="fas fa-money-bill-wave mr-2"></i>Chọn số tiền:
                            </label>
                            <div class="amount-buttons">
                                <div class="amount-btn" data-amount="10000">10.000 VNĐ</div>
                                <div class="amount-btn" data-amount="20000">20.000 VNĐ</div>
                                <div class="amount-btn" data-amount="50000">50.000 VNĐ</div>
                                <div class="amount-btn" data-amount="100000">100.000 VNĐ</div>
                                <div class="amount-btn" data-amount="200000">200.000 VNĐ</div>
                                <div class="amount-btn" data-amount="500000">500.000 VNĐ</div>
                            </div>

                            <div class="form-group">
                                <label for="customAmount">Hoặc nhập số tiền khác:</label>
                                <input type="number" class="form-control" id="customAmount" name="amount"
                                       placeholder="Nhập số tiền (tối thiểu 1.000 VNĐ)"
                                       min="1000" step="1000" value="<?php echo htmlspecialchars($amount); ?>">
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-qrcode mr-2"></i>Tạo mã QR
                        </button>
                    </form>

                    <?php if (!empty($qr_url)): ?>
                        <div class="qr-container">
                            <h5><i class="fas fa-mobile-alt mr-2"></i>Quét mã QR để thanh toán</h5>

                            <!-- Loading QR -->
                            <div id="qr-loading" class="text-center" style="display: none;">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="sr-only">Đang tải QR...</span>
                                </div>
                                <p class="mt-2">Đang tạo mã QR...</p>
                            </div>

                            <!-- QR Image -->
                            <img id="qr-image" src="<?php echo htmlspecialchars($qr_url); ?>" alt="QR Code" class="qr-image" style="display: none;" onload="showQR()">

                            <div class="transaction-info">
                                <h6><i class="fas fa-info-circle mr-2"></i>Thông tin giao dịch:</h6>
                                <p><strong>Số tiền:</strong> <?php echo number_format($amount, 0, ',', '.'); ?> VNĐ</p>
                                <p><strong>Server:</strong> <?php echo htmlspecialchars($server); ?></p>
                                <p><strong>Mã giao dịch:</strong> <code><?php echo htmlspecialchars($transaction_code); ?></code></p>
                                <p><strong>Nội dung chuyển khoản:</strong> <code><?php echo htmlspecialchars($transaction_code); ?></code></p>

                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-lightbulb mr-2"></i>
                                    <strong>Hướng dẫn:</strong><br>
                                    1. Mở ứng dụng ngân hàng trên điện thoại<br>
                                    2. Quét mã QR hoặc chuyển khoản thủ công<br>
                                    3. Kiểm tra thông tin và xác nhận thanh toán<br>
                                    4. Tiền sẽ được cộng vào tài khoản game <strong>TỰ ĐỘNG</strong> trong vòng 1-3 phút
                                </div>

                                <div class="alert alert-success mt-3">
                                    <i class="fas fa-robot mr-2"></i>
                                    <strong>Hệ thống tự động:</strong> Sau khi chuyển khoản thành công, hệ thống sẽ tự động kiểm tra và cộng tiền vào game. Bạn không cần thao tác gì thêm.
                                </div>

                                <!-- Thông báo thành công sẽ hiển thị ở đây -->
                                <div id="success-notification" class="alert alert-success" style="display: none;">
                                    <i class="fas fa-check-circle mr-2"></i>
                                    <strong>Thanh toán thành công!</strong> Tiền đã được cộng vào tài khoản game.
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Xử lý chọn server
document.querySelectorAll('.server-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        document.querySelectorAll('.server-btn').forEach(b => b.classList.remove('active'));
        this.classList.add('active');
        document.getElementById('serverInput').value = this.dataset.server;
    });
});

// Xử lý chọn số tiền
document.querySelectorAll('.amount-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        document.querySelectorAll('.amount-btn').forEach(b => b.classList.remove('active'));
        this.classList.add('active');
        document.getElementById('customAmount').value = this.dataset.amount;
    });
});

// Khôi phục trạng thái đã chọn
<?php if (!empty($server)): ?>
document.querySelector(`[data-server="<?php echo $server; ?>"]`)?.classList.add('active');
<?php endif; ?>

<?php if (!empty($amount)): ?>
document.querySelector(`[data-amount="<?php echo $amount; ?>"]`)?.classList.add('active');
<?php endif; ?>

// Hiển thị QR sau khi load xong
function showQR() {
    document.getElementById('qr-loading').style.display = 'none';
    document.getElementById('qr-image').style.display = 'block';

    // Bắt đầu auto-check sau 30 giây
    setTimeout(startAutoCheck, 30000);
}

// Hiển thị loading QR khi trang load
document.addEventListener('DOMContentLoaded', function() {
    const qrImage = document.getElementById('qr-image');
    if (qrImage) {
        document.getElementById('qr-loading').style.display = 'block';
    }
});

// Auto-check thanh toán
let autoCheckInterval;
let checkCount = 0;
const maxChecks = 20; // Check tối đa 20 lần (10 phút)

function startAutoCheck() {
    const transactionCode = '<?php echo $transaction_code ?? ""; ?>';
    if (!transactionCode) return;

    console.log('Bắt đầu auto-check thanh toán...');

    autoCheckInterval = setInterval(() => {
        checkCount++;
        console.log(`Auto-check lần ${checkCount}/${maxChecks}`);

        fetch('/api/check-payment.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                transaction_code: transactionCode
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Hiển thị thông báo thành công
                showSuccessNotification();
                stopAutoCheck();
            } else if (checkCount >= maxChecks) {
                // Dừng auto-check sau 10 phút
                console.log('Auto-check timeout');
                stopAutoCheck();
            }
        })
        .catch(error => {
            console.error('Auto-check error:', error);
            if (checkCount >= maxChecks) {
                stopAutoCheck();
            }
        });

    }, 30000); // Check mỗi 30 giây
}

function stopAutoCheck() {
    if (autoCheckInterval) {
        clearInterval(autoCheckInterval);
        autoCheckInterval = null;
    }
}

function showSuccessNotification() {
    const notification = document.getElementById('success-notification');
    if (notification) {
        notification.style.display = 'block';
        notification.scrollIntoView({ behavior: 'smooth' });

        // Hiệu ứng animation
        notification.style.animation = 'fadeInBounce 0.5s ease-out';

        // Chuyển hướng sau 3 giây
        setTimeout(() => {
            window.location.href = '/recharge-history';
        }, 3000);
    }
}
</script>
