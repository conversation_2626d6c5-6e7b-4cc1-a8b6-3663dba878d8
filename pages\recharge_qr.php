<?php
// Kiểm tra đăng nhập
if (!isset($_SESSION['user_id'])) {
    $_SESSION['message'] = 'Vui lòng đăng nhập để nạp tiền.';
    $_SESSION['message_type'] = 'danger';
    header('Location: /login');
    exit;
}

// Lấy thông tin người dùng
$user = getUserData($_SESSION['user_id']);

// Nếu không tìm thấy thông tin người dùng, chuyển hướng đến trang đăng nhập
if (!$user) {
    $_SESSION['message'] = 'Không tìm thấy thông tin tài khoản. Vui lòng đăng nhập lại.';
    $_SESSION['message_type'] = 'danger';
    header('Location: /logout');
    exit;
}

// Cấu hình QR
$bank_id = 'mbbank';
$account_no = '**********';
$template = 'compact2';
$account_name = 'DANG DUC';

// <PERSON><PERSON> lý form nạp tiền
$qr_url = '';
$amount = '';
$server = '';
$transaction_code = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $amount = (int)($_POST['amount'] ?? 0);
    $server = $_POST['server'] ?? '';

    if ($amount >= 1000 && !empty($server)) {
        // Tạo mã giao dịch unique với timestamp và user ID để đảm bảo không trùng
        $timestamp = time();
        $user_id = $_SESSION['user_id'];
        $random_string = $timestamp . $user_id . rand(1000, 9999);
        $hash = strtoupper(substr(md5($random_string), 0, 8));
        $transaction_code = $server . $hash;

        // Kiểm tra trùng lặp mã giao dịch và tạo lại nếu cần
        $max_attempts = 5;
        $attempt = 0;

        do {
            $attempt++;

            // Kiểm tra mã đã tồn tại chưa
            $stmt = $conn->prepare("
                SELECT COUNT(*) as count FROM nlogin_qr_pending
                WHERE transaction_code = :transaction_code
                UNION ALL
                SELECT COUNT(*) as count FROM nlogin_qr_transactions
                WHERE transaction_code = :transaction_code
            ");
            $stmt->bindParam(':transaction_code', $transaction_code);
            $stmt->execute();
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $total_count = array_sum(array_column($results, 'count'));

            if ($total_count == 0) {
                // Mã unique, thoát khỏi loop
                break;
            } else {
                // Mã đã tồn tại, tạo mã mới
                $random_string = time() . $user_id . rand(10000, 99999) . $attempt;
                $hash = strtoupper(substr(md5($random_string), 0, 8));
                $transaction_code = $server . $hash;
                error_log("Transaction code exists, regenerating: $transaction_code (attempt $attempt)");
            }

        } while ($attempt < $max_attempts);

        if ($attempt >= $max_attempts) {
            $error_message = 'Không thể tạo mã giao dịch unique. Vui lòng thử lại.';
            error_log("Failed to generate unique transaction code after $max_attempts attempts");
        } else {
            // Log tạo mã giao dịch mới thành công
            error_log("Created new unique transaction code: $transaction_code for user $user_id (attempt $attempt)");
        }

        // Tạo nội dung chuyển khoản
        $content = urlencode($transaction_code);

        // Tạo URL QR
        $qr_url = "https://img.vietqr.io/image/{$bank_id}-{$account_no}-{$template}.jpg?amount={$amount}&addInfo={$content}&accountName=" . urlencode($account_name);

        // Chỉ xóa session pending transaction cũ, KHÔNG hủy trong database ngay lập tức
        if (isset($_SESSION['pending_transaction'])) {
            $old_transaction_code = $_SESSION['pending_transaction']['transaction_code'];
            error_log("Replacing old transaction: $old_transaction_code with new: $transaction_code");
            unset($_SESSION['pending_transaction']);
        }

        // Hủy các giao dịch quá 5 phút của tất cả users
        try {
            $stmt = $conn->prepare("
                UPDATE nlogin_qr_pending
                SET status = 3, processed = 1, processed_at = NOW()
                WHERE status = 0
                AND created_at < DATE_SUB(NOW(), INTERVAL 5 MINUTE)
            ");
            $stmt->execute();
            $expired_count = $stmt->rowCount();

            if ($expired_count > 0) {
                error_log("Auto-cancelled $expired_count expired transactions");
            }
        } catch(PDOException $e) {
            error_log("Error auto-canceling expired transactions: " . $e->getMessage());
        }

        // Lưu thông tin giao dịch tạm thời vào session
        $_SESSION['pending_transaction'] = [
            'user_id' => $_SESSION['user_id'],
            'amount' => $amount,
            'server' => $server,
            'transaction_code' => $transaction_code,
            'created_at' => time()
        ];

        // Lưu giao dịch mới vào bảng pending với status = 0 (pending)
        try {
            $stmt = $conn->prepare("
                INSERT INTO nlogin_qr_pending
                (user_id, username, amount, server, transaction_code, status, created_at)
                VALUES (:user_id, :username, :amount, :server, :transaction_code, 0, NOW())
            ");

            $stmt->bindParam(':user_id', $_SESSION['user_id']);
            $stmt->bindParam(':username', $user['last_name']);
            $stmt->bindParam(':amount', $amount);
            $stmt->bindParam(':server', $server);
            $stmt->bindParam(':transaction_code', $transaction_code);
            $stmt->execute();
        } catch(PDOException $e) {
            error_log("Error saving pending transaction: " . $e->getMessage());
        }
    } else {
        $error_message = 'Vui lòng nhập số tiền tối thiểu 1.000 VNĐ và chọn server.';
    }
}
?>

<!-- Custom CSS cho trang nạp QR -->
<style>
.recharge-container {
    margin-top: 2rem;
    margin-bottom: 3rem;
}

.recharge-card {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.recharge-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.recharge-header {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.recharge-body {
    padding: 2rem;
}

.amount-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.amount-btn {
    padding: 1rem;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    font-weight: 600;
}

.amount-btn:hover {
    border-color: var(--primary);
    background: rgba(var(--primary-rgb), 0.1);
    transform: translateY(-2px);
}

.amount-btn.active {
    border-color: var(--primary);
    background: var(--primary);
    color: white;
}

.qr-container {
    text-align: center;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 15px;
    margin-top: 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.qr-image {
    max-width: 300px;
    width: 100%;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin: 1rem auto;
    display: block;
}

.transaction-info {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 1.5rem;
    width: 100%;
    max-width: 600px;
    text-align: left;
}

.transaction-info p {
    margin-bottom: 0.5rem;
    font-size: 14px;
}

.transaction-info code {
    background: rgba(0, 0, 0, 0.1);
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
}

.transaction-info .text-primary {
    color: #007bff !important;
    font-weight: 700;
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb), 0.25);
}

.btn-primary {
    border-radius: 10px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(var(--primary-rgb), 0.4);
}

.server-select {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.server-btn {
    padding: 1rem;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    font-weight: 600;
}

.server-btn:hover {
    border-color: var(--success);
    background: rgba(40, 167, 69, 0.1);
    transform: translateY(-2px);
}

.server-btn.active {
    border-color: var(--success);
    background: var(--success);
    color: white;
}

@media (max-width: 768px) {
    .amount-buttons {
        grid-template-columns: repeat(2, 1fr);
    }

    .server-select {
        grid-template-columns: 1fr;
    }

    .transaction-info .row {
        margin: 0;
    }

    .transaction-info .col-md-6 {
        padding: 0;
        margin-bottom: 1rem;
    }

    .qr-image {
        max-width: 250px;
    }

    .transaction-info {
        padding: 1rem;
        font-size: 13px;
    }
}

/* Loading spinner */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Animation cho thông báo thành công */
@keyframes fadeInBounce {
    0% {
        opacity: 0;
        transform: translateY(-20px) scale(0.9);
    }
    50% {
        opacity: 1;
        transform: translateY(5px) scale(1.05);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

#success-notification {
    border-left: 5px solid #28a745;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

/* QR loading state */
#qr-loading {
    min-height: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

/* Nút tạo mã mới */
.btn-outline-secondary {
    border: 2px solid #6c757d;
    color: #6c757d;
    background: transparent;
    transition: all 0.3s ease;
}

.btn-outline-secondary:hover {
    background: #6c757d;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

/* Animation cho form hiển thị */
#qr-form {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Countdown Timer */
.countdown-timer {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    margin: 1rem 0;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    animation: pulse 2s infinite;
}

.countdown-display {
    font-family: 'Courier New', monospace;
    font-size: 1.1em;
    font-weight: 700;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 5px;
    margin-left: 0.5rem;
}

.countdown-timer.warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    animation: pulse 1s infinite;
}

.countdown-timer.danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    animation: pulse 0.5s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* QR Expired */
.qr-expired {
    text-align: center;
    padding: 2rem;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 15px;
    margin: 1rem 0;
}
</style>

<div class="container recharge-container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="recharge-card">
                <div class="recharge-header">
                    <h3><i class="fas fa-qrcode mr-2"></i>Nạp tiền bằng QR Code</h3>
                    <p class="mb-0">Quét mã QR để nạp tiền vào tài khoản game</p>
                </div>

                <div class="recharge-body">
                    <?php if (isset($error_message)): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>

                    <!-- Form tạo QR - ẩn khi đã có QR -->
                    <div id="qr-form" <?php echo !empty($qr_url) ? 'style="display: none;"' : ''; ?>>
                        <form method="POST" id="rechargeForm">
                            <div class="form-group">
                                <label class="font-weight-bold">
                                    <i class="fas fa-server mr-2"></i>Chọn Server:
                                </label>
                                <div class="server-select">
                                    <div class="server-btn" data-server="earth">
                                        <i class="fas fa-globe mr-2"></i>Earth
                                    </div>
                                    <div class="server-btn" data-server="ultrapvp">
                                        <i class="fas fa-sword mr-2"></i>UltraPvP
                                    </div>
                                    <div class="server-btn" data-server="skyblock">
                                        <i class="fas fa-cube mr-2"></i>Skyblock
                                    </div>
                                </div>
                                <input type="hidden" name="server" id="serverInput" value="<?php echo htmlspecialchars($server); ?>">
                            </div>

                            <div class="form-group">
                                <label class="font-weight-bold">
                                    <i class="fas fa-money-bill-wave mr-2"></i>Chọn số tiền:
                                </label>
                                <div class="amount-buttons">
                                    <div class="amount-btn" data-amount="10000">10.000 VNĐ</div>
                                    <div class="amount-btn" data-amount="20000">20.000 VNĐ</div>
                                    <div class="amount-btn" data-amount="50000">50.000 VNĐ</div>
                                    <div class="amount-btn" data-amount="100000">100.000 VNĐ</div>
                                    <div class="amount-btn" data-amount="200000">200.000 VNĐ</div>
                                    <div class="amount-btn" data-amount="500000">500.000 VNĐ</div>
                                </div>

                                <div class="form-group">
                                    <label for="customAmount">Hoặc nhập số tiền khác:</label>
                                    <input type="number" class="form-control" id="customAmount" name="amount"
                                           placeholder="Nhập số tiền (tối thiểu 1.000 VNĐ)"
                                           min="1000" step="1000" value="<?php echo htmlspecialchars($amount); ?>">
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary btn-block">
                                <i class="fas fa-qrcode mr-2"></i>Tạo mã QR
                            </button>
                        </form>
                    </div>

                    <?php if (!empty($qr_url)): ?>
                        <!-- Nút tạo mã mới khi đã có QR -->
                        <div class="text-center mb-3">
                            <button type="button" class="btn btn-outline-secondary" onclick="showNewQRForm()">
                                <i class="fas fa-plus mr-2"></i>Tạo mã QR mới
                            </button>
                        </div>

                        <div class="qr-container">
                            <h5><i class="fas fa-mobile-alt mr-2"></i>Quét mã QR để thanh toán</h5>

                            <!-- Thời gian đếm ngược -->
                            <div id="countdown-timer" class="countdown-timer" style="display: none;">
                                <i class="fas fa-clock mr-2"></i>
                                <span>Mã QR hết hạn sau: </span>
                                <span id="countdown-display" class="countdown-display">05:00</span>
                            </div>

                            <!-- Loading QR -->
                            <div id="qr-loading" class="text-center" style="display: none;">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="sr-only">Đang tải QR...</span>
                                </div>
                                <p class="mt-2">Đang tạo mã QR...</p>
                            </div>

                            <!-- QR Image -->
                            <img id="qr-image" src="<?php echo htmlspecialchars($qr_url); ?>" alt="QR Code" class="qr-image" style="display: none;" onload="showQR()">

                            <!-- QR Expired -->
                            <div id="qr-expired" class="qr-expired" style="display: none;">
                                <i class="fas fa-times-circle text-danger" style="font-size: 3rem;"></i>
                                <h5 class="mt-3 text-danger">Mã QR đã hết hạn</h5>
                                <p id="expired-message">Mã QR đã hết hạn sau 5 phút. Vui lòng tạo mã QR mới để tiếp tục thanh toán.</p>
                                <div class="alert alert-warning mt-3">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>
                                    <strong>Lưu ý:</strong> Giao dịch đã bị hủy để đảm bảo bảo mật. Nếu bạn đã chuyển khoản cho giao dịch này, vui lòng liên hệ admin.
                                </div>

                                <div class="alert alert-info mt-2">
                                    <i class="fas fa-info-circle mr-2"></i>
                                    <strong>Có thể do:</strong> Giao dịch cũ chưa được dọn dẹp hoặc bạn đã thực hiện giao dịch này trước đó.
                                </div>
                                <button type="button" class="btn btn-primary" onclick="showNewQRForm()">
                                    <i class="fas fa-redo mr-2"></i>Tạo mã mới
                                </button>
                            </div>

                            <div class="transaction-info">
                                <h6><i class="fas fa-info-circle mr-2"></i>Thông tin giao dịch:</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong><i class="fas fa-money-bill-wave mr-1"></i>Số tiền:</strong> <?php echo number_format($amount, 0, ',', '.'); ?> VNĐ</p>
                                        <p><strong><i class="fas fa-server mr-1"></i>Server:</strong> <?php echo htmlspecialchars($server); ?></p>
                                        <p><strong><i class="fas fa-hashtag mr-1"></i>Mã giao dịch:</strong> <code id="transaction-code"><?php echo htmlspecialchars($transaction_code); ?></code>
                                        <button type="button" class="btn btn-sm btn-outline-primary ml-1" onclick="copyTransactionCode()" title="Copy mã giao dịch">
                                            <i class="fas fa-copy"></i>
                                        </button></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong><i class="fas fa-university mr-1"></i>Ngân hàng:</strong> MB Bank</p>
                                        <p><strong><i class="fas fa-credit-card mr-1"></i>Số tài khoản:</strong> <code>**********</code></p>
                                        <p><strong><i class="fas fa-user mr-1"></i>Tên tài khoản:</strong> DANG DUC</p>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <p><strong><i class="fas fa-comment-alt mr-1"></i>Nội dung chuyển khoản:</strong> <code class="text-primary"><?php echo htmlspecialchars($transaction_code); ?></code></p>
                                </div>

                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-lightbulb mr-2"></i>
                                    <strong>Hướng dẫn:</strong><br>
                                    1. Mở ứng dụng ngân hàng trên điện thoại<br>
                                    2. Quét mã QR hoặc chuyển khoản thủ công<br>
                                    3. Kiểm tra thông tin và xác nhận thanh toán<br>
                                    4. Tiền sẽ được cộng vào tài khoản game <strong>TỰ ĐỘNG</strong> trong vòng 1-3 phút
                                </div>

                                <div class="alert alert-success mt-3">
                                    <i class="fas fa-robot mr-2"></i>
                                    <strong>Hệ thống tự động:</strong> Sau khi chuyển khoản thành công, hệ thống sẽ tự động kiểm tra và cộng tiền vào game. Bạn không cần thao tác gì thêm.
                                </div>

                                <!-- Thông báo thành công sẽ hiển thị ở đây -->
                                <div id="success-notification" class="alert alert-success" style="display: none;">
                                    <i class="fas fa-check-circle mr-2"></i>
                                    <strong>Thanh toán thành công!</strong> Tiền đã được cộng vào tài khoản game.
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Xử lý chọn server
document.querySelectorAll('.server-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        document.querySelectorAll('.server-btn').forEach(b => b.classList.remove('active'));
        this.classList.add('active');
        document.getElementById('serverInput').value = this.dataset.server;
    });
});

// Xử lý chọn số tiền
document.querySelectorAll('.amount-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        document.querySelectorAll('.amount-btn').forEach(b => b.classList.remove('active'));
        this.classList.add('active');
        document.getElementById('customAmount').value = this.dataset.amount;
    });
});

// Khôi phục trạng thái đã chọn
<?php if (!empty($server)): ?>
document.querySelector(`[data-server="<?php echo $server; ?>"]`)?.classList.add('active');
<?php endif; ?>

<?php if (!empty($amount)): ?>
document.querySelector(`[data-amount="<?php echo $amount; ?>"]`)?.classList.add('active');
<?php endif; ?>

// Biến global cho countdown
let countdownInterval;
let timeLeft = 300; // 5 phút = 300 giây
let qrExpired = false;

// Hiển thị QR sau khi load xong
function showQR() {
    document.getElementById('qr-loading').style.display = 'none';
    document.getElementById('qr-image').style.display = 'block';
    document.getElementById('countdown-timer').style.display = 'block';

    // Bắt đầu countdown
    startCountdown();

    // Bắt đầu auto-check sau 20 giây để user có thời gian chuyển khoản
    setTimeout(startAutoCheck, 20000);
}

// Hiển thị loading QR khi trang load
document.addEventListener('DOMContentLoaded', function() {
    const qrImage = document.getElementById('qr-image');
    if (qrImage) {
        document.getElementById('qr-loading').style.display = 'block';
    }
});

// Countdown timer
function startCountdown() {
    const countdownDisplay = document.getElementById('countdown-display');
    const countdownTimer = document.getElementById('countdown-timer');

    countdownInterval = setInterval(() => {
        timeLeft--;

        // Cập nhật hiển thị
        const minutes = Math.floor(timeLeft / 60);
        const seconds = timeLeft % 60;
        countdownDisplay.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        // Thay đổi màu sắc theo thời gian
        if (timeLeft <= 30) {
            countdownTimer.className = 'countdown-timer danger';
        } else if (timeLeft <= 60) {
            countdownTimer.className = 'countdown-timer warning';
        }

        // Hết thời gian
        if (timeLeft <= 0) {
            expireQR();
        }
    }, 1000);
}

// Hủy QR khi hết thời gian
function expireQR(message = null) {
    qrExpired = true;

    // Dừng countdown
    if (countdownInterval) {
        clearInterval(countdownInterval);
    }

    // Dừng auto-check
    stopAutoCheck();

    // Cập nhật message nếu có
    if (message) {
        const expiredMessageEl = document.getElementById('expired-message');
        if (expiredMessageEl) {
            expiredMessageEl.textContent = message;
        }
    }

    // Ẩn QR và hiển thị thông báo hết hạn
    document.getElementById('qr-image').style.display = 'none';
    document.getElementById('countdown-timer').style.display = 'none';
    document.getElementById('qr-expired').style.display = 'block';

    // Scroll đến thông báo
    document.getElementById('qr-expired').scrollIntoView({ behavior: 'smooth' });

    // Hủy giao dịch pending trên server
    cancelPendingTransaction();
}

// Hủy giao dịch pending
function cancelPendingTransaction() {
    const transactionCode = '<?php echo $transaction_code ?? ""; ?>';
    if (!transactionCode) return;

    fetch('/api/cancel-transaction.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            transaction_code: transactionCode
        })
    })
    .then(response => response.json())
    .then(data => {
        console.log('Transaction cancelled:', data);
    })
    .catch(error => {
        console.error('Cancel transaction error:', error);
    });
}

// Auto-check thanh toán
let autoCheckInterval;
let checkCount = 0;
const maxChecks = 20; // Check tối đa 20 lần (10 phút)

function startAutoCheck() {
    const transactionCode = '<?php echo $transaction_code ?? ""; ?>';
    if (!transactionCode) return;

    console.log('Bắt đầu auto-check thanh toán...');

    autoCheckInterval = setInterval(() => {
        // Dừng nếu QR đã hết hạn
        if (qrExpired) {
            stopAutoCheck();
            return;
        }

        checkCount++;
        console.log(`Auto-check lần ${checkCount}/${maxChecks}`);

        fetch('/api/check-payment.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                transaction_code: transactionCode
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log(`Auto-check ${checkCount} response:`, data);

            if (data.success && !data.already_processed) {
                console.log('🎉 PAYMENT SUCCESS DETECTED!');
                console.log('Transaction details:', data);

                // Dừng countdown
                if (countdownInterval) {
                    clearInterval(countdownInterval);
                }

                // Hiển thị thông báo thành công
                showSuccessNotification();
                stopAutoCheck();
            } else if (data.already_processed) {
                // Giao dịch đã được xử lý trước đó - không phải giao dịch hiện tại
                console.log('⚠️ Giao dịch đã được xử lý trước đó:', data.message);
                expireQR('Giao dịch đã được xử lý trước đó. Vui lòng tạo mã QR mới.');
                stopAutoCheck();
            } else if (data.expired) {
                // Giao dịch đã hết hạn hoặc bị hủy
                console.log('❌ Giao dịch đã hết hạn:', data.message);
                expireQR(data.message);
                stopAutoCheck();
            } else if (checkCount >= maxChecks || qrExpired) {
                // Dừng auto-check
                console.log('⏰ Auto-check timeout hoặc QR expired');
                stopAutoCheck();
            } else {
                console.log('⏳ Chưa có thanh toán, tiếp tục check...');
            }
        })
        .catch(error => {
            console.error('Auto-check error:', error);
            if (checkCount >= maxChecks || qrExpired) {
                stopAutoCheck();
            }
        });

    }, 15000); // Check mỗi 15 giây
}

function stopAutoCheck() {
    if (autoCheckInterval) {
        clearInterval(autoCheckInterval);
        autoCheckInterval = null;
    }
}

function showSuccessNotification() {
    console.log('🎉 showSuccessNotification() called');
    console.log('Current time:', new Date().toLocaleTimeString());

    const notification = document.getElementById('success-notification');
    if (notification) {
        console.log('✅ Success notification element found');
        notification.style.display = 'block';
        notification.scrollIntoView({ behavior: 'smooth' });

        // Hiệu ứng animation
        notification.style.animation = 'fadeInBounce 0.5s ease-out';

        // Chuyển hướng sau 3 giây
        setTimeout(() => {
            console.log('🔄 Redirecting to recharge history...');
            window.location.href = '/recharge-history';
        }, 3000);
    } else {
        console.error('❌ Success notification element not found!');
    }
}

// Hiển thị form tạo QR mới
function showNewQRForm() {
    // Hiển thị form
    document.getElementById('qr-form').style.display = 'block';

    // Ẩn QR container hiện tại
    const qrContainer = document.querySelector('.qr-container');
    if (qrContainer) {
        qrContainer.style.display = 'none';
    }

    // Ẩn nút "Tạo mã mới"
    const newQRButton = document.querySelector('.btn-outline-secondary');
    if (newQRButton) {
        newQRButton.parentElement.style.display = 'none';
    }

    // Dừng tất cả timers
    if (countdownInterval) {
        clearInterval(countdownInterval);
    }
    stopAutoCheck();

    // Reset trạng thái
    qrExpired = false;
    checkCount = 0;
    timeLeft = 300;

    // Scroll đến form
    document.getElementById('qr-form').scrollIntoView({ behavior: 'smooth' });
}

// Copy mã giao dịch
function copyTransactionCode() {
    const codeElement = document.getElementById('transaction-code');
    if (codeElement) {
        const code = codeElement.textContent;

        // Sử dụng Clipboard API nếu có
        if (navigator.clipboard) {
            navigator.clipboard.writeText(code).then(() => {
                showCopySuccess();
            }).catch(() => {
                fallbackCopy(code);
            });
        } else {
            fallbackCopy(code);
        }
    }
}

// Fallback copy method
function fallbackCopy(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.opacity = '0';
    document.body.appendChild(textArea);
    textArea.select();

    try {
        document.execCommand('copy');
        showCopySuccess();
    } catch (err) {
        console.error('Copy failed:', err);
        alert('Không thể copy. Vui lòng copy thủ công: ' + text);
    }

    document.body.removeChild(textArea);
}

// Hiển thị thông báo copy thành công
function showCopySuccess() {
    const button = event.target.closest('button');
    const originalHTML = button.innerHTML;

    button.innerHTML = '<i class="fas fa-check text-success"></i>';
    button.disabled = true;

    setTimeout(() => {
        button.innerHTML = originalHTML;
        button.disabled = false;
    }, 1500);

    // Tạo tooltip tạm thời
    const tooltip = document.createElement('div');
    tooltip.textContent = 'Đã copy!';
    tooltip.style.cssText = `
        position: absolute;
        background: #28a745;
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        z-index: 1000;
        pointer-events: none;
        animation: fadeInOut 1.5s ease-out;
    `;

    const rect = button.getBoundingClientRect();
    tooltip.style.left = (rect.left + rect.width / 2 - 25) + 'px';
    tooltip.style.top = (rect.top - 30) + 'px';

    document.body.appendChild(tooltip);

    setTimeout(() => {
        document.body.removeChild(tooltip);
    }, 1500);
}
</script>

<style>
@keyframes fadeInOut {
    0% { opacity: 0; transform: translateY(10px); }
    50% { opacity: 1; transform: translateY(0); }
    100% { opacity: 0; transform: translateY(-10px); }
}
</style>
