<?php
// Script cleanup database QR transactions
require_once 'config/config.php';

echo "<h2>Cleanup QR Database</h2>";

try {
    // 1. <PERSON><PERSON><PERSON> nhật processed_at cho các giao dịch đã processed nhưng chưa có processed_at
    echo "<h3>1. <PERSON><PERSON><PERSON> nhật processed_at cho giao dịch đã processed:</h3>";
    
    $stmt = $conn->prepare("
        UPDATE nlogin_qr_pending 
        SET processed_at = created_at 
        WHERE processed = 1 AND processed_at IS NULL
    ");
    $stmt->execute();
    $updated_count = $stmt->rowCount();
    
    echo "✅ Đã cập nhật processed_at cho $updated_count giao dịch<br>";
    
    // 2. Hủy các giao dịch quá 1 phút và chưa processed
    echo "<h3>2. Hủy giao dịch quá 1 phút:</h3>";
    
    $stmt = $conn->prepare("
        UPDATE nlogin_qr_pending 
        SET processed = 1, processed_at = NOW() 
        WHERE processed = 0 
        AND created_at < DATE_SUB(NOW(), INTERVAL 1 MINUTE)
    ");
    $stmt->execute();
    $expired_count = $stmt->rowCount();
    
    echo "✅ Đã hủy $expired_count giao dịch hết hạn<br>";
    
    // 3. Xóa các giao dịch cũ hơn 24 giờ
    echo "<h3>3. Xóa giao dịch cũ hơn 24 giờ:</h3>";
    
    $stmt = $conn->prepare("
        DELETE FROM nlogin_qr_pending 
        WHERE created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ");
    $stmt->execute();
    $deleted_count = $stmt->rowCount();
    
    echo "✅ Đã xóa $deleted_count giao dịch cũ<br>";
    
    // 4. Hiển thị thống kê hiện tại
    echo "<h3>4. Thống kê hiện tại:</h3>";
    
    // Tổng số giao dịch
    $stmt = $conn->query("SELECT COUNT(*) as total FROM nlogin_qr_pending");
    $total = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Giao dịch đang pending
    $stmt = $conn->query("SELECT COUNT(*) as pending FROM nlogin_qr_pending WHERE processed = 0");
    $pending = $stmt->fetch(PDO::FETCH_ASSOC)['pending'];
    
    // Giao dịch đã processed
    $stmt = $conn->query("SELECT COUNT(*) as processed FROM nlogin_qr_pending WHERE processed = 1");
    $processed_total = $stmt->fetch(PDO::FETCH_ASSOC)['processed'];
    
    // Giao dịch trong 1 giờ qua
    $stmt = $conn->query("
        SELECT COUNT(*) as recent 
        FROM nlogin_qr_pending 
        WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
    ");
    $recent = $stmt->fetch(PDO::FETCH_ASSOC)['recent'];
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>📊 Thống kê:</strong><br>";
    echo "📋 Tổng số giao dịch: <strong>$total</strong><br>";
    echo "⏳ Đang pending: <strong>$pending</strong><br>";
    echo "✅ Đã processed: <strong>$processed_total</strong><br>";
    echo "🕐 Trong 1 giờ qua: <strong>$recent</strong><br>";
    echo "</div>";
    
    // 5. Hiển thị giao dịch gần đây
    echo "<h3>5. Giao dịch gần đây (10 giao dịch cuối):</h3>";
    
    $stmt = $conn->query("
        SELECT id, username, amount, server, transaction_code, 
               created_at, processed, processed_at
        FROM nlogin_qr_pending 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $recent_transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($recent_transactions) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #e9ecef;'>";
        echo "<th>ID</th><th>User</th><th>Amount</th><th>Server</th>";
        echo "<th>Transaction Code</th><th>Created</th><th>Status</th><th>Processed At</th>";
        echo "</tr>";
        
        foreach ($recent_transactions as $tx) {
            $status = $tx['processed'] ? '✅ Processed' : '⏳ Pending';
            $status_color = $tx['processed'] ? '#28a745' : '#ffc107';
            
            echo "<tr>";
            echo "<td>{$tx['id']}</td>";
            echo "<td>{$tx['username']}</td>";
            echo "<td>" . number_format($tx['amount']) . " VNĐ</td>";
            echo "<td>{$tx['server']}</td>";
            echo "<td><code>{$tx['transaction_code']}</code></td>";
            echo "<td>{$tx['created_at']}</td>";
            echo "<td style='color: $status_color; font-weight: bold;'>$status</td>";
            echo "<td>" . ($tx['processed_at'] ?: '-') . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>Không có giao dịch nào.</p>";
    }
    
    // 6. Tạo index để tối ưu performance
    echo "<h3>6. Tối ưu database:</h3>";
    
    try {
        $conn->exec("
            ALTER TABLE nlogin_qr_pending 
            ADD INDEX idx_user_processed (user_id, processed),
            ADD INDEX idx_created_processed (created_at, processed)
        ");
        echo "✅ Đã tạo index tối ưu<br>";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "ℹ️ Index đã tồn tại<br>";
        } else {
            echo "⚠️ Lỗi tạo index: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<hr>";
    echo "<h3>✅ Cleanup hoàn thành!</h3>";
    echo "<p><strong>Khuyến nghị:</strong></p>";
    echo "<ul>";
    echo "<li>Chạy script này định kỳ (hàng ngày) để dọn dẹp database</li>";
    echo "<li>Hoặc thêm vào cron job: <code>0 2 * * * php " . realpath(__FILE__) . "</code></li>";
    echo "<li>Xóa file này sau khi setup xong để bảo mật</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "❌ Lỗi database: " . $e->getMessage();
}
?>

<style>
table {
    border-collapse: collapse;
    margin: 10px 0;
}

table th, table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
    font-size: 12px;
}

table th {
    background-color: #f2f2f2;
    font-weight: bold;
}

code {
    background: #e9ecef;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 11px;
}

div {
    margin: 10px 0;
}
</style>
