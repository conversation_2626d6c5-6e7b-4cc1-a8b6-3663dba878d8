<?php
// Tắt error reporting để tránh HTML output
error_reporting(0);
ini_set('display_errors', 0);

header('Content-Type: application/json');

try {
    require_once '../config/config.php';

    // Kiểm tra đăng nhập
    session_start();
    if (!isset($_SESSION['user_id'])) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Unauthorized']);
        exit;
    }

    // Lấy dữ liệu từ request
    $input_raw = file_get_contents('php://input');
    $input = json_decode($input_raw, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        echo json_encode(['success' => false, 'message' => 'Invalid JSON']);
        exit;
    }

    $transaction_code = $input['transaction_code'] ?? '';

    if (empty($transaction_code)) {
        echo json_encode(['success' => false, 'message' => 'Mã giao dịch không hợp lệ']);
        exit;
    }

    // Hủy giao dịch pending của user này - GIỮ processed = 0 để check nền
    $stmt = $conn->prepare("
        UPDATE nlogin_qr_pending
        SET status = 3, processed = 0, processed_at = NOW(), check_count = 0
        WHERE user_id = :user_id
        AND transaction_code = :transaction_code
        AND status = 0
    ");

    $stmt->bindParam(':user_id', $_SESSION['user_id']);
    $stmt->bindParam(':transaction_code', $transaction_code);
    $stmt->execute();

    $affected_rows = $stmt->rowCount();

    // Log việc hủy giao dịch
    if ($affected_rows > 0) {
        error_log("User {$_SESSION['user_id']} cancelled transaction: $transaction_code");
    }

    // Đồng thời hủy các giao dịch quá hạn khác - GIỮ processed = 0 để check nền
    $stmt = $conn->prepare("
        UPDATE nlogin_qr_pending
        SET status = 3, processed = 0, processed_at = NOW(), check_count = 0
        WHERE status = 0
        AND created_at < DATE_SUB(NOW(), INTERVAL 5 MINUTE)
    ");
    $stmt->execute();
    $expired_count = $stmt->rowCount();

    if ($expired_count > 0) {
        error_log("Auto-cancelled $expired_count expired transactions during manual cancel (keeping processed=0)");
    }

    // Xóa session pending transaction
    if (isset($_SESSION['pending_transaction']) &&
        $_SESSION['pending_transaction']['transaction_code'] === $transaction_code) {
        unset($_SESSION['pending_transaction']);
    }

    echo json_encode([
        'success' => true,
        'message' => 'Giao dịch đã được hủy',
        'affected_rows' => $affected_rows
    ]);

} catch (Exception $e) {
    error_log("Cancel transaction error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Có lỗi xảy ra: ' . $e->getMessage()]);
} catch (Error $e) {
    error_log("Cancel transaction fatal error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Lỗi nghiêm trọng: ' . $e->getMessage()]);
}
?>
