<?php
// Debug script để kiểm tra lịch sử nạp thẻ
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<h2>Debug <PERSON><PERSON><PERSON> sử nạp thẻ</h2>";

// Kiểm tra kết nối
if (!$conn) {
    die("❌ Không thể kết nối database chính!");
}

if (!$card_conn) {
    die("❌ Không thể kết nối database lịch sử nạp thẻ!");
}

echo "✅ Kết nối database thành công!<br><br>";

// Test với user cụ thể
$test_user_id = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 1;

echo "<form method='GET' style='margin-bottom: 20px;'>";
echo "User ID để test: <input type='number' name='user_id' value='" . $test_user_id . "' min='1'>";
echo " <input type='submit' value='Test'>";
echo "</form>";

echo "<h3>Test với User ID: " . $test_user_id . "</h3>";

// Lấy thông tin user
try {
    $stmt = $conn->prepare("SELECT ai, last_name, unique_id FROM nlogin WHERE ai = :user_id");
    $stmt->bindParam(':user_id', $test_user_id);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<h4>1. Thông tin User:</h4>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        echo "<tr><td>User ID</td><td>" . $user['ai'] . "</td></tr>";
        echo "<tr><td>Last Name</td><td>" . htmlspecialchars($user['last_name']) . "</td></tr>";
        echo "<tr><td>Unique ID</td><td>" . htmlspecialchars($user['unique_id']) . "</td></tr>";
        echo "</table><br>";
        
        // Lấy UUID
        $uuid = getUserUUID($test_user_id);
        
        echo "<h4>2. UUID Mapping:</h4>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        echo "<tr><td>UUID tìm thấy</td><td>" . ($uuid ? $uuid : 'NULL') . "</td></tr>";
        
        if ($uuid) {
            $formatted_uuid = formatUUID($uuid);
            $unformatted_uuid = unformatUUID($uuid);
            echo "<tr><td>UUID Formatted</td><td>" . $formatted_uuid . "</td></tr>";
            echo "<tr><td>UUID Unformatted</td><td>" . $unformatted_uuid . "</td></tr>";
        }
        echo "</table><br>";
        
        if ($uuid) {
            // Test count
            $count = getCardRechargeHistoryCount($uuid);
            echo "<h4>3. Count Test:</h4>";
            echo "Tổng số giao dịch: <strong>" . $count . "</strong><br><br>";
            
            // Test history
            $history = getCardRechargeHistory($uuid, 10, 0);
            echo "<h4>4. History Test:</h4>";
            echo "Số bản ghi trả về: <strong>" . count($history) . "</strong><br><br>";
            
            if (!empty($history)) {
                echo "<h4>5. Chi tiết giao dịch:</h4>";
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr><th>ID</th><th>UUID</th><th>Time</th><th>Type</th><th>Price</th><th>Success</th><th>Server</th></tr>";
                
                foreach ($history as $record) {
                    echo "<tr>";
                    echo "<td>" . ($record['id'] ?? 'N/A') . "</td>";
                    echo "<td>" . substr($record['uuid'] ?? 'N/A', 0, 8) . "...</td>";
                    echo "<td>" . ($record['formatted_time'] ?? 'N/A') . "</td>";
                    echo "<td>" . ($record['type_name'] ?? 'N/A') . "</td>";
                    echo "<td>" . ($record['formatted_price'] ?? 'N/A') . "</td>";
                    echo "<td>" . ($record['success'] ?? 'N/A') . "</td>";
                    echo "<td>" . ($record['server'] ?? 'N/A') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "❌ Không có dữ liệu lịch sử nạp thẻ<br>";
                
                // Debug trực tiếp query
                echo "<h4>6. Debug Query trực tiếp:</h4>";
                try {
                    $formatted_uuid = formatUUID($uuid);
                    $unformatted_uuid = unformatUUID($uuid);
                    
                    echo "Thử query với UUID formatted: " . $formatted_uuid . "<br>";
                    $stmt = $card_conn->prepare("SELECT COUNT(*) as count FROM dotman_napthe_log WHERE uuid = :uuid");
                    $stmt->bindParam(':uuid', $formatted_uuid);
                    $stmt->execute();
                    $result = $stmt->fetch(PDO::FETCH_ASSOC);
                    echo "Kết quả: " . $result['count'] . " bản ghi<br><br>";
                    
                    echo "Thử query với UUID unformatted: " . $unformatted_uuid . "<br>";
                    $stmt = $card_conn->prepare("SELECT COUNT(*) as count FROM dotman_napthe_log WHERE uuid = :uuid");
                    $stmt->bindParam(':uuid', $unformatted_uuid);
                    $stmt->execute();
                    $result = $stmt->fetch(PDO::FETCH_ASSOC);
                    echo "Kết quả: " . $result['count'] . " bản ghi<br><br>";
                    
                    // Hiển thị một số UUID mẫu từ database
                    echo "UUID mẫu trong database:<br>";
                    $stmt = $card_conn->query("SELECT DISTINCT uuid FROM dotman_napthe_log LIMIT 10");
                    $sample_uuids = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    foreach ($sample_uuids as $sample) {
                        echo "- " . $sample['uuid'] . "<br>";
                    }
                    
                } catch(PDOException $e) {
                    echo "❌ Lỗi query: " . $e->getMessage() . "<br>";
                }
            }
        } else {
            echo "❌ Không tìm thấy UUID cho user này<br>";
        }
        
    } else {
        echo "❌ Không tìm thấy user với ID: " . $test_user_id . "<br>";
    }
    
} catch(PDOException $e) {
    echo "❌ Lỗi: " . $e->getMessage() . "<br>";
}

echo "<br><hr>";
echo "<p><strong>Lưu ý:</strong> Sau khi debug xong, hãy xóa file này để bảo mật!</p>";
?>
