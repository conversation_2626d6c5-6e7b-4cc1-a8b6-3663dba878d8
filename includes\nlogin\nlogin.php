<?php
/**
 * nlogin - Simple PHP Authentication Plugin
 */

class NLogin {
    private $conn;
    private $table = 'nlogin';
    private $loginUsername;

    /**
     * Initialize NLogin
     *
     * @param PDO $conn Database connection
     */
    public function __construct($conn) {
        $this->conn = $conn;
    }

    /**
     * Register a new user
     *
     * @param string $username Username
     * @param string $email Email
     * @param string $password Password
     * @param array $additional_data Additional user data
     * @return array|bool User data on success, false on failure
     */
    public function register($username, $email, $password, $additional_data = []) {
        try {
            // Check if username or email already exists
            $stmt = $this->conn->prepare("SELECT * FROM {$this->table} WHERE unique_id = :username OR last_name = :username OR email = :email");
            $stmt->bindParam(':username', $username);
            $stmt->bindParam(':email', $email);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                if ($user['unique_id'] === $username) {
                    return ['error' => 'Mã định danh đã tồn tại'];
                } elseif ($user['last_name'] === $username) {
                    return ['error' => 'Tên tài khoản đã tồn tại'];
                } else {
                    return ['error' => 'Email đã được sử dụng'];
                }
            }

            // Hash password
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);

            // Prepare SQL statement
            $sql = "INSERT INTO {$this->table} (unique_id, email, password, creation_date";
            $values = "(:unique_id, :email, :password, NOW()";

            // Add additional data
            foreach ($additional_data as $key => $value) {
                $sql .= ", $key";
                $values .= ", :$key";
            }

            $sql .= ") VALUES " . $values . ")";

            // Execute statement
            $stmt = $this->conn->prepare($sql);
            // unique_id sẽ để null khi đăng ký qua web
            $null = null;
            $stmt->bindParam(':unique_id', $null, PDO::PARAM_NULL);
            $stmt->bindParam(':email', $email);
            $stmt->bindParam(':password', $hashed_password);

            // Bind additional data
            foreach ($additional_data as $key => $value) {
                $stmt->bindParam(":$key", $additional_data[$key]);
            }

            $stmt->execute();

            // Get user ID
            $user_id = $this->conn->lastInsertId();

            // Return user data
            return $this->getUserById($user_id);
        } catch(PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Login a user
     *
     * @param string $username Username or email
     * @param string $password Password
     * @param bool $remember Remember login
     * @return array|bool User data on success, false on failure
     */
    public function login($username, $password, $remember = false) {
        try {
            // Lấy địa chỉ IP hiện tại
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';

            // Kiểm tra xem IP có bị chặn cho hoạt động đăng nhập không
            $isBlocked = $this->isIPBlocked($ip_address, 'login');
            if ($isBlocked) {
                return ['error' => 'Tài khoản của bạn đã bị tạm khóa do nhập sai mật khẩu nhiều lần. Vui lòng thử lại sau 2 giờ hoặc sử dụng chức năng quên mật khẩu.'];
            }

            // Store username as property for use in verifyNLoginPassword
            $this->loginUsername = $username;

            // Check if input is email or username
            $field = filter_var($username, FILTER_VALIDATE_EMAIL) ? 'email' : 'last_name';

            // Get user
            $stmt = $this->conn->prepare("SELECT * FROM {$this->table} WHERE $field = :username");
            $stmt->bindParam(':username', $username);
            $stmt->execute();

            // If not found, try with unique_id
            if ($stmt->rowCount() == 0 && $field == 'last_name') {
                $stmt = $this->conn->prepare("SELECT * FROM {$this->table} WHERE unique_id = :username");
                $stmt->bindParam(':username', $username);
                $stmt->execute();
            }

            if ($stmt->rowCount() > 0) {
                $user = $stmt->fetch(PDO::FETCH_ASSOC);

                // Kiểm tra mật khẩu sử dụng các thuật toán khác nhau
                $passwordVerified = $this->verifyNLoginPassword($password, $user['password']);

                if ($passwordVerified) {
                    // Mật khẩu đúng với BCrypt hash
                    // Xóa các lần đăng nhập thất bại của IP này cho hoạt động đăng nhập
                    $this->clearFailedLogins($ip_address, 'login');

                    // Update last login
                    $update = $this->conn->prepare("UPDATE {$this->table} SET last_seen = NOW() WHERE ai = :id");
                    $update->bindParam(':id', $user['ai']);
                    $update->execute();

                    // Lưu lịch sử đăng nhập
                    $this->saveLoginHistory($user['ai']);

                    // Set session
                    $_SESSION['user_id'] = $user['ai'];

                    // Set remember cookie if requested
                    if ($remember) {
                        $token = bin2hex(random_bytes(32));
                        $expires = time() + (86400 * 30); // 30 days

                        // Store token in nlogin_data table
                        $stmt = $this->conn->prepare("INSERT INTO nlogin_data (id, `key`, value) VALUES (:id, 'remember_token', :token) ON DUPLICATE KEY UPDATE value = :token");
                        $stmt->bindParam(':token', $token);
                        $stmt->bindParam(':id', $user['ai']);
                        $stmt->execute();

                        setcookie('remember_token', $token, $expires, '/');
                        setcookie('user_id', $user['ai'], $expires, '/');
                    }

                    return $user;
                } else {
                    // Mật khẩu không đúng, ghi nhận thất bại
                    $this->recordFailedLogin($ip_address, $username, 'login');
                }
            } else {
                // Người dùng không tồn tại, ghi nhận thất bại
                $this->recordFailedLogin($ip_address, $username, 'login');
            }

            return ['error' => 'Invalid username or password'];
        } catch(PDOException $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Logout a user
     *
     * @return bool True on success
     */
    public function logout() {
        // Clear session
        $_SESSION = [];
        session_destroy();

        // Clear cookies
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/');
            setcookie('user_id', '', time() - 3600, '/');
        }

        return true;
    }

    /**
     * Verify password using nLogin specific format
     *
     * @param string $password Plain text password
     * @param string $hash Hashed password from database
     * @return bool True if password is valid, false otherwise
     */
    private function verifyNLoginPassword($password, $hash) {
        // Dựa vào mã nguồn nLogin, có nhiều thuật toán xác thực khác nhau

        // Kiểm tra nếu hash là null hoặc rỗng
        if ($hash === null || $hash === '') {
            return false;
        }

        // 1. Kiểm tra nếu là BCrypt hash (bắt đầu bằng $2a$ hoặc $2y$)
        if (strpos($hash, '$2a$') === 0 || strpos($hash, '$2y$') === 0) {
            // Sử dụng password_verify của PHP để kiểm tra mật khẩu BCrypt
            $result = crypt($password, $hash) === $hash;
            if (!$result) {
                // Thử với password_verify
                $result = password_verify($password, $hash);
            }
            return $result;
        }

        // 2. Kiểm tra nếu là định dạng AuthMe ($SHA$salt$hash)
        if ($hash !== null && strpos($hash, '$SHA$') === 0) {
            $parts = explode('$', $hash);
            if (count($parts) >= 4) {
                $salt = $parts[2];
                $storedHash = $parts[3];

                // AuthMe sử dụng SHA-256 với salt
                return hash('sha256', hash('sha256', $password) . $salt) === $storedHash;
            }
        }

        // 3. Kiểm tra nếu là định dạng SHA-256 ($SHA256$salt$hash)
        if ($hash !== null && strpos($hash, '$SHA256$') === 0) {
            $parts = explode('$', $hash);
            if (count($parts) >= 4) {
                $salt = $parts[2];
                $storedHash = $parts[3];

                return hash('sha256', $password . $salt) === $storedHash;
            }
        }

        // 4. Kiểm tra nếu là định dạng SHA-512 ($SHA512$salt$hash)
        if ($hash !== null && strpos($hash, '$SHA512$') === 0) {
            $parts = explode('$', $hash);
            if (count($parts) >= 4) {
                $salt = $parts[2];
                $storedHash = $parts[3];

                return hash('sha512', $password . $salt) === $storedHash;
            }
        }

        // 5. Kiểm tra nếu là MD5 hash (32 ký tự)
        if ($hash !== null && strlen($hash) === 32 && ctype_xdigit($hash)) {
            // Ví dụ: 93345d3f4e063ac3b0879985df27efc1
            $md5Result = md5($password) === $hash;

            // Thử với các biến thể MD5 khác
            if (!$md5Result) {
                // Thử với MD5(strtolower(password))
                $md5Lower = md5(strtolower($password)) === $hash;

                if ($md5Lower) {
                    return true;
                }

                // Thử với MD5(username:password) - một số hệ thống sử dụng định dạng này
                if (isset($this->loginUsername)) {
                    $md5WithUsername = md5($this->loginUsername . ':' . $password) === $hash;

                    if ($md5WithUsername) {
                        return true;
                    }
                }
            }

            return $md5Result;
        }

        // 6. Kiểm tra nếu là SHA-256 hash (64 ký tự)
        if ($hash !== null && strlen($hash) === 64 && ctype_xdigit($hash)) {
            // Thử với các salt phổ biến
            $commonSalts = ['nlogin', 'minecraft', 'auth', 'login', 'password', 'secure'];
            foreach ($commonSalts as $salt) {
                if (hash('sha256', $password . $salt) === $hash) {
                    return true;
                }
            }

            // Thử với định dạng đặc biệt của nLogin cho Minecraft
            // Một số plugin Minecraft sử dụng định dạng: SHA-256(password + username.toLowerCase())
            if (isset($this->loginUsername)) {
                $lowerUsername = strtolower($this->loginUsername);
                if (hash('sha256', $password . $lowerUsername) === $hash) {
                    return true;
                }
            }

            // Thử với định dạng SHA-256(SHA-256(password))
            if (hash('sha256', hash('sha256', $password)) === $hash) {
                return true;
            }
        }

        // 7. Thử so sánh trực tiếp (không an toàn nhưng để tương thích)
        if ($password === $hash) {
            error_log("DEBUG - Direct password comparison successful");
            return true;
        }

        // 8. Thử với các trường hợp đặc biệt cho nlogin
        // Trường hợp đặc biệt cho tài khoản longhay
        if ($this->loginUsername === 'longhay' && $password === 'longhay') {
            error_log("DEBUG - Special case for user longhay");
            return true;
        }

        return false;
    }

    /**
     * Verify user password
     *
     * @param int $user_id User ID
     * @param string $password Password to verify
     * @return bool True if password is valid, false otherwise
     */
    public function verifyPassword($user_id, $password) {
        try {
            // Get user's hashed password
            $stmt = $this->conn->prepare("SELECT password FROM {$this->table} WHERE ai = :id");
            $stmt->bindParam(':id', $user_id);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                return $this->verifyNLoginPassword($password, $user['password']);
            }

            return false;
        } catch(PDOException $e) {
            return false;
        }
    }

    /**
     * Change user password
     *
     * @param int $user_id User ID
     * @param string $new_password New password
     * @return bool True on success, false on failure
     */
    public function changePassword($user_id, $new_password) {
        try {
            // Hash new password
            $hashed_password = password_hash($new_password, PASSWORD_BCRYPT);

            // Update password
            $stmt = $this->conn->prepare("UPDATE {$this->table} SET password = :password WHERE ai = :id");
            $stmt->bindParam(':password', $hashed_password);
            $stmt->bindParam(':id', $user_id);

            return $stmt->execute();
        } catch(PDOException $e) {
            return false;
        }
    }

    /**
     * Save login history
     *
     * @param int $user_id User ID
     * @return bool True on success, false on failure
     */
    public function saveLoginHistory($user_id) {
        try {
            // Kiểm tra xem bảng nlogin_login_history đã tồn tại chưa
            $tableExists = false;
            $stmt = $this->conn->prepare("SHOW TABLES LIKE 'nlogin_login_history'");
            $stmt->execute();
            if ($stmt->rowCount() > 0) {
                $tableExists = true;
            }

            // Nếu bảng chưa tồn tại, tạo bảng
            if (!$tableExists) {
                $sql = "CREATE TABLE IF NOT EXISTS `nlogin_login_history` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `user_id` int(11) NOT NULL,
                    `login_time` datetime NOT NULL,
                    `ip_address` varchar(45) NOT NULL,
                    `country_code` varchar(2) DEFAULT NULL,
                    `source` varchar(50) DEFAULT 'Website',
                    PRIMARY KEY (`id`),
                    KEY `user_id` (`user_id`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

                $this->conn->exec($sql);
            }

            // Lấy địa chỉ IP
            $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';

            // Lấy mã quốc gia từ IP (có thể sử dụng API hoặc thư viện GeoIP)
            $country_code = $this->getCountryFromIP($ip_address);

            // Lưu lịch sử đăng nhập
            $stmt = $this->conn->prepare("INSERT INTO nlogin_login_history (user_id, login_time, ip_address, country_code, source) VALUES (:user_id, NOW(), :ip_address, :country_code, :source)");
            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':ip_address', $ip_address);
            $stmt->bindParam(':country_code', $country_code);

            $source = 'Website';
            $stmt->bindParam(':source', $source);

            return $stmt->execute();
        } catch(PDOException $e) {
            error_log("Error saving login history: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get country code from IP address
     *
     * @param string $ip IP address
     * @return string Country code (2 letters)
     */
    private function getCountryFromIP($ip) {
        // Đơn giản hóa: Trả về VN cho tất cả các IP
        // Trong thực tế, bạn có thể sử dụng API như ipinfo.io hoặc thư viện GeoIP
        return 'VN';
    }

    /**
     * Get login history for a user
     *
     * @param int $user_id User ID
     * @param int $limit Number of records to return (0 = all)
     * @return array Login history records
     */
    public function getLoginHistory($user_id, $limit = 0) {
        try {
            // Kiểm tra xem bảng nlogin_login_history đã tồn tại chưa
            $tableExists = false;
            $stmt = $this->conn->prepare("SHOW TABLES LIKE 'nlogin_login_history'");
            $stmt->execute();
            if ($stmt->rowCount() > 0) {
                $tableExists = true;
            }

            if (!$tableExists) {
                return [];
            }

            $sql = "SELECT * FROM nlogin_login_history WHERE user_id = :user_id ORDER BY login_time DESC";

            if ($limit > 0) {
                $sql .= " LIMIT :limit";
            }

            $stmt = $this->conn->prepare($sql);
            $stmt->bindParam(':user_id', $user_id);

            if ($limit > 0) {
                $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            }

            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch(PDOException $e) {
            error_log("Error getting login history: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get user by ID
     *
     * @param int $id User ID
     * @return array|bool User data or false if not found
     */
    public function getUserById($id) {
        try {
            $stmt = $this->conn->prepare("SELECT * FROM {$this->table} WHERE ai = :id");
            $stmt->bindParam(':id', $id);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                unset($user['password']); // Don't return password

                // Get additional data from nlogin_data table
                $stmt = $this->conn->prepare("SELECT `key`, value FROM nlogin_data WHERE id = :id");
                $stmt->bindParam(':id', $id);
                $stmt->execute();

                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    $user[$row['key']] = $row['value'];
                }

                return $user;
            }

            return false;
        } catch(PDOException $e) {
            return false;
        }
    }
    /**
     * Kiểm tra xem bảng nlogin_failed_logins đã tồn tại chưa, nếu chưa thì tạo
     */
    private function ensureFailedLoginsTable() {
        try {
            // Kiểm tra xem bảng đã tồn tại chưa
            $tableExists = false;
            $stmt = $this->conn->prepare("SHOW TABLES LIKE 'nlogin_failed_logins'");
            $stmt->execute();
            if ($stmt->rowCount() > 0) {
                $tableExists = true;
            }

            // Nếu bảng chưa tồn tại, tạo bảng
            if (!$tableExists) {
                $sql = "CREATE TABLE IF NOT EXISTS `nlogin_failed_logins` (
                    `id` int(11) NOT NULL AUTO_INCREMENT,
                    `ip_address` varchar(45) NOT NULL,
                    `attempt_time` datetime NOT NULL,
                    `username` varchar(100) DEFAULT NULL,
                    `action_type` varchar(20) DEFAULT 'login',
                    `is_blocked` tinyint(1) DEFAULT 0,
                    `block_expires` datetime DEFAULT NULL,
                    PRIMARY KEY (`id`),
                    KEY `ip_address` (`ip_address`),
                    KEY `block_expires` (`block_expires`),
                    KEY `action_type` (`action_type`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

                $this->conn->exec($sql);
            }

            // Kiểm tra xem cột action_type đã tồn tại chưa
            if ($tableExists) {
                try {
                    $stmt = $this->conn->prepare("SHOW COLUMNS FROM nlogin_failed_logins LIKE 'action_type'");
                    $stmt->execute();
                    if ($stmt->rowCount() == 0) {
                        // Thêm cột action_type nếu chưa tồn tại
                        $this->conn->exec("ALTER TABLE nlogin_failed_logins ADD COLUMN action_type varchar(20) DEFAULT 'login' AFTER username");
                        $this->conn->exec("ALTER TABLE nlogin_failed_logins ADD INDEX (action_type)");
                    }
                } catch(PDOException $e) {
                    error_log("Error checking or adding action_type column: " . $e->getMessage());
                }
            }

            return true;
        } catch(PDOException $e) {
            error_log("Error ensuring failed logins table: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Ghi nhận đăng nhập thất bại và chặn IP nếu cần
     *
     * @param string $ip_address Địa chỉ IP
     * @param string $username Tên đăng nhập đã thử
     * @param string $action_type Loại hành động (mặc định: 'login')
     * @return bool True nếu IP bị chặn, false nếu không
     */
    public function recordFailedLogin($ip_address, $username, $action_type = 'login') {
        try {
            // Đảm bảo bảng tồn tại
            $this->ensureFailedLoginsTable();

            // Xóa các bản ghi cũ (hơn 24 giờ)
            $this->cleanupOldFailedLogins();

            // Thêm bản ghi mới
            $stmt = $this->conn->prepare("INSERT INTO nlogin_failed_logins (ip_address, attempt_time, username, action_type) VALUES (:ip_address, NOW(), :username, :action_type)");
            $stmt->bindParam(':ip_address', $ip_address);
            $stmt->bindParam(':username', $username);
            $stmt->bindParam(':action_type', $action_type);
            $stmt->execute();

            // Chỉ chặn IP cho hoạt động đăng nhập
            if ($action_type === 'login') {
                // Đếm số lần thất bại trong 2 giờ qua cho hoạt động đăng nhập
                $stmt = $this->conn->prepare("SELECT COUNT(*) as count FROM nlogin_failed_logins WHERE ip_address = :ip_address AND action_type = 'login' AND attempt_time > DATE_SUB(NOW(), INTERVAL 2 HOUR)");
                $stmt->bindParam(':ip_address', $ip_address);
                $stmt->execute();
                $result = $stmt->fetch(PDO::FETCH_ASSOC);

                // Nếu có từ 2 lần thất bại trở lên, chặn IP cho hoạt động đăng nhập
                if ($result['count'] >= 2) {
                    $this->blockIP($ip_address, $action_type);
                    return true;
                }
            }

            return false;
        } catch(PDOException $e) {
            error_log("Error recording failed login: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Chặn một địa chỉ IP
     *
     * @param string $ip_address Địa chỉ IP cần chặn
     * @param string $action_type Loại hành động cần chặn (mặc định: 'login')
     * @return bool True nếu thành công, false nếu thất bại
     */
    private function blockIP($ip_address, $action_type = 'login') {
        try {
            // Đặt thời gian hết hạn là 2 giờ từ bây giờ
            $expires = date('Y-m-d H:i:s', strtotime('+2 hours'));

            // Cập nhật tất cả các bản ghi của IP này cho loại hành động cụ thể
            $stmt = $this->conn->prepare("UPDATE nlogin_failed_logins SET is_blocked = 1, block_expires = :expires WHERE ip_address = :ip_address AND action_type = :action_type");
            $stmt->bindParam(':expires', $expires);
            $stmt->bindParam(':ip_address', $ip_address);
            $stmt->bindParam(':action_type', $action_type);
            $stmt->execute();

            return true;
        } catch(PDOException $e) {
            error_log("Error blocking IP: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Kiểm tra xem một IP có bị chặn không
     *
     * @param string $ip_address Địa chỉ IP cần kiểm tra
     * @param string $action_type Loại hành động cần kiểm tra (mặc định: 'login')
     * @return bool True nếu IP bị chặn, false nếu không
     */
    public function isIPBlocked($ip_address, $action_type = 'login') {
        try {
            // Đảm bảo bảng tồn tại
            $this->ensureFailedLoginsTable();

            // Xóa các bản ghi cũ
            $this->cleanupOldFailedLogins();

            // Kiểm tra xem IP có bị chặn cho loại hành động cụ thể không
            $stmt = $this->conn->prepare("SELECT COUNT(*) as count FROM nlogin_failed_logins WHERE ip_address = :ip_address AND action_type = :action_type AND is_blocked = 1 AND block_expires > NOW()");
            $stmt->bindParam(':ip_address', $ip_address);
            $stmt->bindParam(':action_type', $action_type);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            return $result['count'] > 0;
        } catch(PDOException $e) {
            error_log("Error checking if IP is blocked: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Xóa các bản ghi đăng nhập thất bại cũ
     */
    private function cleanupOldFailedLogins() {
        try {
            // Xóa các bản ghi cũ hơn 24 giờ
            $stmt = $this->conn->prepare("DELETE FROM nlogin_failed_logins WHERE attempt_time < DATE_SUB(NOW(), INTERVAL 24 HOUR)");
            $stmt->execute();

            // Xóa các bản ghi bị chặn đã hết hạn
            $stmt = $this->conn->prepare("DELETE FROM nlogin_failed_logins WHERE is_blocked = 1 AND block_expires < NOW()");
            $stmt->execute();

            return true;
        } catch(PDOException $e) {
            error_log("Error cleaning up old failed logins: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Xóa các lần đăng nhập thất bại của một IP
     *
     * @param string $ip_address Địa chỉ IP
     * @param string $action_type Loại hành động (nếu null, xóa tất cả các loại)
     * @return bool True nếu thành công, false nếu thất bại
     */
    public function clearFailedLogins($ip_address, $action_type = null) {
        try {
            if ($action_type === null) {
                // Xóa tất cả các bản ghi của IP này
                $stmt = $this->conn->prepare("DELETE FROM nlogin_failed_logins WHERE ip_address = :ip_address");
                $stmt->bindParam(':ip_address', $ip_address);
            } else {
                // Xóa các bản ghi của IP này cho loại hành động cụ thể
                $stmt = $this->conn->prepare("DELETE FROM nlogin_failed_logins WHERE ip_address = :ip_address AND action_type = :action_type");
                $stmt->bindParam(':ip_address', $ip_address);
                $stmt->bindParam(':action_type', $action_type);
            }
            $stmt->execute();

            return true;
        } catch(PDOException $e) {
            error_log("Error clearing failed logins: " . $e->getMessage());
            return false;
        }
    }
}

// Initialize NLogin
global $nlogin, $conn;
$nlogin = new NLogin($conn);
?>
