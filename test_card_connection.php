<?php
// Script kiểm tra kết nối database lịch sử nạp thẻ
require_once 'config/config.php';

echo "<h2>Kiểm tra kết nối database lịch sử nạp thẻ</h2>";

// Kiểm tra kết nối database chính
echo "<h3>1. Database chính (nlogin):</h3>";
if ($conn) {
    echo "✅ Kết nối thành công!<br>";
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM nlogin");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "📊 Số lượng user: " . $result['count'] . "<br>";
    } catch(PDOException $e) {
        echo "❌ Lỗi truy vấn: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ Không thể kết nối!<br>";
}

echo "<br>";

// Kiểm tra kết nối database lịch sử nạp thẻ
echo "<h3>2. Database lịch sử nạp thẻ (dotmanearth):</h3>";
if ($card_conn) {
    echo "✅ Kết nối thành công!<br>";
    
    try {
        // Kiểm tra bảng dotman_napthe_log
        $stmt = $card_conn->query("SHOW TABLES LIKE 'dotman_napthe_log'");
        if ($stmt->rowCount() > 0) {
            echo "✅ Bảng dotman_napthe_log tồn tại<br>";
            
            $stmt = $card_conn->query("SELECT COUNT(*) as count FROM dotman_napthe_log");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "📊 Số lượng giao dịch: " . $result['count'] . "<br>";
            
            // Hiển thị 5 giao dịch mới nhất
            $stmt = $card_conn->query("SELECT * FROM dotman_napthe_log ORDER BY time DESC LIMIT 5");
            $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($transactions)) {
                echo "<h4>5 giao dịch mới nhất:</h4>";
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr><th>UUID</th><th>Loại thẻ</th><th>Mệnh giá</th><th>Thời gian</th><th>Trạng thái</th></tr>";
                
                foreach ($transactions as $tx) {
                    $time = date('d/m/Y H:i:s', $tx['time'] / 1000);
                    $status = $tx['success'] == 1 ? 'Thành công' : ($tx['waiting'] == 1 ? 'Đang xử lý' : 'Thất bại');
                    $price = number_format($tx['price'], 0, ',', '.') . ' VNĐ';
                    
                    echo "<tr>";
                    echo "<td>" . substr($tx['uuid'], 0, 8) . "...</td>";
                    echo "<td>" . $tx['type'] . "</td>";
                    echo "<td>" . $price . "</td>";
                    echo "<td>" . $time . "</td>";
                    echo "<td>" . $status . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        } else {
            echo "❌ Bảng dotman_napthe_log không tồn tại<br>";
        }
        
        // Kiểm tra bảng dotman_player_info
        $stmt = $card_conn->query("SHOW TABLES LIKE 'dotman_player_info'");
        if ($stmt->rowCount() > 0) {
            echo "✅ Bảng dotman_player_info tồn tại<br>";
            
            $stmt = $card_conn->query("SELECT COUNT(*) as count FROM dotman_player_info");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "📊 Số lượng player: " . $result['count'] . "<br>";
            
            // Hiển thị 5 player mới nhất
            $stmt = $card_conn->query("SELECT * FROM dotman_player_info ORDER BY last_updated DESC LIMIT 5");
            $players = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($players)) {
                echo "<h4>5 player mới nhất:</h4>";
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr><th>UUID</th><th>Name</th><th>Last Updated</th></tr>";
                
                foreach ($players as $player) {
                    $time = date('d/m/Y H:i:s', $player['last_updated'] / 1000);
                    
                    echo "<tr>";
                    echo "<td>" . substr($player['uuid'], 0, 8) . "...</td>";
                    echo "<td>" . $player['name'] . "</td>";
                    echo "<td>" . $time . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        } else {
            echo "❌ Bảng dotman_player_info không tồn tại<br>";
        }
        
    } catch(PDOException $e) {
        echo "❌ Lỗi truy vấn: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ Không thể kết nối!<br>";
    echo "Thông tin kết nối:<br>";
    echo "Host: " . CARD_DB_HOST . "<br>";
    echo "User: " . CARD_DB_USER . "<br>";
    echo "Database: " . CARD_DB_NAME . "<br>";
}

echo "<br>";

// Test hàm getUserUUID
echo "<h3>3. Test hàm getUserUUID:</h3>";
require_once 'includes/functions.php';

// Giả sử user_id = 1 (thay đổi theo user thực tế)
$test_user_id = 1;
$uuid = getUserUUID($test_user_id);

if ($uuid) {
    echo "✅ UUID tìm thấy: " . $uuid . "<br>";
    
    // Test lấy lịch sử nạp thẻ
    $history = getCardRechargeHistory($uuid, 5, 0);
    $count = getCardRechargeHistoryCount($uuid);
    
    echo "📊 Tổng số giao dịch của user: " . $count . "<br>";
    
    if (!empty($history)) {
        echo "<h4>Lịch sử nạp thẻ của user:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Thời gian</th><th>Loại thẻ</th><th>Mệnh giá</th><th>Trạng thái</th></tr>";
        
        foreach ($history as $record) {
            echo "<tr>";
            echo "<td>" . $record['formatted_time'] . "</td>";
            echo "<td>" . $record['type_name'] . "</td>";
            echo "<td>" . $record['formatted_price'] . "</td>";
            echo "<td>" . $record['status_text'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "ℹ️ User chưa có giao dịch nạp thẻ nào<br>";
    }
} else {
    echo "❌ Không tìm thấy UUID cho user_id: " . $test_user_id . "<br>";
}

echo "<br><hr>";
echo "<p><strong>Lưu ý:</strong> Sau khi kiểm tra xong, hãy xóa file này để bảo mật!</p>";
?>
