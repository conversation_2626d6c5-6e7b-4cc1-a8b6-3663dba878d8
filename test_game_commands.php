<?php
// Test script để thử các lệnh game khác nhau
echo "<h2>Test Game Commands</h2>";

$base_url = 'https://panel.gamehosting.vn';
$api_key = 'ptlc_DzHfXQUGVnsUhaA9cz6M27J47hRIEyAGo9t7YYMl0Gs';
$server_id = '8661ab4b';

// Các lệnh để test
$commands_to_test = [
    'Basic Commands' => [
        'list' => 'Liệt kê players online',
        'help' => 'Hiển thị help',
        'version' => 'Hiển thị version server',
        'plugins' => 'Liệt kê plugins',
        'pl' => 'Liệt kê plugins (short)'
    ],
    'Chat Commands' => [
        'say Hello from API!' => 'Gửi message public',
        'tell longhay Hello!' => 'Gửi message private (nếu online)',
        'broadcast API Test Message' => 'Broadcast message'
    ],
    'Plugin Commands' => [
        'dotman help' => 'Help plugin dotman',
        'dotman version' => 'Version plugin dotman',
        'dotman napthucong longhay 1000 -f' => 'Lệnh nạp tiền thật'
    ],
    'Alternative Commands' => [
        'eco give longhay 1000' => 'EssentialsX economy',
        'money give longhay 1000' => 'Alternative money plugin',
        'vault give longhay 1000' => 'Vault economy'
    ]
];

function sendCommand($endpoint, $api_key, $command) {
    $data = json_encode(['command' => $command]);
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $endpoint,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $data,
        CURLOPT_HTTPHEADER => [
            'Accept: application/json',
            'Content-Type: application/json',
            'Authorization: Bearer ' . $api_key
        ],
        CURLOPT_TIMEOUT => 10
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'success' => ($http_code === 204),
        'http_code' => $http_code,
        'error' => $error,
        'response' => $response
    ];
}

$endpoint = "$base_url/api/client/servers/$server_id/command";

foreach ($commands_to_test as $category => $commands) {
    echo "<h3>$category</h3>";
    
    foreach ($commands as $command => $description) {
        echo "<h4>Test: $command</h4>";
        echo "<p><strong>Mô tả:</strong> $description</p>";
        
        $result = sendCommand($endpoint, $api_key, $command);
        
        echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        
        if ($result['error']) {
            echo "❌ <strong>cURL Error:</strong> " . $result['error'] . "<br>";
        } else {
            echo "📋 <strong>HTTP Code:</strong> " . $result['http_code'] . "<br>";
            
            if ($result['success']) {
                echo "✅ <strong>Status:</strong> Command sent successfully!<br>";
                echo "🎮 <strong>Action:</strong> Kiểm tra console/chat trong game để xem kết quả<br>";
            } else {
                echo "❌ <strong>Status:</strong> Failed<br>";
                if (!empty($result['response'])) {
                    echo "<strong>Response:</strong> " . htmlspecialchars($result['response']) . "<br>";
                }
            }
        }
        
        echo "</div>";
        
        // Delay giữa các lệnh để tránh spam
        if ($result['success']) {
            echo "<p><em>Chờ 2 giây trước lệnh tiếp theo...</em></p>";
            sleep(2);
        }
    }
}

echo "<hr>";
echo "<h3>Hướng dẫn kiểm tra:</h3>";
echo "<ol>";
echo "<li><strong>Vào game console</strong> hoặc <strong>chat</strong> để xem kết quả</li>";
echo "<li><strong>Lệnh 'list'</strong> sẽ hiển thị players online</li>";
echo "<li><strong>Lệnh 'say'</strong> sẽ hiển thị message trong chat</li>";
echo "<li><strong>Lệnh 'plugins'</strong> sẽ hiển thị danh sách plugins</li>";
echo "<li><strong>Nếu không thấy gì</strong> = Server không nhận lệnh hoặc console không hiển thị</li>";
echo "</ol>";

echo "<h3>Debug steps:</h3>";
echo "<ul>";
echo "<li>Nếu <strong>'list'</strong> không hoạt động = Vấn đề cơ bản với command system</li>";
echo "<li>Nếu <strong>'say'</strong> không hoạt động = Server không process commands</li>";
echo "<li>Nếu <strong>'plugins'</strong> không hiển thị dotman = Plugin chưa load</li>";
echo "<li>Nếu <strong>'dotman help'</strong> không hoạt động = Plugin có vấn đề</li>";
echo "</ul>";

echo "<p><strong>Sau khi test, kiểm tra game và báo cáo kết quả!</strong></p>";
echo "<p><strong>Xóa file này sau khi test xong!</strong></p>";
?>

<style>
div {
    margin: 10px 0;
}

h4 {
    color: #333;
    margin-top: 20px;
}

code {
    background: #e9ecef;
    padding: 2px 5px;
    border-radius: 3px;
    font-family: monospace;
}
</style>
