<?php
// <PERSON>ript để cập nhật status codes
require_once 'config/config.php';

echo "<h2>Update Status Codes</h2>";

try {
    // 1. Thêm cột status nếu chưa có
    echo "<h3>1. Adding status column:</h3>";
    
    try {
        $conn->exec("ALTER TABLE nlogin_qr_pending ADD COLUMN status TINYINT DEFAULT 0 COMMENT '0=pending, 1=success, 3=cancelled'");
        echo "✅ Added status column<br>";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "ℹ️ Status column already exists<br>";
        } else {
            echo "❌ Error adding status column: " . $e->getMessage() . "<br>";
        }
    }
    
    // 2. Cập nhật status dựa trên processed
    echo "<h3>2. Updating status based on processed:</h3>";
    
    // Pending: processed = 0 → status = 0
    $stmt = $conn->prepare("UPDATE nlogin_qr_pending SET status = 0 WHERE processed = 0");
    $stmt->execute();
    $pending_count = $stmt->rowCount();
    
    // Cancelled: processed = 1 và không có trong completed transactions → status = 3
    $stmt = $conn->prepare("
        UPDATE nlogin_qr_pending p
        SET status = 3 
        WHERE processed = 1 
        AND NOT EXISTS (
            SELECT 1 FROM nlogin_qr_transactions t 
            WHERE t.transaction_code = p.transaction_code
        )
    ");
    $stmt->execute();
    $cancelled_count = $stmt->rowCount();
    
    // Success: processed = 1 và có trong completed transactions → status = 1
    $stmt = $conn->prepare("
        UPDATE nlogin_qr_pending p
        SET status = 1 
        WHERE processed = 1 
        AND EXISTS (
            SELECT 1 FROM nlogin_qr_transactions t 
            WHERE t.transaction_code = p.transaction_code
        )
    ");
    $stmt->execute();
    $success_count = $stmt->rowCount();
    
    echo "✅ Updated $pending_count pending transactions (status = 0)<br>";
    echo "✅ Updated $cancelled_count cancelled transactions (status = 3)<br>";
    echo "✅ Updated $success_count successful transactions (status = 1)<br>";
    
    // 3. Hiển thị thống kê
    echo "<h3>3. Current Statistics:</h3>";
    
    $stmt = $conn->query("
        SELECT 
            status,
            COUNT(*) as count,
            CASE 
                WHEN status = 0 THEN 'Pending'
                WHEN status = 1 THEN 'Success'
                WHEN status = 3 THEN 'Cancelled'
                ELSE 'Unknown'
            END as status_name
        FROM nlogin_qr_pending 
        GROUP BY status 
        ORDER BY status
    ");
    $stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>Status Code</th><th>Status Name</th><th>Count</th><th>Color</th>";
    echo "</tr>";
    
    foreach ($stats as $stat) {
        $color = '';
        switch ($stat['status']) {
            case 0: $color = '#ffc107'; break; // Yellow for pending
            case 1: $color = '#28a745'; break; // Green for success
            case 3: $color = '#dc3545'; break; // Red for cancelled
        }
        
        echo "<tr>";
        echo "<td>{$stat['status']}</td>";
        echo "<td>{$stat['status_name']}</td>";
        echo "<td>{$stat['count']}</td>";
        echo "<td style='background: $color; color: white; text-align: center;'>●</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 4. Hiển thị giao dịch gần đây
    echo "<h3>4. Recent Transactions:</h3>";
    
    $stmt = $conn->query("
        SELECT id, username, amount, server, transaction_code, created_at, processed, status,
               CASE 
                   WHEN status = 0 THEN 'Pending'
                   WHEN status = 1 THEN 'Success'
                   WHEN status = 3 THEN 'Cancelled'
                   ELSE 'Unknown'
               END as status_name
        FROM nlogin_qr_pending 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $recent = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($recent) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>User</th><th>Amount</th><th>Transaction Code</th><th>Created</th><th>Old Processed</th><th>New Status</th>";
        echo "</tr>";
        
        foreach ($recent as $tx) {
            $status_color = '';
            switch ($tx['status']) {
                case 0: $status_color = '#ffc107'; break;
                case 1: $status_color = '#28a745'; break;
                case 3: $status_color = '#dc3545'; break;
            }
            
            echo "<tr>";
            echo "<td>{$tx['id']}</td>";
            echo "<td>{$tx['username']}</td>";
            echo "<td>" . number_format($tx['amount']) . "</td>";
            echo "<td><code>{$tx['transaction_code']}</code></td>";
            echo "<td>{$tx['created_at']}</td>";
            echo "<td>{$tx['processed']}</td>";
            echo "<td style='background: $status_color; color: white; font-weight: bold;'>{$tx['status']} ({$tx['status_name']})</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 5. Tạo index cho status
    echo "<h3>5. Adding index for status:</h3>";
    
    try {
        $conn->exec("ALTER TABLE nlogin_qr_pending ADD INDEX idx_status (status)");
        echo "✅ Added index for status column<br>";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "ℹ️ Index already exists<br>";
        } else {
            echo "❌ Error adding index: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<hr>";
    echo "<h3>✅ Status codes updated successfully!</h3>";
    echo "<p><strong>New Status System:</strong></p>";
    echo "<ul>";
    echo "<li><span style='color: #ffc107;'>●</span> <strong>0 = Pending</strong> - Đang chờ thanh toán</li>";
    echo "<li><span style='color: #28a745;'>●</span> <strong>1 = Success</strong> - Thanh toán thành công</li>";
    echo "<li><span style='color: #dc3545;'>●</span> <strong>3 = Cancelled</strong> - Giao dịch bị hủy</li>";
    echo "</ul>";
    
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>Update application code to use status instead of processed</li>";
    echo "<li>Test the new status system</li>";
    echo "<li>Remove this file after completion</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage();
}
?>

<style>
table {
    border-collapse: collapse;
    margin: 10px 0;
    font-size: 12px;
}

table th, table td {
    border: 1px solid #ddd;
    padding: 6px;
    text-align: left;
}

table th {
    background-color: #f2f2f2;
}

code {
    background: #e9ecef;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

h3 {
    color: #333;
    margin-top: 20px;
}
</style>
