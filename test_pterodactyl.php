<?php
// Test script để kiểm tra Pterodactyl API
echo "<h2>Test Pterodactyl API</h2>";

// Cấu hình test
$pterodactyl_config = [
    'earth' => [
        'url' => 'https://panel.gamehosting.vn/api/client/servers/8661ab4b/command',
        'api_key' => 'ptlc_DzHfXQUGVnsUhaA9cz6M27J47hRIEyAGo9t7YYMl0Gs'
    ],
    'ultrapvp' => [
        'url' => 'https://panel.gamehosting.vn/api/client/servers/ec2c6a6f/command',
        'api_key' => 'ptlc_DzHfXQUGVnsUhaA9cz6M27J47hRIEyAGo9t7YYMl0Gs'
    ],
    'skyblock' => [
        'url' => 'https://panel.gamehosting.vn/api/client/servers/889ecbac/command',
        'api_key' => 'ptlc_DzHfXQUGVnsUhaA9cz6M27J47hRIEyAGo9t7YYMl0Gs'
    ]
];

$test_username = 'longhay';
$test_amount = 1000;

foreach ($pterodactyl_config as $server => $config) {
    echo "<h3>Test Server: $server</h3>";
    
    $command = "dotman napthucong $test_username $test_amount -f";
    echo "<p><strong>Command:</strong> <code>$command</code></p>";
    echo "<p><strong>URL:</strong> <code>{$config['url']}</code></p>";
    
    // Test kết nối
    $data = json_encode(['command' => $command]);
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $config['url'],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $data,
        CURLOPT_HTTPHEADER => [
            'Accept: application/json',
            'Content-Type: application/json',
            'Authorization: Bearer ' . $config['api_key']
        ],
        CURLOPT_TIMEOUT => 30,
        CURLOPT_VERBOSE => true
    ]);
    
    $start_time = microtime(true);
    $response = curl_exec($ch);
    $end_time = microtime(true);
    
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $info = curl_getinfo($ch);
    curl_close($ch);
    
    $response_time = round(($end_time - $start_time) * 1000, 2);
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    
    if ($error) {
        echo "❌ <strong>cURL Error:</strong> $error<br>";
    } else {
        echo "📊 <strong>Response Time:</strong> {$response_time}ms<br>";
        echo "📋 <strong>HTTP Code:</strong> $http_code<br>";
        
        if ($http_code === 204) {
            echo "✅ <strong>Status:</strong> Command sent successfully!<br>";
        } elseif ($http_code === 401) {
            echo "❌ <strong>Status:</strong> Unauthorized - API key invalid<br>";
        } elseif ($http_code === 404) {
            echo "❌ <strong>Status:</strong> Server not found<br>";
        } elseif ($http_code === 502) {
            echo "⚠️ <strong>Status:</strong> Server offline<br>";
        } else {
            echo "❌ <strong>Status:</strong> Error HTTP $http_code<br>";
        }
        
        if (!empty($response)) {
            echo "<strong>Response:</strong><br>";
            echo "<pre>" . htmlspecialchars($response) . "</pre>";
        }
    }
    
    echo "</div>";
}

echo "<hr>";
echo "<h3>Test với lệnh khác (không cộng tiền thật):</h3>";

// Test với lệnh list players
$test_command = "list";
$server = 'earth';
$config = $pterodactyl_config[$server];

echo "<p><strong>Test Command:</strong> <code>$test_command</code> (Server: $server)</p>";

$data = json_encode(['command' => $test_command]);

$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $config['url'],
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => $data,
    CURLOPT_HTTPHEADER => [
        'Accept: application/json',
        'Content-Type: application/json',
        'Authorization: Bearer ' . $config['api_key']
    ],
    CURLOPT_TIMEOUT => 30
]);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";

if ($error) {
    echo "❌ <strong>cURL Error:</strong> $error<br>";
} else {
    echo "📋 <strong>HTTP Code:</strong> $http_code<br>";
    
    if ($http_code === 204) {
        echo "✅ <strong>Status:</strong> Command sent successfully!<br>";
        echo "<p>Nếu lệnh 'list' thành công, có nghĩa là API hoạt động bình thường.</p>";
    } else {
        echo "❌ <strong>Status:</strong> Error HTTP $http_code<br>";
        if (!empty($response)) {
            echo "<strong>Response:</strong><br>";
            echo "<pre>" . htmlspecialchars($response) . "</pre>";
        }
    }
}

echo "</div>";

echo "<hr>";
echo "<h3>Hướng dẫn:</h3>";
echo "<ul>";
echo "<li><strong>HTTP 204:</strong> ✅ Thành công</li>";
echo "<li><strong>HTTP 401:</strong> ❌ API key không hợp lệ</li>";
echo "<li><strong>HTTP 404:</strong> ❌ Server ID không tồn tại</li>";
echo "<li><strong>HTTP 502:</strong> ⚠️ Server offline</li>";
echo "<li><strong>cURL Error:</strong> ❌ Lỗi kết nối mạng</li>";
echo "</ul>";

echo "<p><strong>Lưu ý:</strong> Nếu test thành công, lệnh sẽ được gửi đến server thật. Hãy kiểm tra trong game.</p>";
echo "<p><strong>Sau khi test xong, hãy xóa file này để bảo mật!</strong></p>";
?>

<style>
pre {
    background: #e9ecef;
    padding: 10px;
    border-radius: 3px;
    overflow-x: auto;
    max-height: 200px;
}

code {
    background: #e9ecef;
    padding: 2px 5px;
    border-radius: 3px;
    font-family: monospace;
}

div {
    margin: 10px 0;
}
</style>
