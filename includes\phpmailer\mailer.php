<?php
/**
 * PHPMailer Integration for cPanel Hosting
 * Using Composer autoload for better dependency management
 */

// Ensure proper path to autoloader
// Ki<PERSON><PERSON> tra các đường dẫn có thể có của autoload.php
$autoloadPaths = [
    '/www/wwwroot/id.dptmc.com/vendor/autoload.php', // Đường dẫn chính xác trên máy chủ
    __DIR__ . '/../../vendor/autoload.php',
    '/www/wwwroot/vendor/autoload.php',
    '/vendor/autoload.php'
];

// Ghi log đường dẫn hiện tại
$logFile = __DIR__ . '/mail_debug.log';
file_put_contents($logFile, "Current directory: " . __DIR__ . "\n", FILE_APPEND);
file_put_contents($logFile, "Checking autoload paths:\n", FILE_APPEND);

$autoloadFound = false;
foreach ($autoloadPaths as $path) {
    file_put_contents($logFile, "Checking: $path - " . (file_exists($path) ? "EXISTS" : "NOT FOUND") . "\n", FILE_APPEND);
    if (file_exists($path)) {
        require_once $path;
        $autoloadFound = true;
        file_put_contents($logFile, "Using autoload.php from: $path\n", FILE_APPEND);
        break;
    }
}

// Nếu không tìm thấy autoload.php, sử dụng các class PHPMailer trực tiếp
if (!$autoloadFound) {
    file_put_contents($logFile, "Autoload.php not found. Trying direct PHPMailer files.\n", FILE_APPEND);

    // Đường dẫn đến các file PHPMailer
    $phpmailerPaths = [
        '/www/wwwroot/id.dptmc.com/vendor/phpmailer/phpmailer/src/', // Đường dẫn chính xác trên máy chủ
        __DIR__ . '/../../vendor/phpmailer/phpmailer/src/',
        '/www/wwwroot/vendor/phpmailer/phpmailer/src/',
        '/vendor/phpmailer/phpmailer/src/'
    ];

    $phpmailerFound = false;
    foreach ($phpmailerPaths as $path) {
        file_put_contents($logFile, "Checking PHPMailer at: $path - " . (file_exists($path . 'PHPMailer.php') ? "EXISTS" : "NOT FOUND") . "\n", FILE_APPEND);
        if (file_exists($path . 'PHPMailer.php')) {
            require_once $path . 'PHPMailer.php';
            require_once $path . 'SMTP.php';
            require_once $path . 'Exception.php';
            $phpmailerFound = true;
            file_put_contents($logFile, "Using PHPMailer from: $path\n", FILE_APPEND);
            break;
        }
    }

    if (!$phpmailerFound) {
        file_put_contents($logFile, "ERROR: PHPMailer files not found in any location!\n", FILE_APPEND);
        // Sử dụng PHPMailer được đóng gói sẵn nếu có
        if (file_exists(__DIR__ . '/phpmailer-src/PHPMailer.php')) {
            file_put_contents($logFile, "Using bundled PHPMailer\n", FILE_APPEND);
            require_once __DIR__ . '/phpmailer-src/PHPMailer.php';
            require_once __DIR__ . '/phpmailer-src/SMTP.php';
            require_once __DIR__ . '/phpmailer-src/Exception.php';
        }
    }
}
require_once __DIR__ . '/config.php';

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use PHPMailer\PHPMailer\Exception;

// Set error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', DEV_MODE ? '1' : '0');

// Ghi log lỗi
function logMailError($message) {
    $logFile = __DIR__ . '/mail_error.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message\n";

    // Ghi log vào file
    file_put_contents($logFile, $logMessage, FILE_APPEND);

    // Ghi log vào error_log của PHP
    error_log("PHPMailer Error: $message");
}

class ExtendedPHPMailer extends PHPMailer {
    /**
     * @var bool
     */
    public $SMTPKeepAlive = true;

    /**
     * @var int
     */
    public $Timeout = 5; // Reduced timeout for faster response

    /**
     * @var string
     */
    public $XMailer = 'Microsoft';

    /**
     * @var bool
     */
    public $SMTPAutoTLS = false; // Disable auto TLS for faster connection

    /**
     * @var int
     */
    public $WordWrap = 0; // Disable word wrap for faster processing

    public function __construct($exceptions = true) {
        parent::__construct($exceptions);

        // Performance optimizations
        $this->Priority = 3; // Normal priority
        $this->CharSet = 'UTF-8';
        $this->Encoding = 'base64';
        $this->SMTPDebug = SMTP_DEBUG;
    }
}

function testSMTPConnection() {
    $mail = new ExtendedPHPMailer(true);
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host       = SMTP_HOST;
        $mail->SMTPAuth   = SMTP_AUTH;
        $mail->Username   = SMTP_USER;
        $mail->Password   = SMTP_PASS;
        $mail->Port       = SMTP_PORT;
        $mail->SMTPSecure = SMTP_SECURE;

        // Character encoding and timeout settings
        $mail->CharSet    = 'UTF-8';
        $mail->Encoding   = 'base64';

        // Setting a test recipient
        $mail->setFrom(EMAIL_FROM, EMAIL_FROM_NAME);
        $mail->addAddress(EMAIL_FROM); // Send to self for testing
        $mail->Subject = 'SMTP Connection Test';
        $mail->isHTML(true);
        $mail->Body = '<html><body>
            <h2>SMTP Connection Test</h2>
            <p>This is a test email to verify SMTP connection.</p>
            <p>Time: ' . date('Y-m-d H:i:s') . '</p>
        </body></html>';
        $mail->AltBody = 'This is a test email to verify SMTP connection. Time: ' . date('Y-m-d H:i:s');

        // Try to send a test email
        if ($mail->send()) {
            return true;
        }
    } catch (Exception $e) {
        $errorMsg = "SMTP Connection failed: " . $mail->ErrorInfo;
        logMailError($errorMsg);
        return false;
    }
    return false;
}

function sendEmail($to, $subject, $body, $altBody = '') {
    // Validate email address
    if (!filter_var($to, FILTER_VALIDATE_EMAIL)) {
        return [
            'status' => 0,
            'message' => 'Địa chỉ email không hợp lệ'
        ];
    }

    // Skip sending in development mode if configured
    if (defined('DEV_MODE') && DEV_MODE === true) {
        return [
            'status' => 1,
            'message' => 'Email đã được gửi thành công (DEV_MODE)'
        ];
    }

    // Use static variable to keep SMTP connection alive between calls
    static $mail = null;

    if ($mail === null) {
        $mail = new ExtendedPHPMailer(true);

        // Server settings - only configure once
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = SMTP_AUTH;
        $mail->Username = SMTP_USER;
        $mail->Password = SMTP_PASS;
        $mail->Port = SMTP_PORT;
        $mail->SMTPSecure = SMTP_SECURE;
        $mail->SMTPOptions = SMTP_OPTIONS;
    } else {
        // Clear all recipients and attachments for reuse
        $mail->clearAllRecipients();
        $mail->clearAttachments();
    }

    try {
        // Recipients
        $mail->setFrom(EMAIL_FROM, EMAIL_FROM_NAME);
        $mail->addAddress($to);
        $mail->addReplyTo(EMAIL_REPLY_TO, EMAIL_REPLY_TO_NAME);

        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $body;
        $mail->AltBody = $altBody ?: strip_tags($body);

        // Attempt to send
        $result = $mail->send();

        if ($result) {
            return [
                'status' => 1,
                'message' => 'Email đã được gửi thành công'
            ];
        } else {
            return [
                'status' => 0,
                'message' => 'Không thể gửi email: ' . $mail->ErrorInfo
            ];
        }
    } catch (Exception $e) {
        $errorMsg = $mail->ErrorInfo;

        // Ghi log lỗi chi tiết
        $logFile = __DIR__ . '/mail_error.log';
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[$timestamp] Error sending email to $to: " . $errorMsg . "\n";
        $logMessage .= "Subject: $subject\n";
        $logMessage .= "Exception: " . $e->getMessage() . "\n";
        $logMessage .= "Trace: " . $e->getTraceAsString() . "\n";
        $logMessage .= "-----------------------------------\n";
        file_put_contents($logFile, $logMessage, FILE_APPEND);

        // Ghi log vào error_log của PHP
        error_log("PHPMailer Error: Error sending email to $to: " . $errorMsg);

        // Reset the connection on error
        $mail = null;

        return [
            'status' => 0,
            'message' => 'Không thể gửi email. Lỗi: ' . $errorMsg
        ];
    }
}

function sendPasswordResetEmail($to, $username, $otp, $resetUrl = '') {
    $subject = '[DPTMC.COM] Mã xác minh đặt lại mật khẩu: ' . $otp;

    $body = '<!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Đặt lại mật khẩu</title>
        <style>
            @import url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap");
            body {
                font-family: "Roboto", Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                background-color: #f5f5f5;
                margin: 0;
                padding: 0;
            }
            .email-container {
                max-width: 600px;
                margin: 0 auto;
                background-color: #ffffff;
            }
            .email-header {
                background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
                color: white;
                padding: 30px 20px;
                text-align: center;
            }
            .email-header h1 {
                margin: 0;
                font-size: 28px;
                font-weight: 700;
            }
            .email-header p {
                margin: 10px 0 0;
                opacity: 0.9;
                font-weight: 300;
            }
            .email-body {
                padding: 40px 30px;
                background-color: #ffffff;
            }
            .greeting {
                font-size: 20px;
                margin-bottom: 20px;
                color: #2c3e50;
            }
            .message {
                margin-bottom: 30px;
                color: #555;
            }
            .otp-container {
                text-align: center;
                margin: 30px 0;
                padding: 20px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border-left: 4px solid #e74c3c;
            }
            .otp-code {
                font-size: 32px;
                color: #2c3e50;
                letter-spacing: 8px;
                font-weight: 700;
                margin: 10px 0;
            }
            .otp-label {
                font-size: 14px;
                color: #7f8c8d;
                margin-bottom: 10px;
                text-transform: uppercase;
                letter-spacing: 1px;
            }
            .otp-expires {
                font-size: 14px;
                color: #e74c3c;
                margin-top: 10px;
            }
            .button-container {
                text-align: center;
                margin: 30px 0;
            }
            .button {
                display: inline-block;
                padding: 12px 30px;
                background-color: #e74c3c;
                color: white !important;
                text-decoration: none;
                border-radius: 4px;
                font-weight: 500;
                font-size: 16px;
                text-align: center;
                transition: background-color 0.3s;
            }
            .button:hover {
                background-color: #c0392b;
            }
            .note {
                background-color: #fef9e7;
                padding: 15px;
                border-radius: 4px;
                margin-top: 30px;
                border-left: 4px solid #f1c40f;
                font-size: 14px;
                color: #7f8c8d;
            }
            .email-footer {
                background-color: #f8f9fa;
                padding: 20px;
                text-align: center;
                color: #7f8c8d;
                font-size: 13px;
                border-top: 1px solid #ecf0f1;
            }
            .social-links {
                margin: 15px 0;
            }
            .social-link {
                display: inline-block;
                margin: 0 10px;
                color: #e74c3c !important;
                text-decoration: none;
            }
            @media only screen and (max-width: 600px) {
                .email-body {
                    padding: 30px 20px;
                }
                .otp-code {
                    font-size: 28px;
                    letter-spacing: 6px;
                }
            }
        </style>
    </head>
    <body>
        <div class="email-container">
            <div class="email-header">
                <h1>DPTMC.COM</h1>
                <p>Đặt lại mật khẩu</p>
            </div>
            <div class="email-body">
                <div class="greeting">
                    Xin chào <strong>' . htmlspecialchars($username) . '</strong>,
                </div>

                <div class="message">
                    Chúng tôi nhận được yêu cầu đặt lại mật khẩu cho tài khoản của bạn. Vui lòng sử dụng mã xác minh dưới đây để tiếp tục quá trình đặt lại mật khẩu:
                </div>

                <div class="otp-container">
                    <div class="otp-label">Mã xác minh của bạn</div>
                    <div class="otp-code">' . htmlspecialchars($otp) . '</div>
                    <div class="otp-expires">Mã này sẽ hết hạn sau 30 phút</div>
                </div>

                <div class="message">
                    Nhập mã này vào trang xác minh để tiếp tục quá trình đặt lại mật khẩu cho tài khoản của bạn.
                </div>';

    if ($resetUrl) {
        $body .= '<div class="button-container">
                    <a href="' . htmlspecialchars($resetUrl) . '" class="button">ĐẶT LẠI MẬT KHẨU</a>
                  </div>';
    }

    $body .= '<div class="note">
                <strong>Lưu ý:</strong> Nếu bạn không thực hiện yêu cầu đặt lại mật khẩu này, vui lòng bỏ qua email này hoặc liên hệ với chúng tôi nếu bạn có bất kỳ câu hỏi nào.
              </div>
            </div>

            <div class="email-footer">
                <div>Email này được gửi tự động từ hệ thống DPTMC.COM, vui lòng không trả lời.</div>
                <div class="social-links">
                    <a href="https://id.dptmc.com" class="social-link">Website</a> |
                    <a href="https://discord.dptmc.com" class="social-link">Liên hệ</a> |
                    <a href="https://discord.dptmc.com" class="social-link">Trợ giúp</a>
                </div>
                <div>&copy; ' . date('Y') . ' DPTMC.COM - Mọi quyền được bảo lưu</div>
            </div>
        </div>
    </body>
    </html>';

    $altBody = "Xin chào $username,\n\nChúng tôi nhận được yêu cầu đặt lại mật khẩu cho tài khoản của bạn. Vui lòng sử dụng mã xác minh dưới đây để tiếp tục quá trình đặt lại mật khẩu:\n\n$otp\n\nMã xác minh này sẽ hết hạn sau 30 phút. Vui lòng không chia sẻ mã này với bất kỳ ai.\n\nNếu bạn không thực hiện yêu cầu này, vui lòng bỏ qua email này hoặc liên hệ với chúng tôi nếu bạn có bất kỳ câu hỏi nào.\n\nDPTMC.COM";

    return sendEmail($to, $subject, $body, $altBody);
}

function sendEmailVerification($to, $username) {
    $subject = 'Xác nhận địa chỉ email - DPTMC.COM';

    $body = '<!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Xác nhận Email</title>
        <style>
            @import url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap");
            body {
                font-family: "Roboto", Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                background-color: #f5f5f5;
                margin: 0;
                padding: 0;
            }
            .email-container {
                max-width: 600px;
                margin: 0 auto;
                background-color: #ffffff;
            }
            .email-header {
                background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
                color: white;
                padding: 30px 20px;
                text-align: center;
            }
            .email-header h1 {
                margin: 0;
                font-size: 28px;
                font-weight: 700;
            }
            .email-header p {
                margin: 10px 0 0;
                opacity: 0.9;
                font-weight: 300;
            }
            .email-body {
                padding: 40px 30px;
                background-color: #ffffff;
            }
            .greeting {
                font-size: 20px;
                margin-bottom: 20px;
                color: #2c3e50;
            }
            .message {
                margin-bottom: 30px;
                color: #555;
            }
            .info-container {
                margin: 30px 0;
                padding: 20px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border-left: 4px solid #2ecc71;
            }
            .info-title {
                font-size: 16px;
                color: #2c3e50;
                font-weight: 700;
                margin-bottom: 15px;
            }
            .info-item {
                padding: 8px 0;
                border-bottom: 1px solid #ecf0f1;
                display: flex;
                align-items: center;
            }
            .info-item:last-child {
                border-bottom: none;
            }
            .info-label {
                color: #7f8c8d;
                width: 40%;
                font-size: 14px;
            }
            .info-value {
                color: #2c3e50;
                width: 60%;
                font-weight: 500;
            }
            .features-list {
                margin: 25px 0;
                padding-left: 20px;
            }
            .features-list li {
                margin-bottom: 10px;
                color: #555;
            }
            .button-container {
                text-align: center;
                margin: 30px 0;
            }
            .button {
                display: inline-block;
                padding: 12px 30px;
                background-color: #3498db;
                color: white !important;
                text-decoration: none;
                border-radius: 4px;
                font-weight: 500;
                font-size: 16px;
                text-align: center;
                transition: background-color 0.3s;
            }
            .button:hover {
                background-color: #2980b9;
            }
            .success-icon {
                display: block;
                text-align: center;
                margin: 20px 0;
                font-size: 48px;
                color: #2ecc71;
            }
            .email-footer {
                background-color: #f8f9fa;
                padding: 20px;
                text-align: center;
                color: #7f8c8d;
                font-size: 13px;
                border-top: 1px solid #ecf0f1;
            }
            .social-links {
                margin: 15px 0;
            }
            .social-link {
                display: inline-block;
                margin: 0 10px;
                color: #3498db !important;
                text-decoration: none;
            }
            @media only screen and (max-width: 600px) {
                .email-body {
                    padding: 30px 20px;
                }
                .info-item {
                    flex-direction: column;
                    align-items: flex-start;
                }
                .info-label, .info-value {
                    width: 100%;
                }
                .info-label {
                    margin-bottom: 5px;
                }
            }
        </style>
    </head>
    <body>
        <div class="email-container">
            <div class="email-header">
                <h1>DPTMC.COM</h1>
                <p>Xác nhận địa chỉ email</p>
            </div>
            <div class="email-body">
                <div class="success-icon">✓</div>

                <div class="greeting">
                    Xin chào <strong>' . htmlspecialchars($username) . '</strong>,
                </div>

                <div class="message">
                    Cảm ơn bạn đã cập nhật địa chỉ email cho tài khoản DPTMC.COM của mình. Email này xác nhận rằng địa chỉ <strong>' . htmlspecialchars($to) . '</strong> đã được liên kết thành công với tài khoản của bạn.
                </div>

                <div class="info-container">
                    <div class="info-title">Thông tin tài khoản:</div>
                    <div class="info-item">
                        <div class="info-label">Tên người dùng:</div>
                        <div class="info-value">' . htmlspecialchars($username) . '</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Địa chỉ email:</div>
                        <div class="info-value">' . htmlspecialchars($to) . '</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Thời gian cập nhật:</div>
                        <div class="info-value">' . date('d/m/Y H:i:s') . '</div>
                    </div>
                </div>

                <div class="message">
                    Bây giờ bạn có thể sử dụng email này để:
                </div>

                <ul class="features-list">
                    <li>Khôi phục mật khẩu nếu bạn quên</li>
                    <li>Nhận thông báo quan trọng từ hệ thống</li>
                    <li>Bảo mật tài khoản của bạn</li>
                    <li>Nhận các cập nhật và thông báo mới</li>
                </ul>

                <div class="button-container">
                    <a href="https://id.dptmc.com/account" class="button">Quản lý tài khoản</a>
                </div>
            </div>

            <div class="email-footer">
                <div>Email này được gửi tự động từ hệ thống DPTMC.COM, vui lòng không trả lời.</div>
                <div class="social-links">
                    <a href="https://dptmc.com" class="social-link">Website</a> |
                    <a href="https://dptmc.com/contact" class="social-link">Liên hệ</a> |
                    <a href="https://dptmc.com/help" class="social-link">Trợ giúp</a>
                </div>
                <div>&copy; ' . date('Y') . ' DPTMC.COM - Mọi quyền được bảo lưu</div>
            </div>
        </div>
    </body>
    </html>';

    $altBody = "Xin chào $username,\n\nCảm ơn bạn đã cập nhật địa chỉ email cho tài khoản DPTMC.COM của mình. Email này xác nhận rằng địa chỉ $to đã được liên kết với tài khoản của bạn.\n\nThông tin tài khoản:\n- Tên người dùng: $username\n- Email: $to\n- Thời gian cập nhật: " . date('d/m/Y H:i:s') . "\n\nBây giờ bạn có thể sử dụng email này để:\n- Khôi phục mật khẩu nếu bạn quên\n- Nhận thông báo quan trọng từ hệ thống\n- Bảo mật tài khoản của bạn\n\nDPTMC.COM";

    return sendEmail($to, $subject, $body, $altBody);
}

/**
 * Send email verification OTP
 *
 * @param string $to Email address to send to
 * @param string $username Username
 * @param string $otp OTP code
 * @return array Result with status and message
 */
function sendEmailVerificationOTP($to, $username, $otp) {
    $subject = '[DPTMC.COM] Mã xác minh email: ' . $otp;

    $body = '<!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Xác minh Email</title>
        <style>
            @import url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap");
            body {
                font-family: "Roboto", Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                background-color: #f5f5f5;
                margin: 0;
                padding: 0;
            }
            .email-container {
                max-width: 600px;
                margin: 0 auto;
                background-color: #ffffff;
            }
            .email-header {
                background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
                color: white;
                padding: 30px 20px;
                text-align: center;
            }
            .email-header h1 {
                margin: 0;
                font-size: 28px;
                font-weight: 700;
            }
            .email-header p {
                margin: 10px 0 0;
                opacity: 0.9;
                font-weight: 300;
            }
            .email-body {
                padding: 40px 30px;
                background-color: #ffffff;
            }
            .greeting {
                font-size: 20px;
                margin-bottom: 20px;
                color: #2c3e50;
            }
            .message {
                margin-bottom: 30px;
                color: #555;
            }
            .otp-container {
                text-align: center;
                margin: 30px 0;
                padding: 20px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border-left: 4px solid #3498db;
            }
            .otp-code {
                font-size: 32px;
                color: #2c3e50;
                letter-spacing: 8px;
                font-weight: 700;
                margin: 10px 0;
            }
            .otp-label {
                font-size: 14px;
                color: #7f8c8d;
                margin-bottom: 10px;
                text-transform: uppercase;
                letter-spacing: 1px;
            }
            .otp-expires {
                font-size: 14px;
                color: #e74c3c;
                margin-top: 10px;
            }
            .button-container {
                text-align: center;
                margin: 30px 0;
            }
            .button {
                display: inline-block;
                padding: 12px 30px;
                background-color: #3498db;
                color: white !important;
                text-decoration: none;
                border-radius: 4px;
                font-weight: 500;
                font-size: 16px;
                text-align: center;
                transition: background-color 0.3s;
            }
            .button:hover {
                background-color: #2980b9;
            }
            .note {
                background-color: #fef9e7;
                padding: 15px;
                border-radius: 4px;
                margin-top: 30px;
                border-left: 4px solid #f1c40f;
                font-size: 14px;
                color: #7f8c8d;
            }
            .email-footer {
                background-color: #f8f9fa;
                padding: 20px;
                text-align: center;
                color: #7f8c8d;
                font-size: 13px;
                border-top: 1px solid #ecf0f1;
            }
            .social-links {
                margin: 15px 0;
            }
            .social-link {
                display: inline-block;
                margin: 0 10px;
                color: #3498db !important;
                text-decoration: none;
            }
            @media only screen and (max-width: 600px) {
                .email-body {
                    padding: 30px 20px;
                }
                .otp-code {
                    font-size: 28px;
                    letter-spacing: 6px;
                }
            }
        </style>
    </head>
    <body>
        <div class="email-container">
            <div class="email-header">
                <h1>DPTMC.COM</h1>
                <p>Hệ thống xác thực tài khoản</p>
            </div>
            <div class="email-body">
                <div class="greeting">
                    Xin chào <strong>' . htmlspecialchars($username) . '</strong>,
                </div>

                <div class="message">
                    Cảm ơn bạn đã đăng ký tài khoản tại DPTMC.COM. Để hoàn tất quá trình đăng ký, vui lòng sử dụng mã xác minh dưới đây:
                </div>

                <div class="otp-container">
                    <div class="otp-label">Mã xác minh của bạn</div>
                    <div class="otp-code">' . htmlspecialchars($otp) . '</div>
                    <div class="otp-expires">Mã này sẽ hết hạn sau 30 phút</div>
                </div>

                <div class="message">
                    Nhập mã này vào trang xác minh để hoàn tất quá trình đăng ký tài khoản của bạn.
                </div>

                <div class="note">
                    <strong>Lưu ý:</strong> Nếu bạn không thực hiện yêu cầu này, vui lòng bỏ qua email này hoặc liên hệ với chúng tôi nếu bạn có bất kỳ câu hỏi nào.
                </div>
            </div>

            <div class="email-footer">
                <div>Email này được gửi tự động từ hệ thống DPTMC.COM, vui lòng không trả lời.</div>
                <div class="social-links">
                    <a href="https://id.dptmc.com" class="social-link">Website</a> |
                    <a href="https://discord.dptmc.com" class="social-link">Liên hệ</a> |
                    <a href="https://discord.dptmc.com" class="social-link">Trợ giúp</a>
                </div>
                <div>&copy; ' . date('Y') . ' DPTMC.COM - Mọi quyền được bảo lưu</div>
            </div>
        </div>
    </body>
    </html>';

    $altBody = "Xin chào $username,\n\nCảm ơn bạn đã đăng ký tài khoản tại DPTMC.COM. Để hoàn tất quá trình đăng ký, vui lòng xác minh địa chỉ email của bạn bằng mã xác minh dưới đây:\n\n$otp\n\nMã xác minh này sẽ hết hạn sau 30 phút. Vui lòng không chia sẻ mã này với bất kỳ ai.\n\nNếu bạn không thực hiện yêu cầu này, vui lòng bỏ qua email này hoặc liên hệ với chúng tôi nếu bạn có bất kỳ câu hỏi nào.\n\nDPTMC.COM";

    return sendEmail($to, $subject, $body, $altBody);
}

function sendTestEmailHumanized($to) {
    $subject = 'Thông báo xác nhận từ DPTMC.COM - ' . date('d/m/Y H:i');

    // Minecraft-style email template
    $body = '<html>
    <head>
        <style>
            @import url("https://fonts.googleapis.com/css2?family=Minecraft&display=swap");
            body {
                font-family: "Minecraft", Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                background-color: #f5f5f5;
            }
            .container {
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #E7E2D2;
                border: 4px solid #555;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
            }
            .header {
                background-color: #546E43;
                color: white;
                padding: 10px;
                text-align: center;
                border-bottom: 4px solid #3B4F2E;
            }
            .content {
                padding: 20px;
                background-color: #E7E2D2;
            }
            .info-box {
                background-color: #D0C8A5;
                border: 2px solid #7E3A14;
                padding: 15px;
                margin: 15px 0;
            }
            .button {
                display: inline-block;
                padding: 10px 15px;
                background-color: #546E43;
                color: white;
                text-decoration: none;
                border: 2px solid #3B4F2E;
                font-weight: bold;
                text-align: center;
                margin: 10px 0;
            }
            .footer {
                font-size: 12px;
                color: #777;
                text-align: center;
                border-top: 2px solid #999;
                padding-top: 10px;
                margin-top: 20px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h2 style="margin: 0;">DPTMC.COM</h2>
            </div>
            <div class="content">
                <h3 style="color: #7E3A14; text-align: center;">Xin chào!</h3>
                <p>Bạn nhận được email này vì đã yêu cầu kiểm tra chức năng gửi email từ hệ thống DPTMC.COM.</p>
                <p>Nếu đây là bạn, vui lòng bỏ qua email này. Nếu không phải bạn, hãy liên hệ với bộ phận hỗ trợ của chúng tôi.</p>

                <div class="info-box">
                    <h4 style="margin-top: 0; color: #7E3A14;">Thông tin email:</h4>
                    <ul style="list-style-type: square; padding-left: 20px;">
                        <li><b>Thời gian gửi:</b> ' . date('d/m/Y H:i:s') . '</li>
                        <li><b>Địa chỉ nhận:</b> ' . htmlspecialchars($to) . '</li>
                        <li><b>IP người gửi:</b> ' . $_SERVER['REMOTE_ADDR'] . '</li>
                    </ul>
                </div>

                <div style="text-align: center; margin-top: 20px;">
                    <a href="https://id.dptmc.com" class="button">TRUY CẬP WEBSITE</a>
                </div>
            </div>
            <div class="footer">
                Email này được gửi tự động từ hệ thống DPTMC.COM, vui lòng không trả lời.<br>
                &copy; ' . date('Y') . ' DPTMC.COM - Minecraft Server Việt Nam
            </div>
        </div>
    </body>
    </html>';

    $altBody = "Xin chào!\nBạn nhận được email này vì đã yêu cầu kiểm tra chức năng gửi email từ hệ thống DPTMC.COM.\nNếu không phải bạn, hãy liên hệ với bộ phận hỗ trợ của chúng tôi.\nThời gian gửi: " . date('d/m/Y H:i:s') . "\nĐội ngũ DPTMC.COM";

    return sendEmail($to, $subject, $body, $altBody);
}
?>
