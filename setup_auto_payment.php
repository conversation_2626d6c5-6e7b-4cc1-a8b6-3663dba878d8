<?php
// Setup script cho hệ thống tự động duyệt thanh toán
require_once 'config/config.php';

echo "<h2>Setup Hệ thống Tự động Duyệt Thanh toán</h2>";

try {
    // Tạo bảng
    $sql = file_get_contents('sql/create_qr_transactions_table.sql');
    $conn->exec($sql);
    
    echo "✅ Các bảng đã được tạo thành công!<br><br>";
    
    // Test script tự động
    echo "<h3>Test Script Tự động:</h3>";
    echo "<a href='/cron/auto_check_payments.php?manual=1' target='_blank' class='btn btn-primary'>Test Script Tự động</a><br><br>";
    
    echo "<h3>Cấu hình Cron Job:</h3>";
    echo "<p>Thêm dòng sau vào crontab để chạy mỗi 2 phút:</p>";
    echo "<code>*/2 * * * * /usr/bin/php " . realpath('cron/auto_check_payments.php') . " >> /var/log/auto_payment.log 2>&1</code><br><br>";
    
    echo "<h3>Hoặc sử dụng Cron Job qua cPanel:</h3>";
    echo "<ol>";
    echo "<li>Vào cPanel → Cron Jobs</li>";
    echo "<li>Chọn: Every 2 minutes</li>";
    echo "<li>Command: <code>/usr/bin/php " . realpath('cron/auto_check_payments.php') . "</code></li>";
    echo "</ol>";
    
    echo "<h3>Kiểm tra Log:</h3>";
    echo "<p>Log file sẽ được lưu tại: <code>/var/log/auto_payment.log</code></p>";
    
    echo "<h3>Cách hoạt động:</h3>";
    echo "<ol>";
    echo "<li>User tạo QR và chuyển khoản</li>";
    echo "<li>Giao dịch được lưu vào bảng <code>nlogin_qr_pending</code></li>";
    echo "<li>Cron job chạy mỗi 2 phút, kiểm tra API thanh toán</li>";
    echo "<li>Nếu tìm thấy giao dịch khớp, tự động cộng tiền vào game</li>";
    echo "<li>Giao dịch được chuyển sang bảng <code>nlogin_qr_transactions</code></li>";
    echo "</ol>";
    
    echo "<h3>Trạng thái hiện tại:</h3>";
    
    // Kiểm tra bảng pending
    $stmt = $conn->query("SELECT COUNT(*) as count FROM nlogin_qr_pending WHERE processed = 0");
    $pending_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<p>Giao dịch đang chờ xử lý: <strong>$pending_count</strong></p>";
    
    // Kiểm tra bảng transactions
    $stmt = $conn->query("SELECT COUNT(*) as count FROM nlogin_qr_transactions");
    $completed_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<p>Giao dịch đã hoàn thành: <strong>$completed_count</strong></p>";
    
    echo "<br><div class='alert alert-warning'>";
    echo "<strong>Lưu ý:</strong> Sau khi setup xong, hãy xóa file này để bảo mật!";
    echo "</div>";
    
} catch(PDOException $e) {
    echo "❌ Lỗi: " . $e->getMessage();
}
?>

<style>
.btn {
    display: inline-block;
    padding: 10px 20px;
    background: #007bff;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    margin: 10px 0;
}

.alert {
    padding: 15px;
    margin: 20px 0;
    border-radius: 5px;
}

.alert-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

code {
    background: #f8f9fa;
    padding: 2px 5px;
    border-radius: 3px;
    font-family: monospace;
}
</style>
