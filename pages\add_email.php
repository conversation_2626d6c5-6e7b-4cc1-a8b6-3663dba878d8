<?php
// Kiểm tra đăng nhập
if (!isset($_SESSION['user_id'])) {
    $_SESSION['message'] = 'Vui lòng đăng nhập để tiếp tục.';
    $_SESSION['message_type'] = 'danger';
    header('Location: /login');
    exit;
}

// Lấy thông tin người dùng
$user = getUserData($_SESSION['user_id']);

// Nếu không tìm thấy thông tin người dùng, chuyển hướng đến trang đăng nhập
if (!$user) {
    $_SESSION['message'] = 'Không tìm thấy thông tin tài khoản. Vui lòng đăng nhập lại.';
    $_SESSION['message_type'] = 'danger';
    header('Location: /logout');
    exit;
}

// Nếu người dùng đã có email, chuyển hướng đến trang tài khoản
if (!empty($user['email'])) {
    $_SESSION['message'] = 'Tài khoản của bạn đã có email.';
    $_SESSION['message_type'] = 'info';
    header('Location: /account');
    exit;
}

// Xử lý form thêm email
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitize($_POST['email']);

    // Kiểm tra email hợp lệ
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $_SESSION['message'] = 'Vui lòng nhập địa chỉ email hợp lệ.';
        $_SESSION['message_type'] = 'danger';
    } else {
        // Kiểm tra xem email đã tồn tại trong hệ thống chưa
        global $conn;
        $stmt = $conn->prepare("SELECT * FROM nlogin WHERE email = :email AND ai != :user_id");
        $stmt->bindParam(':email', $email);
        $stmt->bindParam(':user_id', $_SESSION['user_id']);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $_SESSION['message'] = 'Email này đã được sử dụng bởi tài khoản khác.';
            $_SESSION['message_type'] = 'danger';
        } else {
            // Lưu email tạm thời vào session để xác minh
            $_SESSION['temp_email'] = $email;
            $_SESSION['temp_email_user_id'] = $_SESSION['user_id'];

            // Tạo mã OTP và gửi email xác minh
            $username = $user['last_name'] ?? $user['unique_id'];
            $verificationResult = createEmailVerificationToken($_SESSION['user_id'], $email, $username, 'add_email');

            if ($verificationResult['status']) {
                // Chuyển hướng đến trang xác minh email
                $_SESSION['message'] = 'Vui lòng kiểm tra email và nhập mã xác minh để hoàn tất quá trình thêm email.';
                $_SESSION['message_type'] = 'success';

                // Cho phép hiển thị OTP trong môi trường phát triển
                if (defined('SHOW_OTP') && SHOW_OTP === true) {
                    $_SESSION['debug_otp'] = $verificationResult['data']['otp'];
                }

                header('Location: /verify-add-email');
                exit;
            } else {
                $_SESSION['message'] = 'Không thể gửi email xác minh. Vui lòng thử lại sau.';
                $_SESSION['message_type'] = 'danger';
            }
        }
    }
}
?>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-5">
        <div class="card shadow-sm border-0 mb-5">
            <div class="card-header bg-white">
                <h4 class="text-center mb-0"><i class="fas fa-envelope text-primary"></i> Bổ sung Email</h4>
            </div>
            <div class="card-body p-4">
                <div class="alert alert-info shadow-sm">
                    <i class="fas fa-info-circle"></i> Tài khoản của bạn chưa có địa chỉ email. Vui lòng bổ sung email để có thể sử dụng đầy đủ tính năng của hệ thống.
                </div>

                <div class="text-center mb-4">
                    <i class="fas fa-envelope-open-text text-primary" style="font-size: 3rem;"></i>
                </div>

                <form method="POST" action="/add-email" class="needs-validation" novalidate>
                    <div class="form-group">
                        <label class="form-label" for="email">
                            <i class="fas fa-envelope text-primary"></i> Địa chỉ Email
                        </label>
                        <input type="email" class="form-control" id="email" name="email" placeholder="Nhập địa chỉ email của bạn" required>
                        <div class="invalid-feedback">
                            Vui lòng nhập địa chỉ email hợp lệ.
                        </div>
                        <small class="form-text text-muted">Email sẽ được sử dụng để khôi phục mật khẩu và nhận thông báo quan trọng.</small>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-save"></i> Lưu Email
                        </button>
                    </div>
                </form>
            </div>
            <div class="card-footer bg-white text-center">
                <a href="/account" class="text-muted"><small>Bỏ qua (không khuyến nghị)</small></a>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
