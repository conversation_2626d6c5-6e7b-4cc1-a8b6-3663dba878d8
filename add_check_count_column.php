<?php
// <PERSON><PERSON>t để thêm cột check_count
require_once 'config/config.php';

echo "<h2>Add Check Count Column</h2>";

try {
    // 1. Thêm cột check_count
    echo "<h3>1. Adding check_count column:</h3>";
    
    try {
        $conn->exec("ALTER TABLE nlogin_qr_pending ADD COLUMN check_count INT DEFAULT 0 COMMENT 'Số lần đã check nền'");
        echo "✅ Added check_count column<br>";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "ℹ️ check_count column already exists<br>";
        } else {
            echo "❌ Error adding check_count column: " . $e->getMessage() . "<br>";
        }
    }
    
    // 2. Cập nhật logic status = 3 với processed = 0
    echo "<h3>2. Updating cancelled transactions:</h3>";
    
    // Set processed = 0 cho status = 3 để cho phép check nền
    $stmt = $conn->prepare("
        UPDATE nlogin_qr_pending 
        SET processed = 0, check_count = 0 
        WHERE status = 3 AND processed = 1
    ");
    $stmt->execute();
    $updated_count = $stmt->rowCount();
    
    echo "✅ Updated $updated_count cancelled transactions to allow background check<br>";
    
    // 3. Hiển thị thống kê
    echo "<h3>3. Current Statistics:</h3>";
    
    $stmt = $conn->query("
        SELECT 
            status,
            processed,
            COUNT(*) as count,
            AVG(check_count) as avg_checks,
            CASE 
                WHEN status = 0 THEN 'Pending'
                WHEN status = 1 THEN 'Success'
                WHEN status = 3 AND processed = 0 THEN 'Cancelled (Checking)'
                WHEN status = 3 AND processed = 1 THEN 'Cancelled (Final)'
                ELSE 'Unknown'
            END as status_name
        FROM nlogin_qr_pending 
        GROUP BY status, processed 
        ORDER BY status, processed
    ");
    $stats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>Status</th><th>Processed</th><th>Status Name</th><th>Count</th><th>Avg Checks</th><th>Color</th>";
    echo "</tr>";
    
    foreach ($stats as $stat) {
        $color = '';
        if ($stat['status'] == 0) $color = '#ffc107'; // Yellow for pending
        elseif ($stat['status'] == 1) $color = '#28a745'; // Green for success
        elseif ($stat['status'] == 3 && $stat['processed'] == 0) $color = '#fd7e14'; // Orange for checking
        elseif ($stat['status'] == 3 && $stat['processed'] == 1) $color = '#dc3545'; // Red for final cancelled
        
        echo "<tr>";
        echo "<td>{$stat['status']}</td>";
        echo "<td>{$stat['processed']}</td>";
        echo "<td>{$stat['status_name']}</td>";
        echo "<td>{$stat['count']}</td>";
        echo "<td>" . round($stat['avg_checks'], 1) . "</td>";
        echo "<td style='background: $color; color: white; text-align: center;'>●</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 4. Hiển thị giao dịch gần đây
    echo "<h3>4. Recent Transactions:</h3>";
    
    $stmt = $conn->query("
        SELECT id, username, amount, transaction_code, created_at, processed, status, check_count,
               CASE 
                   WHEN status = 0 THEN 'Pending'
                   WHEN status = 1 THEN 'Success'
                   WHEN status = 3 AND processed = 0 THEN 'Cancelled (Checking)'
                   WHEN status = 3 AND processed = 1 THEN 'Cancelled (Final)'
                   ELSE 'Unknown'
               END as status_name
        FROM nlogin_qr_pending 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $recent = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($recent) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>User</th><th>Amount</th><th>Transaction Code</th><th>Created</th><th>Status</th><th>Checks</th>";
        echo "</tr>";
        
        foreach ($recent as $tx) {
            $status_color = '';
            if ($tx['status'] == 0) $status_color = '#ffc107';
            elseif ($tx['status'] == 1) $status_color = '#28a745';
            elseif ($tx['status'] == 3 && $tx['processed'] == 0) $status_color = '#fd7e14';
            elseif ($tx['status'] == 3 && $tx['processed'] == 1) $status_color = '#dc3545';
            
            echo "<tr>";
            echo "<td>{$tx['id']}</td>";
            echo "<td>{$tx['username']}</td>";
            echo "<td>" . number_format($tx['amount']) . "</td>";
            echo "<td><code>{$tx['transaction_code']}</code></td>";
            echo "<td>{$tx['created_at']}</td>";
            echo "<td style='background: $status_color; color: white; font-weight: bold;'>{$tx['status_name']}</td>";
            echo "<td>{$tx['check_count']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 5. Tạo index cho check_count
    echo "<h3>5. Adding index for check_count:</h3>";
    
    try {
        $conn->exec("ALTER TABLE nlogin_qr_pending ADD INDEX idx_status_processed_checks (status, processed, check_count)");
        echo "✅ Added composite index<br>";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "ℹ️ Index already exists<br>";
        } else {
            echo "❌ Error adding index: " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<hr>";
    echo "<h3>✅ Check count system updated!</h3>";
    echo "<p><strong>New Logic:</strong></p>";
    echo "<ul>";
    echo "<li><span style='color: #ffc107;'>●</span> <strong>Status 0</strong> - Pending: Đang chờ thanh toán</li>";
    echo "<li><span style='color: #28a745;'>●</span> <strong>Status 1</strong> - Success: Thanh toán thành công</li>";
    echo "<li><span style='color: #fd7e14;'>●</span> <strong>Status 3, Processed 0</strong> - Cancelled (Checking): Bị hủy nhưng vẫn check nền</li>";
    echo "<li><span style='color: #dc3545;'>●</span> <strong>Status 3, Processed 1</strong> - Cancelled (Final): Bị hủy hoàn toàn</li>";
    echo "</ul>";
    
    echo "<p><strong>Background Check Logic:</strong></p>";
    echo "<ul>";
    echo "<li>Status = 3, Processed = 0 → Vẫn check nền tối đa 2 lần</li>";
    echo "<li>Nếu tìm thấy payment → Chuyển thành Success</li>";
    echo "<li>Sau 2 lần check không có → Set Processed = 1 (Final)</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage();
}
?>

<style>
table {
    border-collapse: collapse;
    margin: 10px 0;
    font-size: 12px;
}

table th, table td {
    border: 1px solid #ddd;
    padding: 6px;
    text-align: left;
}

table th {
    background-color: #f2f2f2;
}

code {
    background: #e9ecef;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

h3 {
    color: #333;
    margin-top: 20px;
}
</style>
