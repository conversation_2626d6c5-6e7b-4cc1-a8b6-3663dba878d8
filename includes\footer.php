    </div><!-- End container -->

    <footer class="py-4 mt-5 bg-white border-top">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="text-muted mb-0">
                        &copy; <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. Đã đăng ký bản quyền.
                    </p>
                </div>
                <div class="col-md-6 text-right">
                    <p class="text-muted mb-0">
                        <i class="fas fa-server text-primary mr-1"></i>
                        <PERSON><PERSON><PERSON><PERSON> hỗ trợ bởi DPTCLOUD
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script src="assets/js/script.js"></script>
    <script src="assets/js/minecraft-skin.js"></script>

    <!-- Modern Theme JavaScript -->
    <script>
        // Hiệu ứng cho các phần tử
        document.addEventListener('DOMContentLoaded', function() {
            // Hiệu ứng focus cho form
            const formControls = document.querySelectorAll('.form-control');
            formControls.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });
                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('focused');
                });
            });

            // Hiệu ứng hover cho các card
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.classList.add('shadow');
                });
                card.addEventListener('mouseleave', function() {
                    this.classList.remove('shadow');
                });
            });
        });
    </script>
</body>
</html>
