<?php
/**
 * Webhook endpoint để nhận thông báo thanh toán từ hệ thống ngân hàng
 * (<PERSON>ế<PERSON> hệ thống ngân hàng hỗ trợ webhook)
 */

header('Content-Type: application/json');
require_once '../config/config.php';

// Chỉ chấp nhận POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Lấy dữ liệu từ webhook
$input = file_get_contents('php://input');
$data = json_decode($input, true);

// Log webhook data
error_log("Webhook received: " . $input);

try {
    // Validate webhook data
    if (!isset($data['transaction_id']) || !isset($data['amount']) || !isset($data['content'])) {
        throw new Exception('Invalid webhook data');
    }
    
    $transaction_id = $data['transaction_id'];
    $amount = $data['amount'];
    $content = $data['content'];
    
    // Tìm giao dịch pending khớp
    $stmt = $conn->prepare("
        SELECT * FROM nlogin_qr_pending 
        WHERE processed = 0 
        AND created_at > DATE_SUB(NOW(), INTERVAL 30 MINUTE)
    ");
    $stmt->execute();
    $pending_transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $found_pending = null;
    foreach ($pending_transactions as $pending) {
        $server = $pending['server'];
        $amount_match = ($amount == $pending['amount']);
        
        $content_match = false;
        
        // Kiểm tra mã giao dịch đầy đủ
        if (strpos($content, $pending['transaction_code']) !== false) {
            $content_match = true;
        }
        // Kiểm tra server + pattern
        elseif (preg_match('/' . $server . '[A-Z0-9]{8}/i', $content)) {
            $content_match = true;
        }
        // Kiểm tra chỉ có server name
        elseif (stripos($content, $server) !== false && $amount_match) {
            $content_match = true;
        }
        
        if ($content_match && $amount_match) {
            $found_pending = $pending;
            break;
        }
    }
    
    if (!$found_pending) {
        echo json_encode(['success' => false, 'message' => 'No matching pending transaction']);
        exit;
    }
    
    // Kiểm tra xem giao dịch đã được xử lý chưa
    $stmt = $conn->prepare("SELECT id FROM nlogin_qr_transactions WHERE transaction_id = :transaction_id");
    $stmt->bindParam(':transaction_id', $transaction_id);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        echo json_encode(['success' => false, 'message' => 'Transaction already processed']);
        exit;
    }
    
    // Xử lý thanh toán
    $stmt = $conn->prepare("
        INSERT INTO nlogin_qr_transactions 
        (user_id, username, transaction_id, amount, server, transaction_code, payment_data, created_at, processed_at) 
        VALUES (:user_id, :username, :transaction_id, :amount, :server, :transaction_code, :payment_data, :created_at, NOW())
    ");
    
    $stmt->bindParam(':user_id', $found_pending['user_id']);
    $stmt->bindParam(':username', $found_pending['username']);
    $stmt->bindParam(':transaction_id', $transaction_id);
    $stmt->bindParam(':amount', $found_pending['amount']);
    $stmt->bindParam(':server', $found_pending['server']);
    $stmt->bindParam(':transaction_code', $found_pending['transaction_code']);
    $stmt->bindParam(':payment_data', $input);
    $stmt->bindParam(':created_at', $found_pending['created_at']);
    $stmt->execute();
    
    // Gửi lệnh đến server game
    $success = sendGameCommand($found_pending['username'], $found_pending['amount'], $found_pending['server']);
    
    if ($success) {
        // Đánh dấu đã xử lý
        $stmt = $conn->prepare("UPDATE nlogin_qr_pending SET processed = 1, processed_at = NOW() WHERE id = :id");
        $stmt->bindParam(':id', $found_pending['id']);
        $stmt->execute();
        
        echo json_encode(['success' => true, 'message' => 'Payment processed successfully']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to send game command']);
    }
    
} catch (Exception $e) {
    error_log("Webhook error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

function sendGameCommand($username, $amount, $server) {
    // Same function as in auto_check_payments.php
    // ... (copy the function here)
    return true; // Placeholder
}
?>
