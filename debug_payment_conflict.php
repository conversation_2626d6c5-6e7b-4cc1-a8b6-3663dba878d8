<?php
// Debug script để tìm conflict trong payment
require_once 'config/config.php';

echo "<h2>Debug Payment Conflict</h2>";

session_start();

// 1. Kiểm tra payment API
echo "<h3>1. Recent Payments from API:</h3>";
try {
    $payment_api_url = 'http://*************:3000/payments';
    $response = file_get_contents($payment_api_url);
    
    if ($response) {
        $payments = json_decode($response, true);
        
        if (is_array($payments) && count($payments) > 0) {
            echo "✅ API hoạt động, có " . count($payments) . " giao dịch<br>";
            
            // Hiển thị 10 giao dịch gần nhất
            echo "<h4>10 giao dịch gần nhất:</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th>STT</th><th>Amount</th><th>Content</th><th>Date</th><th>Transaction ID</th><th>Age (minutes)</th>";
            echo "</tr>";
            
            foreach (array_slice($payments, 0, 10) as $i => $payment) {
                $time_diff = (time() - strtotime($payment['date'])) / 60; // minutes
                
                echo "<tr>";
                echo "<td>" . ($i + 1) . "</td>";
                echo "<td>" . number_format($payment['amount']) . "</td>";
                echo "<td><code>" . htmlspecialchars(substr($payment['content'], 0, 50)) . "...</code></td>";
                echo "<td>{$payment['date']}</td>";
                echo "<td><code>{$payment['transaction_id']}</code></td>";
                echo "<td>" . round($time_diff, 1) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
        } else {
            echo "❌ API không trả về dữ liệu hợp lệ<br>";
        }
    } else {
        echo "❌ Không thể kết nối đến API<br>";
    }
} catch (Exception $e) {
    echo "❌ Lỗi API: " . $e->getMessage() . "<br>";
}

// 2. Kiểm tra completed transactions
echo "<h3>2. Completed Transactions in Database:</h3>";
try {
    $stmt = $conn->query("
        SELECT * FROM nlogin_qr_transactions 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $completed_txs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($completed_txs) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>User</th><th>Transaction Code</th><th>Amount</th><th>Server</th><th>Transaction ID</th><th>Created</th>";
        echo "</tr>";
        
        foreach ($completed_txs as $tx) {
            echo "<tr>";
            echo "<td>{$tx['id']}</td>";
            echo "<td>{$tx['user_id']}</td>";
            echo "<td><code>{$tx['transaction_code']}</code></td>";
            echo "<td>" . number_format($tx['amount']) . "</td>";
            echo "<td>{$tx['server']}</td>";
            echo "<td><code>{$tx['transaction_id']}</code></td>";
            echo "<td>{$tx['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "Không có completed transactions.<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// 3. Tìm conflict giữa API và database
echo "<h3>3. Finding Conflicts:</h3>";
try {
    $payment_api_url = 'http://*************:3000/payments';
    $response = file_get_contents($payment_api_url);
    
    if ($response) {
        $payments = json_decode($response, true);
        $conflicts = [];
        
        foreach ($payments as $payment) {
            // Kiểm tra xem transaction_id này đã có trong database chưa
            $stmt = $conn->prepare("
                SELECT transaction_code, user_id, amount, created_at 
                FROM nlogin_qr_transactions 
                WHERE transaction_id = :transaction_id
            ");
            $stmt->bindParam(':transaction_id', $payment['transaction_id']);
            $stmt->execute();
            $existing = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($existing) {
                $conflicts[] = [
                    'payment' => $payment,
                    'existing' => $existing
                ];
            }
        }
        
        if (count($conflicts) > 0) {
            echo "⚠️ Found " . count($conflicts) . " conflicts:<br>";
            
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #fff3cd;'>";
            echo "<th>Transaction ID</th><th>API Amount</th><th>API Content</th><th>DB Transaction Code</th><th>DB Amount</th><th>DB User</th>";
            echo "</tr>";
            
            foreach ($conflicts as $conflict) {
                $payment = $conflict['payment'];
                $existing = $conflict['existing'];
                
                echo "<tr>";
                echo "<td><code>{$payment['transaction_id']}</code></td>";
                echo "<td>" . number_format($payment['amount']) . "</td>";
                echo "<td><code>" . htmlspecialchars(substr($payment['content'], 0, 30)) . "...</code></td>";
                echo "<td><code>{$existing['transaction_code']}</code></td>";
                echo "<td>" . number_format($existing['amount']) . "</td>";
                echo "<td>{$existing['user_id']}</td>";
                echo "</tr>";
            }
            echo "</table>";
            
        } else {
            echo "✅ Không tìm thấy conflict nào.<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Lỗi tìm conflict: " . $e->getMessage() . "<br>";
}

// 4. Kiểm tra giao dịch cụ thể
echo "<h3>4. Check Specific Transaction:</h3>";
$test_transaction = 'earth3C07D7CA';

echo "<strong>Testing transaction:</strong> <code>$test_transaction</code><br>";

// Kiểm tra trong pending
$stmt = $conn->prepare("SELECT * FROM nlogin_qr_pending WHERE transaction_code = :code");
$stmt->bindParam(':code', $test_transaction);
$stmt->execute();
$pending = $stmt->fetch(PDO::FETCH_ASSOC);

if ($pending) {
    echo "📋 Found in pending: ID {$pending['id']}, processed = {$pending['processed']}<br>";
} else {
    echo "❌ Not found in pending<br>";
}

// Kiểm tra trong completed
$stmt = $conn->prepare("SELECT * FROM nlogin_qr_transactions WHERE transaction_code = :code");
$stmt->bindParam(':code', $test_transaction);
$stmt->execute();
$completed = $stmt->fetch(PDO::FETCH_ASSOC);

if ($completed) {
    echo "✅ Found in completed: ID {$completed['id']}, transaction_id = {$completed['transaction_id']}<br>";
} else {
    echo "❌ Not found in completed<br>";
}

// Tìm trong API payments
try {
    $payment_api_url = 'http://*************:3000/payments';
    $response = file_get_contents($payment_api_url);
    
    if ($response) {
        $payments = json_decode($response, true);
        $found_in_api = false;
        
        foreach ($payments as $payment) {
            if (strpos($payment['content'], $test_transaction) !== false) {
                echo "🔍 Found in API: amount = {$payment['amount']}, transaction_id = {$payment['transaction_id']}<br>";
                $found_in_api = true;
                break;
            }
        }
        
        if (!$found_in_api) {
            echo "❌ Not found in API payments<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Error checking API: " . $e->getMessage() . "<br>";
}

// 5. Cleanup actions
echo "<h3>5. Cleanup Actions:</h3>";
echo "<button onclick='cleanupConflicts()' style='margin: 5px; padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;'>Remove Conflicting Transactions</button>";
echo "<button onclick='resetPending()' style='margin: 5px; padding: 8px 16px; background: #ffc107; color: black; border: none; border-radius: 4px; cursor: pointer;'>Reset Pending Transactions</button>";

echo "<script>
function cleanupConflicts() {
    if (confirm('Remove all conflicting transactions from database?')) {
        fetch('/cleanup_conflicts.php')
        .then(response => response.text())
        .then(data => {
            alert('Cleanup completed: ' + data);
            location.reload();
        });
    }
}

function resetPending() {
    if (confirm('Reset all pending transactions to unprocessed?')) {
        fetch('/reset_pending.php')
        .then(response => response.text())
        .then(data => {
            alert('Reset completed: ' + data);
            location.reload();
        });
    }
}
</script>";

echo "<hr>";
echo "<h3>🔍 Phân tích:</h3>";
echo "<ul>";
echo "<li>Nếu có <strong>conflicts</strong> → Có giao dịch cũ trong database khớp với API</li>";
echo "<li>Nếu transaction <strong>found in completed</strong> → Đã được xử lý trước đó</li>";
echo "<li>Nếu transaction <strong>found in API</strong> → Có payment thật khớp</li>";
echo "<li>Cần <strong>cleanup conflicts</strong> để tránh false positive</li>";
echo "</ul>";

echo "<p><strong>Sau khi debug xong, xóa file này!</strong></p>";
?>

<style>
table {
    border-collapse: collapse;
    margin: 10px 0;
    font-size: 11px;
}

table th, table td {
    border: 1px solid #ddd;
    padding: 4px;
    text-align: left;
}

table th {
    background-color: #f2f2f2;
}

code {
    background: #e9ecef;
    padding: 1px 3px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 10px;
}

h3 {
    color: #333;
    margin-top: 20px;
}
</style>
