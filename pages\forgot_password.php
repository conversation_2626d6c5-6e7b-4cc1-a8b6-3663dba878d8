<?php
// Include PHPMailer
require_once 'vendor/autoload.php';
require_once 'includes/phpmailer/mailer.php';

// Process forgot password form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitize($_POST['email']);

    // Validate email
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $_SESSION['message'] = 'Vui lòng nhập địa chỉ email hợp lệ';
        $_SESSION['message_type'] = 'danger';
    } else {
        // Create password reset token
        $result = createPasswordResetToken($email);

        if ($result['status']) {
            // Tạo token thành công

            // Send password reset email with OTP code
            $emailResult = sendPasswordResetEmail(
                $result['data']['email'],
                $result['data']['username'],
                $result['data']['otp']
            );

            // Kiểm tra kết quả gửi email

            if ($emailResult['status']) {
                // <PERSON>ểm tra xem có đang ở chế độ phát triển không
                if (defined('DEV_MODE') && DEV_MODE === true) {
                    $_SESSION['message'] = 'Mã xác minh đã được tạo (chế độ phát triển)';
                } else {
                    if (defined('SHOW_OTP') && SHOW_OTP === true) {
                        $_SESSION['message'] = 'Mã xác minh đã được gửi đến email của bạn. Mã OTP: ' . $result['data']['otp'];
                    } else {
                        $_SESSION['message'] = 'Mã xác minh đã được gửi đến email của bạn';
                    }
                }
                $_SESSION['message_type'] = 'success';

                // Lưu email vào session để sử dụng trong trang xác minh
                $_SESSION['reset_email'] = $result['data']['email'];

                // Lưu OTP vào session để debug (chỉ dùng trong môi trường phát triển hoặc khi SHOW_OTP = true)
                if (defined('SHOW_OTP') && SHOW_OTP === true) {
                    $_SESSION['debug_otp'] = $result['data']['otp'];
                }

                // Chuyển hướng đến trang xác minh OTP
                header('Location: /verify-otp');
                exit;
            } else {
                // Không thể gửi email

                // Hiển thị thông báo lỗi chi tiết (chỉ dùng trong môi trường phát triển)
                if (defined('DEV_MODE') && DEV_MODE === true) {
                    $_SESSION['message'] = 'Không thể gửi email đặt lại mật khẩu: ' . $emailResult['message'];
                } else {
                    $_SESSION['message'] = 'Không thể gửi email đặt lại mật khẩu. Vui lòng liên hệ quản trị viên.';
                }
                $_SESSION['message_type'] = 'danger';
            }
        } else {
            // Không thể tạo token đặt lại mật khẩu
            $_SESSION['message'] = 'Không thể tạo mã đặt lại mật khẩu: ' . $result['message'];
            $_SESSION['message_type'] = 'danger';

            // In production, use this instead:
            // $_SESSION['message'] = 'Nếu địa chỉ email tồn tại trong hệ thống, hướng dẫn khôi phục mật khẩu sẽ được gửi đến email của bạn.';
            // $_SESSION['message_type'] = 'info';
        }
    }
}
?>

<div class="row">
    <div class="col-md-12">
        <div class="auth-form">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-key"></i> Khôi phục mật khẩu</h4>
                </div>
                <div class="card-body">
                    <p class="text-center mb-4">Nhập địa chỉ email của bạn để nhận hướng dẫn khôi phục mật khẩu.</p>

                    <form method="POST" action="/forgot-password" class="needs-validation" novalidate>
                        <div class="form-group">
                            <label for="email">Email của tài khoản</label>
                            <div class="input-group">
                                <div class="input-group-prepend">
                                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                </div>
                                <input type="email" class="form-control" id="email" name="email" placeholder="Nhập địa chỉ email" required>
                                <div class="invalid-feedback">
                                    Vui lòng nhập địa chỉ email hợp lệ.
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary">Xác nhận thông tin</button>

                        <div class="form-text text-center mt-3">
                            <p><a href="/login">Quay lại đăng nhập</a></p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
