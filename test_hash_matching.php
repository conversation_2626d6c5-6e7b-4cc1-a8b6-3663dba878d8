<?php
// Test script cho hash matching logic
echo "<h2>Test Hash Matching Logic</h2>";

// Test cases
$test_cases = [
    [
        'transaction_code' => 'earth0487251A',
        'server' => 'earth',
        'api_contents' => [
            'CUSTOMER earth0487251A 310525 23 06 08',
            'CUSTOMER Earth0487251A 310525 23 06 08', 
            'CUSTOMER EARTH0487251A 310525 23 06 08',
            'CUSTOMER 0487251A 310525 23 06 08',
            'CUSTOMER 0487251a 310525 23 06 08',
            'Ma giao dich 0487251A Trace',
            'earthQHGBHTZM some other content',
            'No match content here'
        ]
    ],
    [
        'transaction_code' => 'ultrapvpBDEA4882',
        'server' => 'ultrapvp',
        'api_contents' => [
            'CUSTOMER ultrapvpBDEA4882 310525',
            'CUSTOMER UltraPvPBDEA4882 310525',
            'CUSTOMER BDEA4882 310525',
            'CUSTOMER bdea4882 310525',
            'Ma giao dich BDEA4882',
            'ultrapvp other content',
            'No match here'
        ]
    ]
];

echo "<style>
table { border-collapse: collapse; margin: 20px 0; width: 100%; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
.match { background-color: #d4edda; color: #155724; font-weight: bold; }
.no-match { background-color: #f8d7da; color: #721c24; }
code { background: #e9ecef; padding: 2px 4px; border-radius: 3px; font-family: monospace; }
</style>";

foreach ($test_cases as $i => $test_case) {
    $transaction_code = $test_case['transaction_code'];
    $server = $test_case['server'];
    $hash_part = substr($transaction_code, strlen($server)); // Extract hash
    
    echo "<h3>Test Case " . ($i + 1) . ": <code>$transaction_code</code></h3>";
    echo "<p><strong>Server:</strong> <code>$server</code></p>";
    echo "<p><strong>Hash Part:</strong> <code>$hash_part</code></p>";
    
    echo "<table>";
    echo "<tr>";
    echo "<th>API Content</th>";
    echo "<th>Full Match</th>";
    echo "<th>Full Upper</th>";
    echo "<th>Hash Match</th>";
    echo "<th>Hash Upper</th>";
    echo "<th>Result</th>";
    echo "</tr>";
    
    foreach ($test_case['api_contents'] as $content) {
        // Test matching logic
        $full_match = stripos($content, $transaction_code) !== false;
        $full_upper = stripos($content, strtoupper($transaction_code)) !== false;
        $hash_match = stripos($content, $hash_part) !== false;
        $hash_upper = stripos($content, strtoupper($hash_part)) !== false;
        
        $overall_match = $full_match || $full_upper || $hash_match || $hash_upper;
        
        echo "<tr>";
        echo "<td><code>" . htmlspecialchars($content) . "</code></td>";
        echo "<td>" . ($full_match ? "✅" : "❌") . "</td>";
        echo "<td>" . ($full_upper ? "✅" : "❌") . "</td>";
        echo "<td>" . ($hash_match ? "✅" : "❌") . "</td>";
        echo "<td>" . ($hash_upper ? "✅" : "❌") . "</td>";
        echo "<td class='" . ($overall_match ? "match" : "no-match") . "'>" . ($overall_match ? "✅ MATCH" : "❌ NO MATCH") . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
}

// Test với real API data
echo "<h3>Test với Real API Data:</h3>";

try {
    $payment_api_url = 'http://*************:3000/payments';
    $response = file_get_contents($payment_api_url);
    
    if ($response) {
        $payments = json_decode($response, true);
        
        echo "<p>✅ API connected, testing với 5 payment gần nhất:</p>";
        
        echo "<table>";
        echo "<tr>";
        echo "<th>Payment Content</th>";
        echo "<th>Extracted Hashes</th>";
        echo "</tr>";
        
        foreach (array_slice($payments, 0, 5) as $payment) {
            $content = $payment['content'] ?? '';
            
            // Extract potential hashes (8 character alphanumeric after server names)
            $extracted_hashes = [];
            
            // Look for patterns like earthXXXXXXXX, ultrapvpXXXXXXXX, etc.
            if (preg_match_all('/(?:earth|ultrapvp|skyblock)([A-Z0-9]{8})/i', $content, $matches)) {
                $extracted_hashes = array_merge($extracted_hashes, $matches[1]);
            }
            
            // Look for standalone 8-character hashes
            if (preg_match_all('/\b([A-Z0-9]{8})\b/i', $content, $matches)) {
                $extracted_hashes = array_merge($extracted_hashes, $matches[1]);
            }
            
            $extracted_hashes = array_unique($extracted_hashes);
            
            echo "<tr>";
            echo "<td><code>" . htmlspecialchars(substr($content, 0, 80)) . "...</code></td>";
            echo "<td>";
            if ($extracted_hashes) {
                foreach ($extracted_hashes as $hash) {
                    echo "<code>$hash</code> ";
                }
            } else {
                echo "<em>No hashes found</em>";
            }
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "❌ Cannot connect to API<br>";
    }
} catch (Exception $e) {
    echo "❌ API Error: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h3>🎯 Kết luận:</h3>";
echo "<ul>";
echo "<li><strong>Logic mới:</strong> Kiểm tra cả full transaction code VÀ chỉ phần hash</li>";
echo "<li><strong>Linh hoạt hơn:</strong> API có thể có format khác nhau vẫn match được</li>";
echo "<li><strong>Case insensitive:</strong> Hoạt động với cả chữ hoa và chữ thường</li>";
echo "<li><strong>Ví dụ:</strong> <code>earth0487251A</code> sẽ match với content chứa <code>0487251A</code></li>";
echo "</ul>";

echo "<p><strong>Sau khi test xong, xóa file này!</strong></p>";
?>
