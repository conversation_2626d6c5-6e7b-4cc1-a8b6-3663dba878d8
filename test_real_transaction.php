<?php
// Test với transaction thật
require_once 'config/config.php';

echo "<h2>Test Real Transaction: earthBDEA4882</h2>";

$target_transaction = 'earthBDEA4882';
$target_amount = 2000;
$server = 'earth';

try {
    // 1. Simulate API check logic
    echo "<h3>1. Simulate API Check Logic:</h3>";
    
    $payment_api_url = 'http://160.25.233.54:3000/payments';
    $response = file_get_contents($payment_api_url);
    
    if (!$response) {
        echo "❌ Cannot connect to API<br>";
        exit;
    }
    
    $payments = json_decode($response, true);
    $hash_part = substr($target_transaction, strlen($server)); // BDEA4882
    
    echo "<p><strong>Transaction Code:</strong> <code>$target_transaction</code></p>";
    echo "<p><strong>Hash Part:</strong> <code>$hash_part</code></p>";
    echo "<p><strong>Expected Amount:</strong> " . number_format($target_amount) . " VNĐ</p>";
    
    // 2. Find matching payments
    echo "<h3>2. Search for Matching Payments:</h3>";
    
    $found_payments = [];
    
    foreach ($payments as $payment) {
        $content = $payment['content'] ?? '';
        $amount = $payment['amount'] ?? 0;
        
        // Apply new matching logic
        $content_match = false;
        $match_reason = '';
        
        // Kiểm tra mã giao dịch đầy đủ (case insensitive)
        if (stripos($content, $target_transaction) !== false) {
            $content_match = true;
            $match_reason = 'Full transaction code';
        }
        // Kiểm tra với uppercase
        elseif (stripos($content, strtoupper($target_transaction)) !== false) {
            $content_match = true;
            $match_reason = 'Full transaction code (uppercase)';
        }
        // Kiểm tra CHỈ phần hash (8 ký tự)
        elseif (stripos($content, $hash_part) !== false) {
            $content_match = true;
            $match_reason = 'Hash part (' . $hash_part . ')';
        }
        // Kiểm tra hash uppercase
        elseif (stripos($content, strtoupper($hash_part)) !== false) {
            $content_match = true;
            $match_reason = 'Hash part uppercase (' . strtoupper($hash_part) . ')';
        }
        
        // Check amount and time
        $amount_match = ($amount == $target_amount);
        $time_match = (time() - strtotime($payment['date']) < 600); // 10 phút
        
        if ($content_match && $amount_match && $time_match) {
            $found_payments[] = [
                'payment' => $payment,
                'match_reason' => $match_reason
            ];
        }
    }
    
    // 3. Display results
    if ($found_payments) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 5px solid #28a745;'>";
        echo "<h4>✅ Found " . count($found_payments) . " matching payment(s)!</h4>";
        
        foreach ($found_payments as $i => $found) {
            $payment = $found['payment'];
            $match_reason = $found['match_reason'];
            
            echo "<h5>Payment " . ($i + 1) . ":</h5>";
            echo "<ul>";
            echo "<li><strong>Transaction ID:</strong> <code>{$payment['transaction_id']}</code></li>";
            echo "<li><strong>Amount:</strong> " . number_format($payment['amount']) . " VNĐ</li>";
            echo "<li><strong>Date:</strong> {$payment['date']}</li>";
            echo "<li><strong>Content:</strong> <code>" . htmlspecialchars($payment['content']) . "</code></li>";
            echo "<li><strong>Match Reason:</strong> <span style='color: #28a745; font-weight: bold;'>$match_reason</span></li>";
            echo "</ul>";
        }
        echo "</div>";
        
        // 4. Test actual API call
        echo "<h3>3. Test Actual API Call:</h3>";
        session_start();
        
        if (isset($_SESSION['user_id'])) {
            // Create fake session for testing
            $_SESSION['pending_transaction'] = [
                'transaction_code' => $target_transaction,
                'amount' => $target_amount,
                'server' => $server,
                'created_at' => time()
            ];
            
            echo "<button onclick='testAPICall()' style='background: #007bff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;'>Test API Call</button>";
            echo "<div id='api-result' style='margin-top: 10px;'></div>";
            
            echo "<script>
            function testAPICall() {
                const resultDiv = document.getElementById('api-result');
                resultDiv.innerHTML = '⏳ Testing API call...';
                
                fetch('/api/check-payment.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        transaction_code: '$target_transaction'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    let resultHTML = '<h4>API Response:</h4>';
                    resultHTML += '<pre style=\"background: #f8f9fa; padding: 10px; border-radius: 4px; border: 1px solid #ddd;\">' + JSON.stringify(data, null, 2) + '</pre>';
                    
                    if (data.success) {
                        resultHTML += '<div style=\"background: #d4edda; padding: 10px; border-radius: 4px; color: #155724; margin-top: 10px;\"><strong>✅ SUCCESS!</strong> Payment would be processed!</div>';
                    } else {
                        resultHTML += '<div style=\"background: #f8d7da; padding: 10px; border-radius: 4px; color: #721c24; margin-top: 10px;\"><strong>❌ FAILED:</strong> ' + data.message + '</div>';
                    }
                    
                    resultDiv.innerHTML = resultHTML;
                })
                .catch(error => {
                    resultDiv.innerHTML = '<div style=\"color: red;\">❌ Error: ' + error.message + '</div>';
                });
            }
            </script>";
        } else {
            echo "<p>❌ User not logged in - cannot test API call</p>";
        }
        
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border-left: 5px solid #dc3545;'>";
        echo "<h4>❌ No matching payments found</h4>";
        echo "<p>Possible reasons:</p>";
        echo "<ul>";
        echo "<li>Payment is older than 10 minutes</li>";
        echo "<li>Amount doesn't match</li>";
        echo "<li>Content doesn't contain the hash</li>";
        echo "</ul>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}

echo "<hr>";
echo "<p><strong>Sau khi test xong, xóa file này!</strong></p>";
?>

<style>
h3 {
    color: #333;
    margin-top: 20px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}

code {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
}

ul {
    margin: 10px 0;
}

li {
    margin: 5px 0;
}

pre {
    font-size: 12px;
    max-height: 300px;
    overflow-y: auto;
}
</style>
