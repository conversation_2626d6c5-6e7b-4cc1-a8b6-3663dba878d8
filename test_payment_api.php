<?php
// Test script để kiểm tra API thanh toán
echo "<h2>Test Payment API</h2>";

// Test 1: Kiểm tra kết nối API
echo "<h3>1. Test kết nối API:</h3>";

try {
    $payment_api_url = 'http://*************:3000/payments';
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET',
            'header' => [
                'User-Agent: Mozilla/5.0 (compatible; Payment-Checker/1.0)',
                'Accept: application/json'
            ]
        ]
    ]);
    
    echo "Đang kết nối đến: $payment_api_url<br>";
    
    $start_time = microtime(true);
    $response = file_get_contents($payment_api_url, false, $context);
    $end_time = microtime(true);
    
    $response_time = round(($end_time - $start_time) * 1000, 2);
    
    if ($response === false) {
        $error = error_get_last();
        echo "❌ <strong>Lỗi kết nối:</strong> " . ($error['message'] ?? 'Unknown error') . "<br>";
    } else {
        echo "✅ <strong>Kết nối thành công!</strong><br>";
        echo "📊 <strong>Thời gian phản hồi:</strong> {$response_time}ms<br>";
        echo "📏 <strong>Kích thước dữ liệu:</strong> " . strlen($response) . " bytes<br>";
        
        // Test parse JSON
        $payments = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo "❌ <strong>Lỗi parse JSON:</strong> " . json_last_error_msg() . "<br>";
            echo "<strong>Raw response (first 500 chars):</strong><br>";
            echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "</pre>";
        } else {
            echo "✅ <strong>Parse JSON thành công!</strong><br>";
            
            if (is_array($payments)) {
                echo "📊 <strong>Số lượng giao dịch:</strong> " . count($payments) . "<br>";
                
                if (count($payments) > 0) {
                    echo "<h4>Giao dịch mới nhất:</h4>";
                    $latest = $payments[0];
                    echo "<table border='1' style='border-collapse: collapse;'>";
                    echo "<tr><th>Field</th><th>Value</th></tr>";
                    foreach ($latest as $key => $value) {
                        echo "<tr><td>$key</td><td>" . htmlspecialchars($value) . "</td></tr>";
                    }
                    echo "</table>";
                }
            } else {
                echo "❌ <strong>Dữ liệu không phải array</strong><br>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "❌ <strong>Exception:</strong> " . $e->getMessage() . "<br>";
}

// Test 2: Kiểm tra database connection
echo "<h3>2. Test kết nối Database:</h3>";

try {
    require_once 'config/config.php';
    echo "✅ <strong>Kết nối database thành công!</strong><br>";
    
    // Test bảng pending
    $stmt = $conn->query("SELECT COUNT(*) as count FROM nlogin_qr_pending");
    $pending_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "📊 <strong>Giao dịch pending:</strong> $pending_count<br>";
    
    // Test bảng transactions
    $stmt = $conn->query("SELECT COUNT(*) as count FROM nlogin_qr_transactions");
    $completed_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "📊 <strong>Giao dịch hoàn thành:</strong> $completed_count<br>";
    
} catch (Exception $e) {
    echo "❌ <strong>Lỗi database:</strong> " . $e->getMessage() . "<br>";
}

// Test 3: Test session
echo "<h3>3. Test Session:</h3>";

session_start();
if (isset($_SESSION['user_id'])) {
    echo "✅ <strong>User đã đăng nhập:</strong> " . $_SESSION['user_id'] . "<br>";
    
    if (isset($_SESSION['pending_transaction'])) {
        echo "📋 <strong>Có giao dịch pending:</strong><br>";
        echo "<pre>" . print_r($_SESSION['pending_transaction'], true) . "</pre>";
    } else {
        echo "ℹ️ <strong>Không có giao dịch pending</strong><br>";
    }
} else {
    echo "❌ <strong>User chưa đăng nhập</strong><br>";
}

// Test 4: Test API endpoint
echo "<h3>4. Test API Endpoint:</h3>";

if (isset($_SESSION['user_id']) && isset($_SESSION['pending_transaction'])) {
    $transaction_code = $_SESSION['pending_transaction']['transaction_code'];
    
    echo "<button onclick='testAPI()'>Test API Check Payment</button><br>";
    echo "<div id='api-result'></div>";
    
    echo "<script>
    function testAPI() {
        const resultDiv = document.getElementById('api-result');
        resultDiv.innerHTML = '⏳ Đang test...';
        
        fetch('/api/check-payment.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                transaction_code: '$transaction_code'
            })
        })
        .then(response => response.json())
        .then(data => {
            resultDiv.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        })
        .catch(error => {
            resultDiv.innerHTML = '❌ Error: ' + error.message;
        });
    }
    </script>";
} else {
    echo "ℹ️ <strong>Cần đăng nhập và có giao dịch pending để test API</strong><br>";
}

echo "<hr>";
echo "<p><strong>Hướng dẫn:</strong></p>";
echo "<ol>";
echo "<li>Nếu API kết nối thất bại, kiểm tra firewall và network</li>";
echo "<li>Nếu database lỗi, kiểm tra config.php</li>";
echo "<li>Nếu session lỗi, thử đăng nhập lại</li>";
echo "<li>Sau khi test xong, xóa file này</li>";
echo "</ol>";
?>

<style>
table {
    border-collapse: collapse;
    margin: 10px 0;
}

table th, table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

table th {
    background-color: #f2f2f2;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
}

button {
    background: #007bff;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    margin: 10px 0;
}

button:hover {
    background: #0056b3;
}
</style>
