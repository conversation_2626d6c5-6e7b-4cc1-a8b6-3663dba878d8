<?php
// Script để cleanup conflicts
require_once 'config/config.php';

header('Content-Type: text/plain');

try {
    // 1. <PERSON><PERSON>a các giao dịch completed cũ hơn 24 giờ
    $stmt = $conn->prepare("
        DELETE FROM nlogin_qr_transactions 
        WHERE created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ");
    $stmt->execute();
    $deleted_old = $stmt->rowCount();
    
    echo "Deleted $deleted_old old completed transactions (>24h)\n";
    
    // 2. Tìm và xóa duplicate transaction_ids
    $stmt = $conn->query("
        SELECT transaction_id, COUNT(*) as count 
        FROM nlogin_qr_transactions 
        GROUP BY transaction_id 
        HAVING COUNT(*) > 1
    ");
    $duplicates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $deleted_duplicates = 0;
    foreach ($duplicates as $dup) {
        $transaction_id = $dup['transaction_id'];
        
        // Giữ lại record mới nhất, xóa các record cũ
        $stmt = $conn->prepare("
            DELETE FROM nlogin_qr_transactions 
            WHERE transaction_id = :transaction_id 
            AND id NOT IN (
                SELECT * FROM (
                    SELECT MAX(id) FROM nlogin_qr_transactions 
                    WHERE transaction_id = :transaction_id2
                ) as temp
            )
        ");
        $stmt->bindParam(':transaction_id', $transaction_id);
        $stmt->bindParam(':transaction_id2', $transaction_id);
        $stmt->execute();
        
        $deleted_duplicates += $stmt->rowCount();
    }
    
    echo "Deleted $deleted_duplicates duplicate transactions\n";
    
    // 3. Reset pending transactions về unprocessed nếu chưa quá 2 phút
    $stmt = $conn->prepare("
        UPDATE nlogin_qr_pending 
        SET processed = 0, processed_at = NULL 
        WHERE processed = 1 
        AND created_at > DATE_SUB(NOW(), INTERVAL 2 MINUTE)
    ");
    $stmt->execute();
    $reset_count = $stmt->rowCount();
    
    echo "Reset $reset_count recent pending transactions to unprocessed\n";
    
    echo "Cleanup completed successfully!";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
