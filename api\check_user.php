<?php
// Include necessary files
require_once '../includes/config.php';
require_once '../includes/functions.php';
require_once '../includes/nlogin/nlogin.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get data from POST request
    $field = isset($_POST['field']) ? $_POST['field'] : '';
    $value = isset($_POST['value']) ? $_POST['value'] : '';

    // Validate input
    if (empty($field) || empty($value)) {
        echo json_encode(['status' => false, 'message' => 'Thiếu thông tin']);
        exit;
    }

    // Sanitize input
    $value = sanitize($value);

    try {
        // Check if username or email exists
        if ($field === 'username') {
            $stmt = $conn->prepare("SELECT * FROM nlogin WHERE unique_id = :value OR last_name = :value");
            $stmt->bindParam(':value', $value);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                // Kiểm tra xem trùng unique_id hay last_name
                if ($user['unique_id'] === $value) {
                    echo json_encode(['status' => false, 'message' => 'Mã định danh đã tồn tại']);
                } else {
                    echo json_encode(['status' => false, 'message' => 'Tên tài khoản đã tồn tại']);
                }
                exit;
            }
        } elseif ($field === 'email') {
            $stmt = $conn->prepare("SELECT * FROM nlogin WHERE email = :value");
            $stmt->bindParam(':value', $value);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                echo json_encode(['status' => false, 'message' => 'Email đã được sử dụng']);
                exit;
            }
        } else {
            echo json_encode(['status' => false, 'message' => 'Trường không hợp lệ']);
            exit;
        }

        // User does not exist
        echo json_encode(['status' => true, 'message' => 'Hợp lệ']);
    } catch(PDOException $e) {
        echo json_encode(['status' => false, 'message' => 'Lỗi hệ thống']);
    }
} else {
    // Method not allowed
    http_response_code(405);
    echo json_encode(['status' => false, 'message' => 'Phương thức không được phép']);
}
?>
