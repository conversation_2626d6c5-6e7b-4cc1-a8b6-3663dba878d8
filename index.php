<?php
session_start();
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/nlogin/nlogin.php';

// <PERSON><PERSON>c định trang hiện tại từ URL
$request_uri = $_SERVER['REQUEST_URI'];
$uri_parts = explode('?', $request_uri, 2);
$path = trim($uri_parts[0], '/');

// Nếu path rỗng, đặt trang mặc định là 'home'
$page = $path ? $path : 'home';

// X<PERSON> lý các tham số query nếu có
if (isset($uri_parts[1])) {
    parse_str($uri_parts[1], $query_params);
    $_GET = array_merge($_GET, $query_params);
}

// Xử lý các trường hợp đặc biệt
if (strpos($page, 'reset-password/') === 0) {
    $token = substr($page, strlen('reset-password/'));
    $page = 'reset_password';
    $_GET['token'] = $token;
}

// Chuyển đổi tên trang từ dạng URL sang tên file
$page_mapping = [
    'forgot-password' => 'forgot_password',
    'reset-password' => 'reset_password',
    'add-email' => 'add_email',
    'verify-otp' => 'verify_otp',
    'verify-email' => 'verify_email',
    'verify-add-email' => 'verify_add_email',
    'recharge-history' => 'recharge_history'
];

if (isset($page_mapping[$page])) {
    $page = $page_mapping[$page];
}

// Check if user is logged in
$loggedIn = isset($_SESSION['user_id']);

// Include header
include 'includes/header.php';

// Include appropriate page content
switch ($page) {
    case 'login':
        include 'pages/login.php';
        break;
    case 'register':
        include 'pages/register.php';
        break;
    case 'account':
        // Redirect to login if not logged in
        if (!$loggedIn) {
            header('Location: /login');
            exit;
        }
        include 'pages/account.php';
        break;
    case 'profile':
        // Redirect to login if not logged in
        if (!$loggedIn) {
            header('Location: /login');
            exit;
        }
        include 'pages/profile.php';
        break;
    case 'logout':
        include 'pages/logout.php';
        break;
    case 'forgot_password':
        // Redirect to account if already logged in
        if ($loggedIn) {
            header('Location: /account');
            exit;
        }
        include 'pages/forgot_password.php';
        break;
    case 'verify_otp':
        // Redirect to account if already logged in
        if ($loggedIn) {
            header('Location: /account');
            exit;
        }
        include 'pages/verify_otp.php';
        break;
    case 'reset_password':
        // Redirect to account if already logged in
        if ($loggedIn) {
            header('Location: /account');
            exit;
        }
        include 'pages/reset_password.php';
        break;
    case 'verify_email':
        // Redirect to account if already logged in
        if ($loggedIn) {
            header('Location: /account');
            exit;
        }
        include 'pages/verify_email.php';
        break;
    case 'add_email':
        // Redirect to login if not logged in
        if (!$loggedIn) {
            header('Location: /login');
            exit;
        }
        include 'pages/add_email.php';
        break;
    case 'verify_add_email':
        // Redirect to login if not logged in
        if (!$loggedIn) {
            header('Location: /login');
            exit;
        }
        include 'pages/verify_add_email.php';
        break;
    case 'recharge_history':
        // Redirect to login if not logged in
        if (!$loggedIn) {
            header('Location: /login');
            exit;
        }
        include 'pages/recharge_history.php';
        break;
    default:
        include 'pages/home.php';
        break;
}

// Include footer
include 'includes/footer.php';
?>
