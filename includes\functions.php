<?php
/**
 * General utility functions for the website
 */

/**
 * Sanitize user input
 *
 * @param string $data Data to sanitize
 * @return string Sanitized data
 */
function sanitize($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * Check if user is logged in
 *
 * @return bool True if user is logged in, false otherwise
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

/**
 * Get user data from database
 *
 * @param int $user_id User ID
 * @return array|bool User data or false if user not found
 */
function getUserData($user_id) {
    global $conn, $nlogin;

    // Use the nlogin plugin to get user data
    return $nlogin->getUserById($user_id);
}

/**
 * Get login history for a user
 *
 * @param int $user_id User ID
 * @param int $limit Maximum number of entries to return
 * @return array Login history
 */
function getLoginHistory($user_id, $limit = 10) {
    global $conn;

    $history = [];

    try {
        // Get last_seen from nlogin table
        $stmt = $conn->prepare("SELECT last_seen, last_ip FROM nlogin WHERE ai = :id");
        $stmt->bindParam(':id', $user_id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $userData = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!empty($userData['last_seen'])) {
                $history[] = [
                    'time' => $userData['last_seen'],
                    'ip' => $userData['last_ip'],
                    'country' => getCountryFromIP($userData['last_ip']),
                    'source' => 'Website'
                ];
            }
        }

        // Get additional login history from nlogin_data
        try {
            $stmt = $conn->prepare("SELECT `key`, value FROM nlogin_data WHERE `key` LIKE 'login_history_" . $user_id . "_%' ORDER BY `key` DESC LIMIT :limit");
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            error_log("DEBUG - Login history query executed, rowCount: " . $stmt->rowCount());
        } catch(PDOException $e) {
            error_log("DEBUG - Error getting login history: " . $e->getMessage());
            return $history; // Return what we have so far
        }

        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $loginData = json_decode($row['value'], true);
            if ($loginData) {
                $history[] = [
                    'time' => $loginData['time'],
                    'ip' => $loginData['ip'],
                    'country' => getCountryFromIP($loginData['ip']),
                    'source' => $loginData['source'] ?? 'Website'
                ];
            }
        }

        // Sort by time descending
        usort($history, function($a, $b) {
            return strtotime($b['time']) - strtotime($a['time']);
        });

        // Limit the number of entries
        return array_slice($history, 0, $limit);
    } catch(PDOException $e) {
        return [];
    }
}

/**
 * Get password change history for a user
 *
 * @param int $user_id User ID
 * @param int $limit Maximum number of entries to return
 * @return array Password change history
 */
function getPasswordChangeHistory($user_id, $limit = 10) {
    global $conn;

    $history = [];

    try {
        // Get password change history from nlogin_data
        try {
            $stmt = $conn->prepare("SELECT `key`, value FROM nlogin_data WHERE `key` LIKE 'password_change_" . $user_id . "_%' ORDER BY `key` DESC LIMIT :limit");
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            error_log("DEBUG - Password change history query executed, rowCount: " . $stmt->rowCount());
        } catch(PDOException $e) {
            error_log("DEBUG - Error getting password change history: " . $e->getMessage());
            return $history; // Return empty array
        }

        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $changeData = json_decode($row['value'], true);
            if ($changeData) {
                $history[] = [
                    'date' => $changeData['date'],
                    'ip' => $changeData['ip'],
                    'country' => getCountryFromIP($changeData['ip'])
                ];
            }
        }

        // Sort by date descending
        usort($history, function($a, $b) {
            return strtotime($b['date']) - strtotime($a['date']);
        });

        return $history;
    } catch(PDOException $e) {
        return [];
    }
}

/**
 * Get country code from IP address
 *
 * @param string $ip IP address
 * @return string Two-letter country code
 */
function getCountryFromIP($ip) {
    // Simple check for localhost
    if ($ip == '127.0.0.1' || $ip == '::1') {
        return 'LO';
    }

    // Check if it's an IPv6 address
    if (strpos($ip, ':') !== false) {
        // For simplicity, we'll just return a default for IPv6
        return 'VN';
    }

    // For simplicity, we'll just return VN for all IPs
    // In a real application, you would use a GeoIP database or API
    return 'VN';
}

/**
 * Update user profile data
 *
 * @param int $user_id User ID
 * @param array $data Data to update
 * @return bool True on success, false on failure
 */
/**
 * Get Gravatar URL for a specified email address
 *
 * @param string $email The email address
 * @param int $size Size in pixels, defaults to 80px
 * @param string $default Default image set to use
 * @param string $rating Rating to use
 * @return string URL to the Gravatar image
 */
function getGravatarUrl($email, $size = 80, $default = 'mp', $rating = 'g') {
    $url = 'https://www.gravatar.com/avatar/';
    $url .= md5(strtolower(trim($email)));
    $url .= "?s=$size&d=$default&r=$rating";
    return $url;
}

/**
 * Get Minecraft avatar URL from mc-heads.net
 *
 * @param string $username Minecraft username
 * @param string $type Type of image (avatar, head, body, etc.)
 * @param int $size Size in pixels (only for avatar type)
 * @return string URL to the Minecraft avatar image
 */
function getMinecraftAvatarUrl($username, $type = 'avatar', $size = 100) {
    // Sanitize username
    $username = preg_replace('/[^a-zA-Z0-9_]/', '', $username);

    // Default to avatar if invalid type is provided
    $validTypes = ['avatar', 'head', 'body', 'skin', 'cube', 'bust', 'armor/bust', 'armor/body'];
    if (!in_array($type, $validTypes)) {
        $type = 'avatar';
    }

    // Build URL based on type
    switch ($type) {
        case 'avatar':
            return "https://mc-heads.net/avatar/{$username}/{$size}";
        case 'head':
            return "https://mc-heads.net/head/{$username}";
        case 'body':
            return "https://mc-heads.net/body/{$username}/right";
        case 'skin':
            return "https://mc-heads.net/skin/{$username}";
        case 'cube':
            return "https://mc-heads.net/cube/{$username}";
        case 'bust':
            return "https://mc-heads.net/bust/{$username}";
        case 'armor/bust':
            return "https://mc-heads.net/armor/bust/{$username}";
        case 'armor/body':
            return "https://mc-heads.net/armor/body/{$username}/right";
        default:
            return "https://mc-heads.net/avatar/{$username}/{$size}";
    }
}

/**
 * Change user password
 *
 * @param int $user_id User ID
 * @param string $current_password Current password
 * @param string $new_password New password
 * @return array Result with status and message
 */
function changeUserPassword($user_id, $current_password, $new_password) {
    global $conn, $nlogin;

    // Verify current password
    if (!$nlogin->verifyPassword($user_id, $current_password)) {
        return [
            'status' => false,
            'message' => 'Mật khẩu hiện tại không chính xác'
        ];
    }

    // Change password
    if ($nlogin->changePassword($user_id, $new_password)) {
        // Log password change
        $now = date('Y-m-d H:i:s');
        $ip = $_SERVER['REMOTE_ADDR'];

        // Store password change history in nlogin_data
        $historyKey = 'password_change_' . $user_id . '_' . time();
        $historyValue = json_encode([
            'date' => $now,
            'ip' => $ip,
            'user_id' => $user_id
        ]);

        try {
            // Thêm mới với key duy nhất
            $insertStmt = $conn->prepare("INSERT INTO nlogin_data (`key`, value) VALUES (:key, :value)");
            $insertStmt->bindParam(':key', $historyKey);
            $insertStmt->bindParam(':value', $historyValue);
            $insertStmt->execute();

            error_log("DEBUG - Password change history saved successfully with key: " . $historyKey);
        } catch(PDOException $e) {
            // Log lỗi nhưng vẫn tiếp tục
            error_log("DEBUG - Error saving password change history: " . $e->getMessage());
        }

        return [
            'status' => true,
            'message' => 'Mật khẩu đã được thay đổi thành công'
        ];
    }

    return [
        'status' => false,
        'message' => 'Không thể thay đổi mật khẩu. Vui lòng thử lại sau.'
    ];
}

/**
 * Generate a random token
 *
 * @param int $length Token length
 * @return string Random token
 */
function generateToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Create an email verification token for a user
 *
 * @param int $user_id User ID
 * @param string $email User email
 * @param string $username Username
 * @param string $type Type of verification (register, add_email, etc.)
 * @return array Result with status, message, and token data
 */
function createEmailVerificationToken($user_id, $email, $username, $type = 'register') {
    global $conn;

    try {
        // Generate 6-digit OTP code
        $otp = sprintf("%06d", mt_rand(0, 999999));
        $token = generateToken(); // Generate a token for the verification URL
        $expires = date('Y-m-d H:i:s', strtotime('+30 minutes'));

        // Store token and OTP in database
        $tokenData = json_encode([
            'token' => $token,
            'otp' => $otp,
            'expires' => $expires,
            'used' => false,
            'verified' => false,
            'user_id' => $user_id,
            'email' => $email,
            'username' => $username,
            'type' => $type
        ]);

        // Create a new record with auto-incrementing ID
        $insertStmt = $conn->prepare("INSERT INTO nlogin_data (`key`, value) VALUES (:key, :value)");
        $verifyKey = 'email_verification_token_' . $type . '_' . $user_id . '_' . time(); // Create a unique key
        $insertStmt->bindParam(':key', $verifyKey);
        $insertStmt->bindParam(':value', $tokenData);
        $insertStmt->execute();

        // Return success immediately to improve user experience
        // Email will be sent in the background

        // Start a background process to send the email
        if (function_exists('fastcgi_finish_request')) {
            // For PHP-FPM, we can use fastcgi_finish_request to send the response to the browser
            // and continue processing in the background
            register_shutdown_function(function() use ($email, $username, $otp) {
                require_once __DIR__ . '/phpmailer/mailer.php';
                sendEmailVerificationOTP($email, $username, $otp);
            });
        } else {
            // For non-FPM environments, we'll still send the email but it will block the response
            require_once __DIR__ . '/phpmailer/mailer.php';
            sendEmailVerificationOTP($email, $username, $otp);
        }

        return [
            'status' => true,
            'message' => 'Mã xác minh email đã được tạo',
            'data' => [
                'user_id' => $user_id,
                'username' => $username,
                'email' => $email,
                'token' => $token,
                'otp' => $otp,
                'expires' => $expires
            ]
        ];
    } catch(PDOException $e) {
        return [
            'status' => false,
            'message' => 'Lỗi khi tạo mã xác minh email: ' . $e->getMessage()
        ];
    }
}

/**
 * Create a password reset token for a user
 *
 * @param string $email User email
 * @return array Result with status, message, and token data
 */
function createPasswordResetToken($email) {
    global $conn, $nlogin;

    try {
        // Find user by email
        $stmt = $conn->prepare("SELECT * FROM nlogin WHERE email = :email");
        $stmt->bindParam(':email', $email);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            return [
                'status' => false,
                'message' => 'Không tìm thấy tài khoản với email này'
            ];
        }

        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        $user_id = $user['ai'];
        $username = isset($user['last_name']) ? $user['last_name'] : $user['unique_id'];

        // Check if email is empty
        if (empty($user['email'])) {
            return [
                'status' => false,
                'message' => 'Tài khoản này không có địa chỉ email'
            ];
        }

        // Generate 6-digit OTP code
        $otp = sprintf("%06d", mt_rand(0, 999999));
        $token = generateToken(); // Still generate a token for the reset URL
        $expires = date('Y-m-d H:i:s', strtotime('+30 minutes'));

        // Store token and OTP in database
        $tokenData = json_encode([
            'token' => $token,
            'otp' => $otp,
            'expires' => $expires,
            'used' => false,
            'verified' => false
        ]);

        // Kiểm tra cấu trúc bảng nlogin_data
        try {
            $describeStmt = $conn->prepare("DESCRIBE nlogin_data");
            $describeStmt->execute();
            $columns = $describeStmt->fetchAll(PDO::FETCH_ASSOC);
        } catch(PDOException $e) {
            // Bỏ qua lỗi
        }

        // Kiểm tra xem có bản ghi nào trong bảng nlogin_data không
        try {
            $countStmt = $conn->prepare("SELECT COUNT(*) FROM nlogin_data");
            $countStmt->execute();
            $count = $countStmt->fetchColumn();
        } catch(PDOException $e) {
            // Bỏ qua lỗi
        }

        // Thử xóa token cũ trước khi tạo mới
        try {
            $deleteStmt = $conn->prepare("DELETE FROM nlogin_data WHERE id = :id AND `key` = 'password_reset_token'");
            $deleteStmt->bindParam(':id', $user_id);
            $deleteStmt->execute();
        } catch(PDOException $e) {
            // Bỏ qua lỗi
        }

        // Tạo token mới
        try {
            // Tạo một bản ghi mới với ID tự động tăng
            $insertStmt = $conn->prepare("INSERT INTO nlogin_data (`key`, value) VALUES (:key, :value)");
            $resetKey = 'password_reset_token_' . $user_id . '_' . time(); // Tạo key duy nhất
            $insertStmt->bindParam(':key', $resetKey);
            $insertStmt->bindParam(':value', $tokenData);
            $insertStmt->execute();

            // Lưu thông tin người dùng vào token data
            $tokenDataObj = json_decode($tokenData, true);
            $tokenDataObj['user_id'] = $user_id;
            $tokenData = json_encode($tokenDataObj);

            // Cập nhật lại token data với user_id
            $updateStmt = $conn->prepare("UPDATE nlogin_data SET value = :value WHERE `key` = :key");
            $updateStmt->bindParam(':value', $tokenData);
            $updateStmt->bindParam(':key', $resetKey);
            $updateStmt->execute();
        } catch(PDOException $e) {
            throw $e; // Re-throw to be caught by the outer try-catch
        }

        return [
            'status' => true,
            'message' => 'Mã đặt lại mật khẩu đã được tạo',
            'data' => [
                'user_id' => $user_id,
                'username' => $username,
                'email' => $email,
                'token' => $token,
                'otp' => $otp,
                'expires' => $expires
            ]
        ];
    } catch(PDOException $e) {
        return [
            'status' => false,
            'message' => 'Lỗi khi tạo mã đặt lại mật khẩu: ' . $e->getMessage()
        ];
    }
}

/**
 * Verify a password reset token
 *
 * @param string $token Reset token
 * @return array Result with status, message, and user data
 */
function verifyPasswordResetToken($token) {
    global $conn;

    try {
        // Find token in database - search in all password_reset_token keys
        $stmt = $conn->prepare("SELECT `key`, value FROM nlogin_data WHERE `key` LIKE 'password_reset_token_%'");
        $stmt->execute();

        // Tìm token trong database

        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $tokenData = json_decode($row['value'], true);

            // Kiểm tra token data

            if (isset($tokenData['token']) && $tokenData['token'] === $token) {
                // Check if token is expired
                if (strtotime($tokenData['expires']) < time()) {
                    return [
                        'status' => false,
                        'message' => 'Mã đặt lại mật khẩu đã hết hạn'
                    ];
                }

                // Check if token has been used
                if (isset($tokenData['used']) && $tokenData['used']) {
                    return [
                        'status' => false,
                        'message' => 'Mã đặt lại mật khẩu đã được sử dụng'
                    ];
                }

                // Get user data from token
                if (isset($tokenData['user_id'])) {
                    $user_id = $tokenData['user_id'];
                } else {
                    // For backward compatibility - extract user_id from key
                    $keyParts = explode('_', $row['key']);
                    if (count($keyParts) >= 3) {
                        $user_id = $keyParts[2];
                    } else {
                        continue;
                    }
                }

                // Token hợp lệ cho user_id

                $userStmt = $conn->prepare("SELECT * FROM nlogin WHERE ai = :id");
                $userStmt->bindParam(':id', $user_id);
                $userStmt->execute();

                if ($userStmt->rowCount() > 0) {
                    $user = $userStmt->fetch(PDO::FETCH_ASSOC);

                    // Người dùng được tìm thấy

                    return [
                        'status' => true,
                        'message' => 'Mã đặt lại mật khẩu hợp lệ',
                        'data' => [
                            'user_id' => $user_id,
                            'username' => $user['last_name'] ?? $user['unique_id'],
                            'email' => $user['email'],
                            'token' => $token,
                            'token_key' => $row['key'] // Lưu lại key để cập nhật token
                        ]
                    ];
                } else {
                    // Không tìm thấy người dùng
                }
            }
        }

        return [
            'status' => false,
            'message' => 'Mã đặt lại mật khẩu không hợp lệ'
        ];
    } catch(PDOException $e) {
        return [
            'status' => false,
            'message' => 'Lỗi khi xác minh mã đặt lại mật khẩu: ' . $e->getMessage()
        ];
    }
}

/**
 * Reset password using token
 *
 * @param string $token Reset token
 * @param string $new_password New password
 * @return array Result with status and message
 */
function resetPasswordWithToken($token, $new_password) {
    global $conn, $nlogin;

    // Verify token
    $result = verifyPasswordResetToken($token);

    if (!$result['status']) {
        return $result;
    }

    $user_id = $result['data']['user_id'];

    // Change password
    if ($nlogin->changePassword($user_id, $new_password)) {
        // Mark token as used
        $token_key = $result['data']['token_key'];

        // Đánh dấu token đã được sử dụng

        $stmt = $conn->prepare("SELECT value FROM nlogin_data WHERE `key` = :key");
        $stmt->bindParam(':key', $token_key);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $tokenData = json_decode($row['value'], true);
            $tokenData['used'] = true;

            $updateStmt = $conn->prepare("UPDATE nlogin_data SET value = :value WHERE `key` = :key");
            $updateStmt->bindParam(':value', json_encode($tokenData));
            $updateStmt->bindParam(':key', $token_key);
            $updateStmt->execute();

            // Token đã được đánh dấu là đã sử dụng
        } else {
            // Không tìm thấy token
        }

        // Log password change
        $now = date('Y-m-d H:i:s');
        $ip = $_SERVER['REMOTE_ADDR'];

        // Store password change history
        $historyKey = 'password_change_' . $user_id . '_' . time();
        $historyValue = json_encode([
            'date' => $now,
            'ip' => $ip,
            'method' => 'reset',
            'user_id' => $user_id
        ]);

        try {
            // Chèn mới với key duy nhất
            $insertStmt = $conn->prepare("INSERT INTO nlogin_data (`key`, value) VALUES (:key, :value)");
            $insertStmt->bindParam(':key', $historyKey);
            $insertStmt->bindParam(':value', $historyValue);
            $insertStmt->execute();

            // Lưu lịch sử thay đổi mật khẩu thành công
        } catch(PDOException $e) {
            // Bỏ qua lỗi và tiếp tục
        }

        return [
            'status' => true,
            'message' => 'Mật khẩu đã được đặt lại thành công'
        ];
    }

    return [
        'status' => false,
        'message' => 'Không thể đặt lại mật khẩu. Vui lòng thử lại sau.'
    ];
}

function updateUserProfile($user_id, $data) {
    global $conn;

    try {
        $success = true;

        // Các trường cơ bản trong bảng nlogin
        $mainTableFields = ['last_name', 'email'];
        $mainTableData = [];
        $additionalData = [];

        // Phân loại dữ liệu
        if (is_array($data) || is_object($data)) {
            foreach ($data as $key => $value) {
                if (in_array($key, $mainTableFields)) {
                    $mainTableData[$key] = $value;
                } else {
                    $additionalData[$key] = $value;
                }
            }
        }

        // Cập nhật bảng chính nếu có dữ liệu
        if (!empty($mainTableData)) {
            $sql = "UPDATE nlogin SET ";
            $params = [];

            foreach ($mainTableData as $key => $value) {
                $sql .= "$key = :$key, ";
                $params[":$key"] = $value;
            }

            $sql = rtrim($sql, ", ");
            $sql .= " WHERE ai = :id";
            $params[':id'] = $user_id;

            $stmt = $conn->prepare($sql);
            $success = $stmt->execute($params) && $success;
        }

        // Cập nhật dữ liệu bổ sung vào bảng nlogin_data
        foreach ($additionalData as $key => $value) {
            // Kiểm tra xem dữ liệu đã tồn tại chưa
            $checkStmt = $conn->prepare("SELECT * FROM nlogin_data WHERE id = :id AND `key` = :key");
            $checkStmt->bindParam(':id', $user_id);
            $checkStmt->bindParam(':key', $key);
            $checkStmt->execute();

            if ($checkStmt->rowCount() > 0) {
                // Cập nhật dữ liệu hiện có
                $updateStmt = $conn->prepare("UPDATE nlogin_data SET value = :value WHERE id = :id AND `key` = :key");
                $updateStmt->bindParam(':value', $value);
                $updateStmt->bindParam(':id', $user_id);
                $updateStmt->bindParam(':key', $key);
                $success = $updateStmt->execute() && $success;
            } else {
                // Thêm dữ liệu mới
                $insertStmt = $conn->prepare("INSERT INTO nlogin_data (id, `key`, value) VALUES (:id, :key, :value)");
                $insertStmt->bindParam(':id', $user_id);
                $insertStmt->bindParam(':key', $key);
                $insertStmt->bindParam(':value', $value);
                $success = $insertStmt->execute() && $success;
            }
        }

        return $success;
    } catch(PDOException $e) {
        return false;
    }
}

/**
 * Lấy lịch sử nạp thẻ của người dùng
 *
 * @param string $uuid UUID của người dùng
 * @param int $limit Số lượng bản ghi tối đa (mặc định: 50)
 * @param int $offset Vị trí bắt đầu (mặc định: 0)
 * @return array Danh sách lịch sử nạp thẻ
 */
function getCardRechargeHistory($uuid, $limit = 50, $offset = 0) {
    global $card_conn;

    // Kiểm tra kết nối database
    if (!$card_conn) {
        error_log("Card database connection not available");
        return [];
    }

    try {
        // Kiểm tra xem bảng dotman_napthe_log có tồn tại không
        $stmt = $card_conn->prepare("SHOW TABLES LIKE 'dotman_napthe_log'");
        $stmt->execute();
        if ($stmt->rowCount() == 0) {
            return [];
        }

        // Lấy lịch sử nạp thẻ
        $sql = "SELECT * FROM dotman_napthe_log WHERE uuid = :uuid ORDER BY time DESC LIMIT :limit OFFSET :offset";
        $stmt = $card_conn->prepare($sql);
        $stmt->bindParam(':uuid', $uuid);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        $history = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Chuyển đổi timestamp thành định dạng dễ đọc
        foreach ($history as &$record) {
            if (isset($record['time'])) {
                $record['formatted_time'] = date('d/m/Y H:i:s', $record['time'] / 1000);
            }

            // Định dạng giá trị tiền
            if (isset($record['price'])) {
                $record['formatted_price'] = number_format($record['price'], 0, ',', '.') . ' VNĐ';
            }

            // Định dạng trạng thái
            $record['status_text'] = getCardStatusText($record);
            $record['status_class'] = getCardStatusClass($record);

            // Định dạng loại thẻ
            if (isset($record['type'])) {
                $record['type_name'] = getCardTypeName($record['type']);
            }
        }

        return $history;
    } catch(PDOException $e) {
        error_log("Error getting card recharge history: " . $e->getMessage());
        return [];
    }
}

/**
 * Đếm tổng số bản ghi lịch sử nạp thẻ của người dùng
 *
 * @param string $uuid UUID của người dùng
 * @return int Tổng số bản ghi
 */
function getCardRechargeHistoryCount($uuid) {
    global $card_conn;

    // Kiểm tra kết nối database
    if (!$card_conn) {
        error_log("Card database connection not available");
        return 0;
    }

    try {
        // Kiểm tra xem bảng dotman_napthe_log có tồn tại không
        $stmt = $card_conn->prepare("SHOW TABLES LIKE 'dotman_napthe_log'");
        $stmt->execute();
        if ($stmt->rowCount() == 0) {
            return 0;
        }

        $stmt = $card_conn->prepare("SELECT COUNT(*) as count FROM dotman_napthe_log WHERE uuid = :uuid");
        $stmt->bindParam(':uuid', $uuid);
        $stmt->execute();

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'];
    } catch(PDOException $e) {
        error_log("Error counting card recharge history: " . $e->getMessage());
        return 0;
    }
}

/**
 * Lấy text trạng thái thẻ nạp
 *
 * @param array $record Bản ghi thẻ nạp
 * @return string Text trạng thái
 */
function getCardStatusText($record) {
    if ($record['success'] == 1) {
        return 'Thành công';
    } elseif ($record['waiting'] == 1) {
        return 'Đang xử lý';
    } else {
        return 'Thất bại';
    }
}

/**
 * Lấy class CSS cho trạng thái thẻ nạp
 *
 * @param array $record Bản ghi thẻ nạp
 * @return string CSS class
 */
function getCardStatusClass($record) {
    if ($record['success'] == 1) {
        return 'success';
    } elseif ($record['waiting'] == 1) {
        return 'warning';
    } else {
        return 'danger';
    }
}

/**
 * Lấy tên loại thẻ
 *
 * @param int $type Mã loại thẻ
 * @return string Tên loại thẻ
 */
function getCardTypeName($type) {
    $cardTypes = [
        1 => 'Viettel',
        2 => 'Mobifone',
        3 => 'Vinaphone',
        4 => 'Vietnamobile',
        5 => 'Gmobile',
        6 => 'Zing',
        7 => 'Gate',
        8 => 'Vcoin'
    ];

    return isset($cardTypes[$type]) ? $cardTypes[$type] : 'Không xác định';
}

/**
 * Lấy UUID của người dùng từ unique_id hoặc last_name
 *
 * @param int $user_id ID người dùng
 * @return string|null UUID của người dùng
 */
function getUserUUID($user_id) {
    global $conn, $card_conn;

    try {
        // Lấy thông tin người dùng từ bảng nlogin (database chính)
        $stmt = $conn->prepare("SELECT unique_id, last_name FROM nlogin WHERE ai = :user_id");
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            // Ưu tiên unique_id nếu có
            if (!empty($user['unique_id'])) {
                return $user['unique_id'];
            }

            // Nếu không có unique_id, tìm UUID từ bảng dotman_player_info (database lịch sử nạp thẻ)
            if (!empty($user['last_name']) && $card_conn) {
                // Kiểm tra xem bảng dotman_player_info có tồn tại không
                $stmt = $card_conn->prepare("SHOW TABLES LIKE 'dotman_player_info'");
                $stmt->execute();
                if ($stmt->rowCount() > 0) {
                    // Trong dotman_player_info có 2 loại tên:
                    // 1. Có prefix _ (ví dụ: _longhay, _Twefe3088)
                    // 2. Không có prefix (ví dụ: luandeptrai, KronMC, sang)

                    // Thử tìm chính xác trước (case-sensitive)
                    $stmt = $card_conn->prepare("SELECT uuid, name FROM dotman_player_info WHERE name = :name_exact OR name = :name_with_prefix LIMIT 1");
                    $name_exact = $user['last_name'];              // Tên chính xác
                    $name_with_prefix = '_' . $user['last_name'];  // Thêm prefix _
                    $stmt->bindParam(':name_exact', $name_exact);
                    $stmt->bindParam(':name_with_prefix', $name_with_prefix);
                    $stmt->execute();

                    if ($stmt->rowCount() > 0) {
                        $player = $stmt->fetch(PDO::FETCH_ASSOC);
                        return $player['uuid'];
                    }

                    // Nếu không tìm thấy, thử tìm case-insensitive
                    $stmt = $card_conn->prepare("SELECT uuid, name FROM dotman_player_info WHERE LOWER(name) = LOWER(:name_exact) OR LOWER(name) = LOWER(:name_with_prefix) LIMIT 1");
                    $stmt->bindParam(':name_exact', $name_exact);
                    $stmt->bindParam(':name_with_prefix', $name_with_prefix);
                    $stmt->execute();

                    if ($stmt->rowCount() > 0) {
                        $player = $stmt->fetch(PDO::FETCH_ASSOC);
                        return $player['uuid'];
                    }

                    // Cuối cùng, thử tìm bằng LIKE pattern (tìm tên chứa substring)
                    $stmt = $card_conn->prepare("SELECT uuid, name FROM dotman_player_info WHERE LOWER(name) LIKE LOWER(:pattern) LIMIT 1");
                    $pattern = '%' . strtolower($user['last_name']) . '%';
                    $stmt->bindParam(':pattern', $pattern);
                    $stmt->execute();

                    if ($stmt->rowCount() > 0) {
                        $player = $stmt->fetch(PDO::FETCH_ASSOC);
                        return $player['uuid'];
                    }
                }
            }
        }

        return null;
    } catch(PDOException $e) {
        error_log("Error getting user UUID: " . $e->getMessage());
        return null;
    }
}

/**
 * Debug function để kiểm tra mapping tên người dùng
 *
 * @param int $user_id ID người dùng
 * @return array Thông tin debug
 */
function debugUserMapping($user_id) {
    global $conn, $card_conn;

    $debug_info = [
        'user_id' => $user_id,
        'nlogin_data' => null,
        'search_attempts' => [],
        'found_uuid' => null,
        'found_player_name' => null
    ];

    try {
        // Lấy thông tin từ nlogin
        $stmt = $conn->prepare("SELECT unique_id, last_name FROM nlogin WHERE ai = :user_id");
        $stmt->bindParam(':user_id', $user_id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            $debug_info['nlogin_data'] = $user;

            if (!empty($user['unique_id'])) {
                $debug_info['found_uuid'] = $user['unique_id'];
                $debug_info['search_attempts'][] = "Tìm thấy unique_id: " . $user['unique_id'];
                return $debug_info;
            }

            if (!empty($user['last_name']) && $card_conn) {
                $last_name = $user['last_name'];

                // Thử tìm chính xác
                $search_names = [
                    $last_name,
                    '_' . $last_name
                ];

                foreach ($search_names as $search_name) {
                    $stmt = $card_conn->prepare("SELECT uuid, name FROM dotman_player_info WHERE name = :name");
                    $stmt->bindParam(':name', $search_name);
                    $stmt->execute();

                    $debug_info['search_attempts'][] = "Tìm kiếm chính xác: '" . $search_name . "' -> " . ($stmt->rowCount() > 0 ? "Tìm thấy" : "Không tìm thấy");

                    if ($stmt->rowCount() > 0) {
                        $player = $stmt->fetch(PDO::FETCH_ASSOC);
                        $debug_info['found_uuid'] = $player['uuid'];
                        $debug_info['found_player_name'] = $player['name'];
                        return $debug_info;
                    }
                }

                // Thử tìm case-insensitive
                foreach ($search_names as $search_name) {
                    $stmt = $card_conn->prepare("SELECT uuid, name FROM dotman_player_info WHERE LOWER(name) = LOWER(:name)");
                    $stmt->bindParam(':name', $search_name);
                    $stmt->execute();

                    $debug_info['search_attempts'][] = "Tìm kiếm case-insensitive: '" . $search_name . "' -> " . ($stmt->rowCount() > 0 ? "Tìm thấy" : "Không tìm thấy");

                    if ($stmt->rowCount() > 0) {
                        $player = $stmt->fetch(PDO::FETCH_ASSOC);
                        $debug_info['found_uuid'] = $player['uuid'];
                        $debug_info['found_player_name'] = $player['name'];
                        return $debug_info;
                    }
                }

                // Thử tìm LIKE pattern
                $pattern = '%' . strtolower($last_name) . '%';
                $stmt = $card_conn->prepare("SELECT uuid, name FROM dotman_player_info WHERE LOWER(name) LIKE :pattern LIMIT 5");
                $stmt->bindParam(':pattern', $pattern);
                $stmt->execute();

                $matches = $stmt->fetchAll(PDO::FETCH_ASSOC);
                $debug_info['search_attempts'][] = "Tìm kiếm LIKE pattern: '" . $pattern . "' -> Tìm thấy " . count($matches) . " kết quả";

                if (!empty($matches)) {
                    foreach ($matches as $match) {
                        $debug_info['search_attempts'][] = "  - " . $match['name'] . " (" . substr($match['uuid'], 0, 8) . "...)";
                    }

                    // Lấy kết quả đầu tiên
                    $debug_info['found_uuid'] = $matches[0]['uuid'];
                    $debug_info['found_player_name'] = $matches[0]['name'];
                }
            }
        }

        return $debug_info;
    } catch(PDOException $e) {
        $debug_info['error'] = $e->getMessage();
        return $debug_info;
    }
}
?>
