<?php
// Check if token is provided
if (!isset($_GET['token']) || empty($_GET['token'])) {
    $_SESSION['message'] = 'Mã đặt lại mật khẩu không hợp lệ';
    $_SESSION['message_type'] = 'danger';
    header('Location: /forgot-password');
    exit;
}

$token = $_GET['token'];

// Verify token
$result = verifyPasswordResetToken($token);
$tokenValid = $result['status'];
$userData = $result['data'] ?? null;

// Kiểm tra kết quả xác thực token

// Check if token has been verified with O<PERSON>
if ($tokenValid) {
    try {
        global $conn;

        // Get token data
        if (isset($userData['token_key'])) {
            $stmt = $conn->prepare("SELECT value FROM nlogin_data WHERE `key` = :key");
            $stmt->bindParam(':key', $userData['token_key']);
            $stmt->execute();

            // <PERSON><PERSON>m tra xác thực token

            if ($stmt->rowCount() > 0) {
                $row = $stmt->fetch(PDO::FETCH_ASSOC);
                $tokenData = json_decode($row['value'], true);

                // Lấy dữ liệu token

                // Check if token has been verified
                if (!isset($tokenData['verified']) || !$tokenData['verified']) {
                    // Token not verified, redirect to verify OTP page
                    $_SESSION['reset_email'] = $userData['email'];
                    $_SESSION['message'] = 'Vui lòng xác minh mã OTP trước khi đặt lại mật khẩu.';
                    $_SESSION['message_type'] = 'warning';
                    header('Location: /verify-otp');
                    exit;
                }
            }
        }
    } catch(PDOException $e) {
        // Xử lý lỗi nếu cần
    }
}

// Process reset password form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $tokenValid) {
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];

    // Validate passwords
    if (empty($password) || empty($confirm_password)) {
        $_SESSION['message'] = 'Vui lòng nhập mật khẩu mới và xác nhận mật khẩu';
        $_SESSION['message_type'] = 'danger';
    } elseif ($password !== $confirm_password) {
        $_SESSION['message'] = 'Mật khẩu không khớp';
        $_SESSION['message_type'] = 'danger';
    } elseif (strlen($password) < 8) {
        $_SESSION['message'] = 'Mật khẩu phải có ít nhất 8 ký tự';
        $_SESSION['message_type'] = 'danger';
    } else {
        // Reset password
        $resetResult = resetPasswordWithToken($token, $password);

        if ($resetResult['status']) {
            $_SESSION['message'] = 'Mật khẩu đã được đặt lại thành công. Vui lòng đăng nhập bằng mật khẩu mới.';
            $_SESSION['message_type'] = 'success';
            header('Location: /login');
            exit;
        } else {
            $_SESSION['message'] = $resetResult['message'];
            $_SESSION['message_type'] = 'danger';
        }
    }
}
?>

<div class="row">
    <div class="col-md-6 offset-md-3">
        <div class="auth-form">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-key"></i> Đặt Lại Mật Khẩu</h4>
                </div>
                <div class="card-body">
                    <?php if (!$tokenValid): ?>
                        <div class="alert alert-danger">
                            <p><?php echo $result['message']; ?></p>
                            <p>Vui lòng <a href="/forgot-password">yêu cầu đặt lại mật khẩu</a> mới.</p>
                        </div>
                    <?php else: ?>
                        <p class="text-center">Xin chào <?php echo htmlspecialchars($userData['username']); ?>, vui lòng nhập mật khẩu mới của bạn.</p>

                        <form method="POST" action="/reset-password?token=<?php echo htmlspecialchars($token); ?>" class="needs-validation" novalidate>
                            <div class="form-group">
                                <label for="password">Mật khẩu mới</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    </div>
                                    <input type="password" class="form-control" id="password" name="password" placeholder="Nhập mật khẩu mới" required minlength="8">
                                    <div class="invalid-feedback">
                                        Mật khẩu phải có ít nhất 8 ký tự.
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="confirm_password">Xác nhận mật khẩu</label>
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    </div>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="Nhập lại mật khẩu mới" required>
                                    <div class="invalid-feedback">
                                        Mật khẩu không khớp.
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary btn-block">Đặt Lại Mật Khẩu</button>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Password validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;

    if (password !== confirmPassword) {
        this.setCustomValidity('Mật khẩu không khớp');
    } else {
        this.setCustomValidity('');
    }
});
</script>
