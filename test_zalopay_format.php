<?php
// Test script cho ZaloPay format matching
echo "<h2>Test ZaloPay Format Matching</h2>";

// Test cases với ZaloPay format
$test_cases = [
    [
        'transaction_code' => 'earth0487251A',
        'server' => 'earth',
        'zalopay_contents' => [
            'ZION ZALOPAY-CHUYENTIEN-O5CH7A7JBVL6-ear th0487251A. TU: ZION',
            'ZION ZALOPAY-CHUYENTIEN-O5CH7A7JBVL6-earth0487251A. TU: ZION',
            'ZION ZALOPAY-CHUYENTIEN-O5CH7A7JBVL6-EARTH0487251A. TU: ZION',
            'ZION ZALOPAY-CHUYENTIEN-O5CH7A7JBVL6-0487251A. TU: ZION',
            'ZION ZALOPAY-CHUYENTIEN-O5CH7A7JBVL6-earth 0487251A. TU: ZION',
            'ZION ZALOPAY-CHUYENTIEN-O5CH7A7JBVL6-KTJ <PERSON>VZL. TU: ZION', // No match
        ]
    ],
    [
        'transaction_code' => 'ultrapvpBDEA4882',
        'server' => 'ultrapvp',
        'zalopay_contents' => [
            'ZION ZALOPAY-CHUYENTIEN-O5CH7A7JBVL6-ultrapvpBDEA4882. TU: ZION',
            'ZION ZALOPAY-CHUYENTIEN-O5CH7A7JBVL6-UltraPvPBDEA4882. TU: ZION',
            'ZION ZALOPAY-CHUYENTIEN-O5CH7A7JBVL6-BDEA4882. TU: ZION',
            'ZION ZALOPAY-CHUYENTIEN-O5CH7A7JBVL6-ultrapvp BDEA4882. TU: ZION',
            'ZION ZALOPAY-CHUYENTIEN-O5CH7A7JBVL6-random text. TU: ZION', // No match
        ]
    ]
];

echo "<style>
table { border-collapse: collapse; margin: 20px 0; width: 100%; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
.match { background-color: #d4edda; color: #155724; font-weight: bold; }
.no-match { background-color: #f8d7da; color: #721c24; }
.zalopay-match { background-color: #fff3cd; color: #856404; font-weight: bold; }
code { background: #e9ecef; padding: 2px 4px; border-radius: 3px; font-family: monospace; font-size: 11px; }
</style>";

foreach ($test_cases as $i => $test_case) {
    $transaction_code = $test_case['transaction_code'];
    $server = $test_case['server'];
    $hash_part = substr($transaction_code, strlen($server)); // Extract hash
    
    echo "<h3>Test Case " . ($i + 1) . ": <code>$transaction_code</code></h3>";
    echo "<p><strong>Server:</strong> <code>$server</code></p>";
    echo "<p><strong>Hash Part:</strong> <code>$hash_part</code></p>";
    
    echo "<table>";
    echo "<tr>";
    echo "<th>ZaloPay Content</th>";
    echo "<th>Full Match</th>";
    echo "<th>Hash Match</th>";
    echo "<th>ZaloPay Regex</th>";
    echo "<th>ZaloPay Full</th>";
    echo "<th>Server+Hash</th>";
    echo "<th>Result</th>";
    echo "</tr>";
    
    foreach ($test_case['zalopay_contents'] as $content) {
        // Test all matching methods
        $full_match = stripos($content, $transaction_code) !== false;
        $hash_match = stripos($content, $hash_part) !== false;
        
        // ZaloPay regex patterns
        $zalopay_regex = preg_match('/ZALOPAY.*?-(' . preg_quote($server, '/') . ')?\s*(' . preg_quote($hash_part, '/') . ')/i', $content);
        $zalopay_full = preg_match('/ZALOPAY.*?(' . preg_quote($transaction_code, '/') . ')/i', $content);
        $server_hash = preg_match('/(' . preg_quote($server, '/') . ')\s*(' . preg_quote($hash_part, '/') . ')/i', $content);
        
        $overall_match = $full_match || $hash_match || $zalopay_regex || $zalopay_full || $server_hash;
        
        // Determine which method matched
        $match_method = '';
        if ($full_match) $match_method = 'Full Code';
        elseif ($hash_match) $match_method = 'Hash Only';
        elseif ($zalopay_regex) $match_method = 'ZaloPay Regex';
        elseif ($zalopay_full) $match_method = 'ZaloPay Full';
        elseif ($server_hash) $match_method = 'Server+Hash';
        
        echo "<tr>";
        echo "<td><code>" . htmlspecialchars($content) . "</code></td>";
        echo "<td>" . ($full_match ? "✅" : "❌") . "</td>";
        echo "<td>" . ($hash_match ? "✅" : "❌") . "</td>";
        echo "<td>" . ($zalopay_regex ? "✅" : "❌") . "</td>";
        echo "<td>" . ($zalopay_full ? "✅" : "❌") . "</td>";
        echo "<td>" . ($server_hash ? "✅" : "❌") . "</td>";
        
        if ($overall_match) {
            $class = ($zalopay_regex || $zalopay_full) ? 'zalopay-match' : 'match';
            echo "<td class='$class'>✅ $match_method</td>";
        } else {
            echo "<td class='no-match'>❌ NO MATCH</td>";
        }
        echo "</tr>";
    }
    
    echo "</table>";
}

// Test với real ZaloPay data
echo "<h3>Test với Real ZaloPay Data từ API:</h3>";

try {
    $payment_api_url = 'http://*************:3000/payments';
    $response = file_get_contents($payment_api_url);
    
    if ($response) {
        $payments = json_decode($response, true);
        
        // Filter ZaloPay payments
        $zalopay_payments = array_filter($payments, function($payment) {
            return stripos($payment['content'] ?? '', 'ZALOPAY') !== false;
        });
        
        if ($zalopay_payments) {
            echo "<p>✅ Found " . count($zalopay_payments) . " ZaloPay payments:</p>";
            
            echo "<table>";
            echo "<tr>";
            echo "<th>Content</th>";
            echo "<th>Extracted Info</th>";
            echo "<th>Potential Matches</th>";
            echo "</tr>";
            
            foreach (array_slice($zalopay_payments, 0, 5) as $payment) {
                $content = $payment['content'] ?? '';
                
                // Extract potential transaction codes
                $extracted_info = [];
                
                // Look for server names + hash patterns
                if (preg_match_all('/ZALOPAY.*?-([a-z]*)\s*([A-Z0-9]{8})/i', $content, $matches, PREG_SET_ORDER)) {
                    foreach ($matches as $match) {
                        $server_part = strtolower($match[1]);
                        $hash_part = strtoupper($match[2]);
                        $extracted_info[] = "Server: '$server_part', Hash: '$hash_part'";
                    }
                }
                
                // Look for standalone hashes
                if (preg_match_all('/\b([A-Z0-9]{8})\b/', $content, $matches)) {
                    foreach ($matches[1] as $hash) {
                        $extracted_info[] = "Standalone hash: '$hash'";
                    }
                }
                
                // Test potential matches với các server
                $potential_matches = [];
                $servers = ['earth', 'ultrapvp', 'skyblock'];
                
                foreach ($servers as $server) {
                    if (preg_match('/ZALOPAY.*?-(' . $server . ')?\s*([A-Z0-9]{8})/i', $content, $match)) {
                        $hash = $match[2] ?? '';
                        if ($hash) {
                            $potential_matches[] = $server . $hash;
                        }
                    }
                }
                
                echo "<tr>";
                echo "<td><code>" . htmlspecialchars(substr($content, 0, 60)) . "...</code></td>";
                echo "<td>";
                if ($extracted_info) {
                    foreach ($extracted_info as $info) {
                        echo "<div style='font-size: 11px;'>$info</div>";
                    }
                } else {
                    echo "<em>No patterns found</em>";
                }
                echo "</td>";
                echo "<td>";
                if ($potential_matches) {
                    foreach ($potential_matches as $match) {
                        echo "<code>$match</code><br>";
                    }
                } else {
                    echo "<em>No matches</em>";
                }
                echo "</td>";
                echo "</tr>";
            }
            
            echo "</table>";
        } else {
            echo "❌ No ZaloPay payments found in recent API data<br>";
        }
    } else {
        echo "❌ Cannot connect to API<br>";
    }
} catch (Exception $e) {
    echo "❌ API Error: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h3>🎯 ZaloPay Matching Strategy:</h3>";
echo "<ul>";
echo "<li><strong>Pattern 1:</strong> <code>ZALOPAY.*?-earth0487251A</code> (Full transaction code)</li>";
echo "<li><strong>Pattern 2:</strong> <code>ZALOPAY.*?-earth\\s*0487251A</code> (Server + space + hash)</li>";
echo "<li><strong>Pattern 3:</strong> <code>ZALOPAY.*?-0487251A</code> (Hash only)</li>";
echo "<li><strong>Fallback:</strong> Existing hash matching methods</li>";
echo "</ul>";

echo "<p><strong>Regex Patterns Used:</strong></p>";
echo "<ul>";
echo "<li><code>/ZALOPAY.*?-(server)?\\s*(hash)/i</code> - ZaloPay với optional server</li>";
echo "<li><code>/ZALOPAY.*?(full_transaction_code)/i</code> - ZaloPay với full code</li>";
echo "<li><code>/(server)\\s*(hash)/i</code> - Server + hash với separator</li>";
echo "</ul>";

echo "<p><strong>Sau khi test xong, xóa file này!</strong></p>";
?>
