<?php
/**
 * <PERSON>ript tự động kiểm tra và duyệt thanh toán QR
 * Chạy mỗi 1-2 phút bằng cron job
 */

// Chạy script này từ command line hoặc cron
if (php_sapi_name() !== 'cli' && !isset($_GET['manual'])) {
    die('Script này chỉ chạy từ command line hoặc với tham số ?manual=1');
}

require_once dirname(__DIR__) . '/config/config.php';

echo "[" . date('Y-m-d H:i:s') . "] Bắt đầu kiểm tra thanh toán tự động...\n";

try {
    // Lấy danh sách giao dịch pending (chưa xử lý trong 30 phút qua)
    $stmt = $conn->prepare("
        SELECT * FROM nlogin_qr_pending 
        WHERE processed = 0 
        AND created_at > DATE_SUB(NOW(), INTERVAL 30 MINUTE)
        ORDER BY created_at ASC
    ");
    $stmt->execute();
    $pending_transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($pending_transactions)) {
        echo "Không có giao dịch pending nào.\n";
        exit;
    }
    
    echo "Tìm thấy " . count($pending_transactions) . " giao dịch pending.\n";
    
    // Lấy dữ liệu từ API thanh toán
    $payment_api_url = 'http://*************:3000/payments';
    $response = file_get_contents($payment_api_url);
    
    if ($response === false) {
        throw new Exception('Không thể kết nối đến API thanh toán');
    }
    
    $payments = json_decode($response, true);
    
    if (!is_array($payments)) {
        throw new Exception('Dữ liệu API không hợp lệ');
    }
    
    echo "Lấy được " . count($payments) . " giao dịch từ API.\n";
    
    $processed_count = 0;
    
    // Kiểm tra từng giao dịch pending
    foreach ($pending_transactions as $pending) {
        $found_payment = findMatchingPayment($payments, $pending);
        
        if ($found_payment) {
            // Kiểm tra xem giao dịch đã được xử lý chưa
            $stmt = $conn->prepare("SELECT id FROM nlogin_qr_transactions WHERE transaction_id = :transaction_id");
            $stmt->bindParam(':transaction_id', $found_payment['transaction_id']);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                echo "Giao dịch {$found_payment['transaction_id']} đã được xử lý trước đó.\n";
                continue;
            }
            
            // Xử lý thanh toán
            $success = processPayment($pending, $found_payment);
            
            if ($success) {
                // Đánh dấu đã xử lý
                $stmt = $conn->prepare("UPDATE nlogin_qr_pending SET processed = 1, processed_at = NOW() WHERE id = :id");
                $stmt->bindParam(':id', $pending['id']);
                $stmt->execute();
                
                $processed_count++;
                echo "✅ Đã xử lý thành công giao dịch: {$pending['transaction_code']} - {$pending['amount']} VNĐ - {$pending['username']}\n";
            } else {
                echo "❌ Lỗi khi xử lý giao dịch: {$pending['transaction_code']}\n";
            }
        }
    }
    
    echo "Hoàn thành! Đã xử lý $processed_count giao dịch.\n";
    
} catch (Exception $e) {
    echo "❌ Lỗi: " . $e->getMessage() . "\n";
    error_log("Auto payment check error: " . $e->getMessage());
}

/**
 * Tìm giao dịch khớp trong danh sách thanh toán
 */
function findMatchingPayment($payments, $pending) {
    foreach ($payments as $payment) {
        $content = $payment['content'] ?? '';
        $server = $pending['server'];
        $amount_match = ($payment['amount'] == $pending['amount']);
        $time_match = (time() - strtotime($payment['date']) < 1800); // 30 phút
        
        $content_match = false;
        
        // Kiểm tra mã giao dịch đầy đủ
        if (strpos($content, $pending['transaction_code']) !== false) {
            $content_match = true;
        }
        // Kiểm tra server + pattern (ví dụ: earthQHGBHTZM)
        elseif (preg_match('/' . $server . '[A-Z0-9]{8}/i', $content)) {
            $content_match = true;
        }
        // Kiểm tra chỉ có server name trong nội dung
        elseif (stripos($content, $server) !== false && $amount_match && $time_match) {
            $content_match = true;
        }
        
        if ($content_match && $amount_match) {
            return $payment;
        }
    }
    
    return null;
}

/**
 * Xử lý thanh toán
 */
function processPayment($pending, $payment) {
    global $conn;
    
    try {
        // Lưu giao dịch vào database
        $stmt = $conn->prepare("
            INSERT INTO nlogin_qr_transactions 
            (user_id, username, transaction_id, amount, server, transaction_code, payment_data, created_at, processed_at) 
            VALUES (:user_id, :username, :transaction_id, :amount, :server, :transaction_code, :payment_data, :created_at, NOW())
        ");
        
        $stmt->bindParam(':user_id', $pending['user_id']);
        $stmt->bindParam(':username', $pending['username']);
        $stmt->bindParam(':transaction_id', $payment['transaction_id']);
        $stmt->bindParam(':amount', $pending['amount']);
        $stmt->bindParam(':server', $pending['server']);
        $stmt->bindParam(':transaction_code', $pending['transaction_code']);
        $stmt->bindParam(':payment_data', json_encode($payment));
        $stmt->bindParam(':created_at', $pending['created_at']);
        $stmt->execute();
        
        // Gửi lệnh đến server game
        $success = sendGameCommand($pending['username'], $pending['amount'], $pending['server']);
        
        return $success;
        
    } catch (Exception $e) {
        error_log("Process payment error: " . $e->getMessage());
        return false;
    }
}

/**
 * Gửi lệnh cộng tiền đến server game
 */
function sendGameCommand($username, $amount, $server) {
    // Cấu hình Pterodactyl API
    $pterodactyl_configDebug Pterodactyl API
Test: Server Info
URL: https://panel.gamehosting.vn/api/client/servers/8661ab4b

📋 HTTP Code: 200
✅ Status: Success!
Response Keys: object, attributes, meta
Response (first 500 chars):
{"object":"server","attributes":{"server_owner":false,"identifier":"8661ab4b","internal_id":969,"parent_id":null,"uuid":"8661ab4b-fff7-4628-b150-2a2e579d4d03","name":"DPT Earth","mcversion":null,"nest_id":1,"egg_id":1,"node":"RYZEN 9","node_alert":null,"daemon_text":"RYZEN 9","container_text":"RYZEN 9","egg_image":null,"is_node_under_maintenance":false,"sftp_details":{"ip":"ryzen.dptmc.com","port":2022},"description":"","limits":{"memory":46080,"swap":-1,"disk":307200,"io":1000,"cpu":1200,"threa...
Test: Server Resources
URL: https://panel.gamehosting.vn/api/client/servers/8661ab4b/resources

📋 HTTP Code: 200
✅ Status: Success!
Response Keys: object, attributes
Response:
{"object":"stats","attributes":{"current_state":"running","is_suspended":false,"resources":{"memory_bytes":16607277056,"cpu_absolute":156.1,"disk_bytes":90089019481,"network_rx_bytes":13177091654,"network_tx_bytes":************,"uptime":40163704}}}
Test: Server Command
URL: https://panel.gamehosting.vn/api/client/servers/8661ab4b/command

📋 HTTP Code: 405
❌ Status: Error HTTP 405
Response:
{
    "errors": [
        {
            "code": "MethodNotAllowedHttpException",
            "status": "405",
            "detail": "The GET method is not supported for route api/client/servers/8661ab4b/command. Supported methods: POST."
        }
    ]
}
Test: Server Power
URL: https://panel.gamehosting.vn/api/client/servers/8661ab4b/power

📋 HTTP Code: 405
❌ Status: Error HTTP 405
Response:
{
    "errors": [
        {
            "code": "MethodNotAllowedHttpException",
            "status": "405",
            "detail": "The GET method is not supported for route api/client/servers/8661ab4b/power. Supported methods: POST."
        }
    ]
}
Test: Server Websocket
URL: https://panel.gamehosting.vn/api/client/servers/8661ab4b/websocket

📋 HTTP Code: 200
✅ Status: Success!
Response Keys: data
Data Keys: token, socket
Response:
{"data":{"token":"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiIsImp0aSI6IjBiNDcyYTYyZmQ3NWVjMDQ2Y2M0YmZmYmNhMjc2MzY1In0.eyJpc3MiOiJodHRwczovL3BhbmVsLmdhbWVob3N0aW5nLnZuIiwiYXVkIjpbImh0dHBzOi8vcnl6ZW4uZHB0bWMuY29tOjgwODAiXSwianRpIjoiMGI0NzJhNjJmZDc1ZWMwNDZjYzRiZmZiY2EyNzYzNjUiLCJpYXQiOjE3NDg2OTkyMzEsIm5iZiI6MTc0ODY5ODkzMSwiZXhwIjoxNzQ4Njk5ODMxLCJzZXJ2ZXJfdXVpZCI6Ijg2NjFhYjRiLWZmZjctNDYyOC1iMTUwLTJhMmU1NzlkNGQwMyIsInBlcm1pc3Npb25zIjpbIioiLCJhZG1pbi53ZWJzb2NrZXQuZXJyb3JzIiwiYWRtaW4ud2Vic29ja2V0Lmluc3RhbGwiLCJhZG1pbi53ZWJzb2NrZXQudHJhbnNmZXIiXSwidXNlcl91dWlkIjoiMWYxMWZkZGUtNDE1ZC00NmFhLWI3OWYtZjdmN2YwOTA0MjM5IiwidXNlcl9pZCI6MjIsInVuaXF1ZV9pZCI6IkJqRkhjcUd0Z2VzU0IzbEgifQ.1J2cp14YLsIzkeVa43Sm2EX88D6C2ggv_Z_kS4EKD4w","socket":"wss:\/\/ryzen.dptmc.com:8080\/api\/servers\/8661ab4b-fff7-4628-b150-2a2e579d4d03\/ws"}}
Test: List Servers
URL: https://panel.gamehosting.vn/api/client

📋 HTTP Code: 200
✅ Status: Success!
Response Keys: object, data, meta
Data Keys: 0, 1, 2, 3
Response (first 500 chars):
{"object":"list","data":[{"object":"server","attributes":{"server_owner":true,"identifier":"07474c31","internal_id":1700,"parent_id":null,"uuid":"07474c31-cf0a-443b-945c-e1a1557a087a","name":"Testing ABC XYZ","mcversion":null,"nest_id":1,"egg_id":1,"node":"NODE 1 - 6144","node_alert":null,"daemon_text":"NODE 1","container_text":"NODE 1","egg_image":null,"is_node_under_maintenance":false,"sftp_details":{"ip":"sv.dptcloud.vn","port":2022},"description":"","limits":{"memory":16384,"swap":0,"disk":6...
Test: Account Info
URL: https://panel.gamehosting.vn/api/client/account

📋 HTTP Code: 200
✅ Status: Success!
Response Keys: object, attributes
Response:
{"object":"user","attributes":{"id":22,"admin":true,"username":"trannhatlong2001th","email":"<EMAIL>","first_name":"Tr\u1ea7n","last_name":"Long","language":"en"}}
Test POST Command (nếu server info thành công):
Testing command: list

URL: https://panel.gamehosting.vn/api/client/servers/8661ab4b/command

📋 HTTP Code: 204
✅ Status: Command sent successfully!
Kết luận:
Nếu Account Info hoạt động → API key đúng
Nếu List Servers hoạt động → Có thể lấy danh sách server
Nếu Server Info hoạt động → Server ID đúng
Nếu Server Command hoạt động → Có thể gửi lệnh
Sau khi debug xong, hãy xóa file này! = [
        'earth' => [
            'url' => 'https://panel.gamehosting.vn/api/client/servers/8661ab4b/command',
            'api_key' => 'ptlc_DzHfXQUGVnsUhaA9cz6M27J47hRIEyAGo9t7YYMl0Gs'
        ],
        'ultrapvp' => [
            'url' => 'https://panel.gamehosting.vn/api/client/servers/ec2c6a6f/command',
            'api_key' => 'ptlc_DzHfXQUGVnsUhaA9cz6M27J47hRIEyAGo9t7YYMl0Gs'
        ],
        'skyblock' => [
            'url' => 'https://panel.gamehosting.vn/api/client/servers/889ecbac/command',
            'api_key' => 'ptlc_DzHfXQUGVnsUhaA9cz6M27J47hRIEyAGo9t7YYMl0Gs'
        ]
    ];
    
    if (!isset($pterodactyl_config[$server])) {
        error_log("Unknown server: $server");
        return false;
    }
    
    $config = $pterodactyl_config[$server];
    
    // Tạo lệnh game
    $command = "dotman napthucong $username $amount -f";
    
    // Chuẩn bị dữ liệu gửi
    $data = json_encode(['command' => $command]);
    
    // Cấu hình cURL
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $config['url'],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $data,
        CURLOPT_HTTPHEADER => [
            'Accept: application/json',
            'Content-Type: application/json',
            'Authorization: Bearer ' . $config['api_key']
        ],
        CURLOPT_TIMEOUT => 30
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        error_log("cURL error: $error");
        return false;
    }
    
    if ($http_code === 204) {
        // Thành công
        echo "Command sent successfully: $command\n";
        return true;
    } else {
        error_log("Pterodactyl API error: HTTP $http_code, Response: $response");
        return false;
    }
}
?>
