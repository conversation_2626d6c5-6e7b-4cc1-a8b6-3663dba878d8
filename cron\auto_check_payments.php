<?php
/**
 * <PERSON>ript tự động kiểm tra và duyệt thanh toán QR
 * Chạy mỗi 1-2 phút bằng cron job
 */

// Chạy script này từ command line hoặc cron
if (php_sapi_name() !== 'cli' && !isset($_GET['manual'])) {
    die('Script này chỉ chạy từ command line hoặc với tham số ?manual=1');
}

require_once dirname(__DIR__) . '/config/config.php';

echo "[" . date('Y-m-d H:i:s') . "] Bắt đầu kiểm tra thanh toán tự động...\n";

try {
    // Hủy các giao dịch quá 6 phút - NHƯNG GIỮ processed = 0 để check nền
    $stmt = $conn->prepare("
        UPDATE nlogin_qr_pending
        SET status = 3, processed = 0, processed_at = NOW(), check_count = 0
        WHERE status = 0
        AND created_at < DATE_SUB(NOW(), INTERVAL 6 MINUTE)
    ");
    $stmt->execute();
    $expired_count = $stmt->rowCount();

    if ($expired_count > 0) {
        echo "Đã hủy $expired_count giao dịch hết hạn (quá 6 phút) - giữ processed=0 để check nền.\n";
        error_log("Cron job cancelled $expired_count expired QR transactions (keeping processed=0 for background check)");
    }

    // Đánh dấu final cho giao dịch đã check nền quá 2 lần
    $stmt = $conn->prepare("
        UPDATE nlogin_qr_pending
        SET processed = 1
        WHERE status = 3
        AND processed = 0
        AND check_count >= 2
    ");
    $stmt->execute();
    $final_count = $stmt->rowCount();

    if ($final_count > 0) {
        echo "Đã đánh dấu final cho $final_count giao dịch sau 2 lần check nền.\n";
        error_log("Cron job marked $final_count transactions as final after 2 background checks");
    }

    // Xóa các giao dịch cũ hơn 24 giờ để tránh database quá lớn
    $stmt = $conn->prepare("
        DELETE FROM nlogin_qr_pending
        WHERE created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ");
    $stmt->execute();
    $deleted_count = $stmt->rowCount();

    if ($deleted_count > 0) {
        echo "Đã xóa $deleted_count giao dịch cũ hơn 24 giờ.\n";
        error_log("Cron job deleted $deleted_count old QR transactions");
    }

    // Cleanup completed transactions cũ hơn 2 giờ để tránh conflict
    $stmt = $conn->prepare("
        DELETE FROM nlogin_qr_transactions
        WHERE created_at < DATE_SUB(NOW(), INTERVAL 2 HOUR)
    ");
    $stmt->execute();
    $cleaned_completed = $stmt->rowCount();

    if ($cleaned_completed > 0) {
        echo "Đã xóa $cleaned_completed completed transactions cũ.\n";
        error_log("Cron job cleaned $cleaned_completed old completed transactions");
    }

    // Lấy danh sách giao dịch cần check: pending (status=0) và cancelled đang check nền (status=3, processed=0)
    $stmt = $conn->prepare("
        SELECT * FROM nlogin_qr_pending
        WHERE (
            (status = 0 AND created_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE))
            OR
            (status = 3 AND processed = 0 AND check_count < 2)
        )
        ORDER BY created_at ASC
    ");
    $stmt->execute();
    $pending_transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($pending_transactions)) {
        echo "Không có giao dịch pending nào.\n";
        exit;
    }

    echo "Tìm thấy " . count($pending_transactions) . " giao dịch pending.\n";

    // Lấy dữ liệu từ API thanh toán
    $payment_api_url = 'http://160.25.233.54:3000/payments';
    $response = file_get_contents($payment_api_url);

    if ($response === false) {
        throw new Exception('Không thể kết nối đến API thanh toán');
    }

    $payments = json_decode($response, true);

    if (!is_array($payments)) {
        throw new Exception('Dữ liệu API không hợp lệ');
    }

    echo "Lấy được " . count($payments) . " giao dịch từ API.\n";

    $processed_count = 0;

    // Kiểm tra từng giao dịch pending
    foreach ($pending_transactions as $pending) {
        $status_text = ($pending['status'] == 0) ? 'Pending' : 'Cancelled (Background Check)';
        echo "Kiểm tra giao dịch: {$pending['transaction_code']} - {$pending['amount']} VNĐ - $status_text (Check #{$pending['check_count']})\n";

        $found_payment = findMatchingPayment($payments, $pending);

        if ($found_payment) {
            // Kiểm tra xem giao dịch đã được xử lý chưa
            $stmt = $conn->prepare("SELECT id FROM nlogin_qr_transactions WHERE transaction_id = :transaction_id");
            $stmt->bindParam(':transaction_id', $found_payment['transaction_id']);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                echo "Giao dịch {$found_payment['transaction_id']} đã được xử lý trước đó.\n";
                continue;
            }

            // Xử lý thanh toán
            $success = processPayment($pending, $found_payment);

            if ($success) {
                // Đánh dấu thành công: status = 1, processed = 1
                $stmt = $conn->prepare("UPDATE nlogin_qr_pending SET status = 1, processed = 1, processed_at = NOW() WHERE id = :id");
                $stmt->bindParam(':id', $pending['id']);
                $stmt->execute();

                $processed_count++;
                echo "✅ Đã xử lý thành công giao dịch: {$pending['transaction_code']} - {$pending['amount']} VNĐ - {$pending['username']}\n";
            } else {
                echo "❌ Lỗi khi xử lý giao dịch: {$pending['transaction_code']}\n";
            }
        } else {
            // Không tìm thấy payment - tăng check_count
            if ($pending['status'] == 3) {
                // Đây là giao dịch cancelled đang check nền
                $new_check_count = $pending['check_count'] + 1;
                $stmt = $conn->prepare("UPDATE nlogin_qr_pending SET check_count = :check_count WHERE id = :id");
                $stmt->bindParam(':check_count', $new_check_count);
                $stmt->bindParam(':id', $pending['id']);
                $stmt->execute();

                echo "⏳ Không tìm thấy payment cho {$pending['transaction_code']} - Check count: $new_check_count/2\n";
            }
        }
    }

    echo "Hoàn thành! Đã xử lý $processed_count giao dịch.\n";

} catch (Exception $e) {
    echo "❌ Lỗi: " . $e->getMessage() . "\n";
    error_log("Auto payment check error: " . $e->getMessage());
}

/**
 * Tìm giao dịch khớp trong danh sách thanh toán
 */
function findMatchingPayment($payments, $pending) {
    foreach ($payments as $payment) {
        $content = $payment['content'] ?? '';
        $server = $pending['server'];
        $amount_match = ($payment['amount'] == $pending['amount']);
        $time_match = (time() - strtotime($payment['date']) < 1800); // 30 phút

        $content_match = false;

        // Lấy phần hash (8 ký tự cuối) từ transaction code
        $hash_part = substr($pending['transaction_code'], strlen($server)); // Bỏ phần server, lấy hash

        // Kiểm tra mã giao dịch đầy đủ (case insensitive)
        if (stripos($content, $pending['transaction_code']) !== false) {
            $content_match = true;
        }
        // Kiểm tra với uppercase
        elseif (stripos($content, strtoupper($pending['transaction_code'])) !== false) {
            $content_match = true;
        }
        // Kiểm tra CHỈ phần hash (8 ký tự) - LINH HOẠT HƠN
        elseif (stripos($content, $hash_part) !== false) {
            $content_match = true;
        }
        // Kiểm tra hash uppercase
        elseif (stripos($content, strtoupper($hash_part)) !== false) {
            $content_match = true;
        }
        // TÍNH NĂNG MỚI: Kiểm tra ZaloPay format - Pattern 1: Full transaction code
        elseif (preg_match('/ZALOPAY.*?-[^.]*?' . preg_quote($pending['transaction_code'], '/') . '/i', $content)) {
            $content_match = true;
        }
        // ZaloPay Pattern 2: Server + space/separator + hash
        elseif (preg_match('/ZALOPAY.*?-[^.]*?' . preg_quote($server, '/') . '\s+' . preg_quote($hash_part, '/') . '/i', $content)) {
            $content_match = true;
        }
        // ZaloPay Pattern 3: Hash only trong ZaloPay content
        elseif (stripos($content, 'ZALOPAY') !== false && stripos($content, $hash_part) !== false) {
            $content_match = true;
        }
        // Pattern 4: Server + hash với khoảng trắng hoặc ký tự đặc biệt (general)
        elseif (preg_match('/(' . preg_quote($server, '/') . ')\s+(' . preg_quote($hash_part, '/') . ')/i', $content)) {
            $content_match = true;
        }
        // Kiểm tra chỉ có server name trong nội dung
        elseif (stripos($content, $server) !== false && $amount_match && $time_match) {
            $content_match = true;
        }

        if ($content_match && $amount_match) {
            return $payment;
        }
    }

    return null;
}

/**
 * Xử lý thanh toán
 */
function processPayment($pending, $payment) {
    global $conn;

    try {
        // Lưu giao dịch vào database
        $stmt = $conn->prepare("
            INSERT INTO nlogin_qr_transactions
            (user_id, username, transaction_id, amount, server, transaction_code, payment_data, created_at, processed_at)
            VALUES (:user_id, :username, :transaction_id, :amount, :server, :transaction_code, :payment_data, :created_at, NOW())
        ");

        $stmt->bindParam(':user_id', $pending['user_id']);
        $stmt->bindParam(':username', $pending['username']);
        $stmt->bindParam(':transaction_id', $payment['transaction_id']);
        $stmt->bindParam(':amount', $pending['amount']);
        $stmt->bindParam(':server', $pending['server']);
        $stmt->bindParam(':transaction_code', $pending['transaction_code']);
        $stmt->bindParam(':payment_data', json_encode($payment));
        $stmt->bindParam(':created_at', $pending['created_at']);
        $stmt->execute();

        // Gửi lệnh đến server game
        $success = sendGameCommand($pending['username'], $pending['amount'], $pending['server']);

        return $success;

    } catch (Exception $e) {
        error_log("Process payment error: " . $e->getMessage());
        return false;
    }
}

/**
 * Gửi lệnh cộng tiền đến server game
 */
function sendGameCommand($username, $amount, $server) {
    // Cấu hình Pterodactyl API
    $pterodactyl_config = [
        'earth' => [
            'url' => 'https://panel.gamehosting.vn/api/client/servers/8661ab4b/command',
            'api_key' => 'ptlc_DzHfXQUGVnsUhaA9cz6M27J47hRIEyAGo9t7YYMl0Gs'
        ],
        'ultrapvp' => [
            'url' => 'https://panel.gamehosting.vn/api/client/servers/ec2c6a6f/command',
            'api_key' => 'ptlc_DzHfXQUGVnsUhaA9cz6M27J47hRIEyAGo9t7YYMl0Gs'
        ],
        'skyblock' => [
            'url' => 'https://panel.gamehosting.vn/api/client/servers/889ecbac/command',
            'api_key' => 'ptlc_DzHfXQUGVnsUhaA9cz6M27J47hRIEyAGo9t7YYMl0Gs'
        ]
    ];

    if (!isset($pterodactyl_config[$server])) {
        error_log("Unknown server: $server");
        return false;
    }

    $config = $pterodactyl_config[$server];

    // Tạo lệnh game
    $command = "dotman napthucong $username $amount -f";

    // Chuẩn bị dữ liệu gửi
    $data = json_encode(['command' => $command]);

    // Cấu hình cURL
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $config['url'],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $data,
        CURLOPT_HTTPHEADER => [
            'Accept: application/json',
            'Content-Type: application/json',
            'Authorization: Bearer ' . $config['api_key']
        ],
        CURLOPT_TIMEOUT => 30
    ]);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        error_log("cURL error: $error");
        return false;
    }

    if ($http_code === 204) {
        // Thành công
        echo "Command sent successfully: $command\n";
        return true;
    } else {
        error_log("Pterodactyl API error: HTTP $http_code, Response: $response");
        return false;
    }
}
?>
