<?php
/**
 * <PERSON>ript tự động kiểm tra và duyệt thanh toán QR
 * Chạy mỗi 1-2 phút bằng cron job
 */

// Chạy script này từ command line hoặc cron
if (php_sapi_name() !== 'cli' && !isset($_GET['manual'])) {
    die('Script này chỉ chạy từ command line hoặc với tham số ?manual=1');
}

require_once dirname(__DIR__) . '/config/config.php';

echo "[" . date('Y-m-d H:i:s') . "] Bắt đầu kiểm tra thanh toán tự động...\n";

try {
    // Hủy các giao dịch quá 2 phút (thay vì 1 phút để tránh hủy nhầm)
    $stmt = $conn->prepare("
        UPDATE nlogin_qr_pending
        SET processed = 1, processed_at = NOW()
        WHERE processed = 0
        AND created_at < DATE_SUB(NOW(), INTERVAL 2 MINUTE)
    ");
    $stmt->execute();
    $expired_count = $stmt->rowCount();

    if ($expired_count > 0) {
        echo "Đã hủy $expired_count giao dịch hết hạn (quá 2 phút).\n";
        error_log("Cron job cancelled $expired_count expired QR transactions (older than 2 minutes)");
    }

    // Xóa các giao dịch cũ hơn 24 giờ để tránh database quá lớn
    $stmt = $conn->prepare("
        DELETE FROM nlogin_qr_pending
        WHERE created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)
    ");
    $stmt->execute();
    $deleted_count = $stmt->rowCount();

    if ($deleted_count > 0) {
        echo "Đã xóa $deleted_count giao dịch cũ hơn 24 giờ.\n";
        error_log("Cron job deleted $deleted_count old QR transactions");
    }

    // Lấy danh sách giao dịch pending (chưa xử lý trong 1 phút qua)
    $stmt = $conn->prepare("
        SELECT * FROM nlogin_qr_pending
        WHERE processed = 0
        AND created_at > DATE_SUB(NOW(), INTERVAL 1 MINUTE)
        ORDER BY created_at ASC
    ");
    $stmt->execute();
    $pending_transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($pending_transactions)) {
        echo "Không có giao dịch pending nào.\n";
        exit;
    }

    echo "Tìm thấy " . count($pending_transactions) . " giao dịch pending.\n";

    // Lấy dữ liệu từ API thanh toán
    $payment_api_url = 'http://160.25.233.54:3000/payments';
    $response = file_get_contents($payment_api_url);

    if ($response === false) {
        throw new Exception('Không thể kết nối đến API thanh toán');
    }

    $payments = json_decode($response, true);

    if (!is_array($payments)) {
        throw new Exception('Dữ liệu API không hợp lệ');
    }

    echo "Lấy được " . count($payments) . " giao dịch từ API.\n";

    $processed_count = 0;

    // Kiểm tra từng giao dịch pending
    foreach ($pending_transactions as $pending) {
        $found_payment = findMatchingPayment($payments, $pending);

        if ($found_payment) {
            // Kiểm tra xem giao dịch đã được xử lý chưa
            $stmt = $conn->prepare("SELECT id FROM nlogin_qr_transactions WHERE transaction_id = :transaction_id");
            $stmt->bindParam(':transaction_id', $found_payment['transaction_id']);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                echo "Giao dịch {$found_payment['transaction_id']} đã được xử lý trước đó.\n";
                continue;
            }

            // Xử lý thanh toán
            $success = processPayment($pending, $found_payment);

            if ($success) {
                // Đánh dấu đã xử lý
                $stmt = $conn->prepare("UPDATE nlogin_qr_pending SET processed = 1, processed_at = NOW() WHERE id = :id");
                $stmt->bindParam(':id', $pending['id']);
                $stmt->execute();

                $processed_count++;
                echo "✅ Đã xử lý thành công giao dịch: {$pending['transaction_code']} - {$pending['amount']} VNĐ - {$pending['username']}\n";
            } else {
                echo "❌ Lỗi khi xử lý giao dịch: {$pending['transaction_code']}\n";
            }
        }
    }

    echo "Hoàn thành! Đã xử lý $processed_count giao dịch.\n";

} catch (Exception $e) {
    echo "❌ Lỗi: " . $e->getMessage() . "\n";
    error_log("Auto payment check error: " . $e->getMessage());
}

/**
 * Tìm giao dịch khớp trong danh sách thanh toán
 */
function findMatchingPayment($payments, $pending) {
    foreach ($payments as $payment) {
        $content = $payment['content'] ?? '';
        $server = $pending['server'];
        $amount_match = ($payment['amount'] == $pending['amount']);
        $time_match = (time() - strtotime($payment['date']) < 1800); // 30 phút

        $content_match = false;

        // Kiểm tra mã giao dịch đầy đủ
        if (strpos($content, $pending['transaction_code']) !== false) {
            $content_match = true;
        }
        // Kiểm tra server + pattern (ví dụ: earthQHGBHTZM)
        elseif (preg_match('/' . $server . '[A-Z0-9]{8}/i', $content)) {
            $content_match = true;
        }
        // Kiểm tra chỉ có server name trong nội dung
        elseif (stripos($content, $server) !== false && $amount_match && $time_match) {
            $content_match = true;
        }

        if ($content_match && $amount_match) {
            return $payment;
        }
    }

    return null;
}

/**
 * Xử lý thanh toán
 */
function processPayment($pending, $payment) {
    global $conn;

    try {
        // Lưu giao dịch vào database
        $stmt = $conn->prepare("
            INSERT INTO nlogin_qr_transactions
            (user_id, username, transaction_id, amount, server, transaction_code, payment_data, created_at, processed_at)
            VALUES (:user_id, :username, :transaction_id, :amount, :server, :transaction_code, :payment_data, :created_at, NOW())
        ");

        $stmt->bindParam(':user_id', $pending['user_id']);
        $stmt->bindParam(':username', $pending['username']);
        $stmt->bindParam(':transaction_id', $payment['transaction_id']);
        $stmt->bindParam(':amount', $pending['amount']);
        $stmt->bindParam(':server', $pending['server']);
        $stmt->bindParam(':transaction_code', $pending['transaction_code']);
        $stmt->bindParam(':payment_data', json_encode($payment));
        $stmt->bindParam(':created_at', $pending['created_at']);
        $stmt->execute();

        // Gửi lệnh đến server game
        $success = sendGameCommand($pending['username'], $pending['amount'], $pending['server']);

        return $success;

    } catch (Exception $e) {
        error_log("Process payment error: " . $e->getMessage());
        return false;
    }
}

/**
 * Gửi lệnh cộng tiền đến server game
 */
function sendGameCommand($username, $amount, $server) {
    // Cấu hình Pterodactyl API
    $pterodactyl_config = [
        'earth' => [
            'url' => 'https://panel.gamehosting.vn/api/client/servers/8661ab4b/command',
            'api_key' => 'ptlc_DzHfXQUGVnsUhaA9cz6M27J47hRIEyAGo9t7YYMl0Gs'
        ],
        'ultrapvp' => [
            'url' => 'https://panel.gamehosting.vn/api/client/servers/ec2c6a6f/command',
            'api_key' => 'ptlc_DzHfXQUGVnsUhaA9cz6M27J47hRIEyAGo9t7YYMl0Gs'
        ],
        'skyblock' => [
            'url' => 'https://panel.gamehosting.vn/api/client/servers/889ecbac/command',
            'api_key' => 'ptlc_DzHfXQUGVnsUhaA9cz6M27J47hRIEyAGo9t7YYMl0Gs'
        ]
    ];

    if (!isset($pterodactyl_config[$server])) {
        error_log("Unknown server: $server");
        return false;
    }

    $config = $pterodactyl_config[$server];

    // Tạo lệnh game
    $command = "dotman napthucong $username $amount -f";

    // Chuẩn bị dữ liệu gửi
    $data = json_encode(['command' => $command]);

    // Cấu hình cURL
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $config['url'],
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $data,
        CURLOPT_HTTPHEADER => [
            'Accept: application/json',
            'Content-Type: application/json',
            'Authorization: Bearer ' . $config['api_key']
        ],
        CURLOPT_TIMEOUT => 30
    ]);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        error_log("cURL error: $error");
        return false;
    }

    if ($http_code === 204) {
        // Thành công
        echo "Command sent successfully: $command\n";
        return true;
    } else {
        error_log("Pterodactyl API error: HTTP $http_code, Response: $response");
        return false;
    }
}
?>
