<?php
// Debug script để tìm API endpoint đúng
echo "<h2>Debug Pterodactyl API</h2>";

$base_url = 'https://panel.gamehosting.vn';
$api_key = 'ptlc_DzHfXQUGVnsUhaA9cz6M27J47hRIEyAGo9t7YYMl0Gs';
$server_id = '8661ab4b';

// Test các endpoint khác nhau
$endpoints_to_test = [
    'Server Info' => "/api/client/servers/$server_id",
    'Server Resources' => "/api/client/servers/$server_id/resources", 
    'Server Command' => "/api/client/servers/$server_id/command",
    'Server Power' => "/api/client/servers/$server_id/power",
    'Server Websocket' => "/api/client/servers/$server_id/websocket",
    'List Servers' => "/api/client",
    'Account Info' => "/api/client/account"
];

foreach ($endpoints_to_test as $name => $endpoint) {
    echo "<h3>Test: $name</h3>";
    echo "<p><strong>URL:</strong> <code>$base_url$endpoint</code></p>";
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $base_url . $endpoint,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_HTTPHEADER => [
            'Accept: application/json',
            'Content-Type: application/json',
            'Authorization: Bearer ' . $api_key
        ],
        CURLOPT_TIMEOUT => 10
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    
    if ($error) {
        echo "❌ <strong>cURL Error:</strong> $error<br>";
    } else {
        echo "📋 <strong>HTTP Code:</strong> $http_code<br>";
        
        if ($http_code === 200) {
            echo "✅ <strong>Status:</strong> Success!<br>";
            $data = json_decode($response, true);
            if ($data) {
                echo "<strong>Response Keys:</strong> " . implode(', ', array_keys($data)) . "<br>";
                if (isset($data['data'])) {
                    echo "<strong>Data Keys:</strong> " . implode(', ', array_keys($data['data'])) . "<br>";
                }
            }
        } elseif ($http_code === 401) {
            echo "❌ <strong>Status:</strong> Unauthorized - API key invalid<br>";
        } elseif ($http_code === 404) {
            echo "❌ <strong>Status:</strong> Not Found<br>";
        } elseif ($http_code === 403) {
            echo "❌ <strong>Status:</strong> Forbidden<br>";
        } else {
            echo "❌ <strong>Status:</strong> Error HTTP $http_code<br>";
        }
        
        if (!empty($response) && strlen($response) < 1000) {
            echo "<strong>Response:</strong><br>";
            echo "<pre>" . htmlspecialchars($response) . "</pre>";
        } elseif (!empty($response)) {
            echo "<strong>Response (first 500 chars):</strong><br>";
            echo "<pre>" . htmlspecialchars(substr($response, 0, 500)) . "...</pre>";
        }
    }
    
    echo "</div>";
}

// Test POST command nếu có endpoint hoạt động
echo "<hr><h3>Test POST Command (nếu server info thành công):</h3>";

// Thử với endpoint command
$command_url = "$base_url/api/client/servers/$server_id/command";
$test_command = "list";

echo "<p><strong>Testing command:</strong> <code>$test_command</code></p>";
echo "<p><strong>URL:</strong> <code>$command_url</code></p>";

$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL => $command_url,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode(['command' => $test_command]),
    CURLOPT_HTTPHEADER => [
        'Accept: application/json',
        'Content-Type: application/json',
        'Authorization: Bearer ' . $api_key
    ],
    CURLOPT_TIMEOUT => 10
]);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;'>";

if ($error) {
    echo "❌ <strong>cURL Error:</strong> $error<br>";
} else {
    echo "📋 <strong>HTTP Code:</strong> $http_code<br>";
    
    if ($http_code === 204) {
        echo "✅ <strong>Status:</strong> Command sent successfully!<br>";
    } elseif ($http_code === 401) {
        echo "❌ <strong>Status:</strong> Unauthorized<br>";
    } elseif ($http_code === 404) {
        echo "❌ <strong>Status:</strong> Endpoint not found<br>";
    } elseif ($http_code === 502) {
        echo "⚠️ <strong>Status:</strong> Server offline<br>";
    } else {
        echo "❌ <strong>Status:</strong> Error HTTP $http_code<br>";
    }
    
    if (!empty($response)) {
        echo "<strong>Response:</strong><br>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
    }
}

echo "</div>";

echo "<hr>";
echo "<h3>Kết luận:</h3>";
echo "<ul>";
echo "<li>Nếu <strong>Account Info</strong> hoạt động → API key đúng</li>";
echo "<li>Nếu <strong>List Servers</strong> hoạt động → Có thể lấy danh sách server</li>";
echo "<li>Nếu <strong>Server Info</strong> hoạt động → Server ID đúng</li>";
echo "<li>Nếu <strong>Server Command</strong> hoạt động → Có thể gửi lệnh</li>";
echo "</ul>";

echo "<p><strong>Sau khi debug xong, hãy xóa file này!</strong></p>";
?>

<style>
pre {
    background: #e9ecef;
    padding: 10px;
    border-radius: 3px;
    overflow-x: auto;
    max-height: 300px;
}

code {
    background: #e9ecef;
    padding: 2px 5px;
    border-radius: 3px;
    font-family: monospace;
}
</style>
