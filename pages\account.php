<?php
// Get user data
if (!isset($_SESSION['user_id'])) {
    $_SESSION['message'] = 'Phiên đăng nhập không hợp lệ. Vui lòng đăng nhập lại.';
    $_SESSION['message_type'] = 'danger';
    header('Location: /login');
    exit;
}

$user = getUserData($_SESSION['user_id']);

// If user not found, redirect to login
if (!$user) {
    $_SESSION['message'] = 'Không tìm thấy thông tin tài khoản. Vui lòng đăng nhập lại.';
    $_SESSION['message_type'] = 'danger';
    header('Location: /logout');
    exit;
}

// Format dates
$created_at = isset($user['creation_date']) && !empty($user['creation_date']) ? new DateTime($user['creation_date']) : null;
$last_login = isset($user['last_seen']) && !empty($user['last_seen']) ? new DateTime($user['last_seen']) : null;
?>

<!-- Custom CSS for this page -->
<style>
    .account-container {
        margin-top: 2rem;
        margin-bottom: 3rem;
    }

    .profile-card {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }

    .profile-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    }

    .profile-header {
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        padding: 2rem 0;
        position: relative;
    }

    .avatar-container {
        position: relative;
        width: 150px;
        height: 150px;
        margin: 0 auto;
    }

    .minecraft-avatar {
        width: 150px;
        height: 150px;
        border: 5px solid #fff;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        transition: transform 0.3s ease;
    }

    .minecraft-avatar:hover {
        transform: scale(1.05);
    }

    .profile-info {
        padding: 1.5rem;
        background-color: #fff;
    }

    .nav-pills .nav-link {
        border-radius: 10px;
        padding: 0.8rem 1.2rem;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
    }

    .nav-pills .nav-link:hover {
        background-color: rgba(var(--primary-rgb), 0.1);
        transform: translateX(5px);
    }

    .nav-pills .nav-link.active {
        background-color: var(--primary);
        color: white;
        box-shadow: 0 5px 15px rgba(var(--primary-rgb), 0.3);
    }

    .nav-pills .nav-link i {
        margin-right: 10px;
        font-size: 1.1rem;
    }

    .info-card {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        height: 100%;
    }

    .info-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .info-card .card-header {
        background-color: #fff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        padding: 1.2rem 1.5rem;
    }

    .info-card .card-header h6 {
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
    }

    .info-card .card-body {
        padding: 1.5rem;
    }

    .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.8rem 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .info-label {
        color: var(--gray-600);
        font-size: 0.95rem;
    }

    .info-value {
        font-weight: 600;
        color: var(--gray-800);
    }

    .welcome-card {
        border-radius: 15px;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
    }

    .login-history-card {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
    }

    .login-history-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.2rem 1.5rem;
        background-color: #fff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .login-history-header h6 {
        margin: 0;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .toggle-btn {
        background: none;
        border: none;
        color: var(--primary);
        font-size: 0.9rem;
        cursor: pointer;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        transition: all 0.3s ease;
    }

    .toggle-btn:hover {
        background-color: rgba(var(--primary-rgb), 0.1);
    }

    .login-history-table {
        width: 100%;
    }

    .login-history-table th {
        background-color: rgba(var(--primary-rgb), 0.05);
        color: var(--gray-700);
        font-weight: 600;
        padding: 1rem;
        text-align: left;
    }

    .login-history-table td {
        padding: 1rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .login-history-table tr:last-child td {
        border-bottom: none;
    }

    .login-history-table tr:hover {
        background-color: rgba(var(--primary-rgb), 0.03);
    }

    .edit-profile-btn {
        padding: 0.8rem 2rem;
        border-radius: 50px;
        font-weight: 600;
        box-shadow: 0 5px 15px rgba(var(--primary-rgb), 0.3);
        transition: all 0.3s ease;
    }

    .edit-profile-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(var(--primary-rgb), 0.4);
    }

    @media (max-width: 767.98px) {
        .profile-card {
            margin-bottom: 2rem;
        }
    }
</style>

<div class="container account-container">
    <div class="row">
        <!-- Sidebar / Profile Card -->
        <div class="col-lg-4 mb-4">
            <div class="profile-card">
                <div class="profile-header">
                    <div class="avatar-container">
                        <img src="<?php echo getMinecraftAvatarUrl($user['last_name'], 'avatar', 150); ?>" alt="Minecraft Avatar" class="minecraft-avatar rounded-circle">
                    </div>
                </div>
                <div class="profile-info">
                    <div class="text-center mb-4">
                        <h4 class="font-weight-bold mb-1"><?php echo $user['last_name'] ?? $user['unique_id']; ?></h4>
                        <p class="text-muted"><?php echo $user['email'] ?? '<span class="text-danger">Chưa có email</span>'; ?></p>
                    </div>

                    <ul class="nav nav-pills flex-column">
                        <li class="nav-item">
                            <a href="/account" class="nav-link active">
                                <i class="fas fa-tachometer-alt"></i> Trang chủ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/profile" class="nav-link">
                                <i class="fas fa-user-edit"></i> Chỉnh sửa hồ sơ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/logout" class="nav-link text-danger">
                                <i class="fas fa-sign-out-alt"></i> Đăng xuất
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Welcome Card -->
            <div class="welcome-card">
                <div class="d-flex align-items-center">
                    <div class="mr-3">
                        <i class="fas fa-user-circle fa-3x text-primary"></i>
                    </div>
                    <div>
                        <h4 class="mb-1">Xin chào, <?php echo isset($user['last_name']) && $user['last_name'] ? $user['last_name'] : $user['unique_id']; ?>!</h4>
                        <p class="mb-0">Đây là trang quản lý tài khoản của bạn, nơi bạn có thể quản lý hồ sơ và cài đặt tài khoản.</p>
                    </div>
                </div>
            </div>

            <!-- Info Cards -->
            <div class="row mb-4">
                <div class="col-md-6 mb-4">
                    <div class="info-card">
                        <div class="card-header">
                            <h6><i class="fas fa-id-card text-primary mr-2"></i> Thông tin cá nhân</h6>
                        </div>
                        <div class="card-body">
                            <div class="info-item">
                                <span class="info-label">Tên tài khoản</span>
                                <span class="info-value"><?php echo $user['last_name']; ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Mã định danh</span>
                                <span class="info-value"><?php echo $user['unique_id']; ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Email</span>
                                <span class="info-value"><?php echo $user['email'] ?: '<span class="text-danger">Chưa cập nhật</span>'; ?></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 mb-4">
                    <div class="info-card">
                        <div class="card-header">
                            <h6><i class="fas fa-history text-primary mr-2"></i> Hoạt động tài khoản</h6>
                        </div>
                        <div class="card-body">
                            <div class="info-item">
                                <span class="info-label">Ngày tạo tài khoản</span>
                                <span class="info-value"><?php echo $created_at ? $created_at->format('d/m/Y H:i') : 'Không xác định'; ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Đăng nhập gần nhất</span>
                                <span class="info-value"><?php echo $last_login ? $last_login->format('d/m/Y H:i') : 'Chưa có'; ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Login History -->
            <div class="login-history-card mb-4">
                <div class="login-history-header">
                    <h6><i class="fas fa-history text-primary mr-2"></i> Lịch sử đăng nhập</h6>
                    <button class="toggle-btn" type="button" data-toggle="collapse" data-target="#loginHistoryCollapse" aria-expanded="true" aria-controls="loginHistoryCollapse">
                        <i class="fas fa-chevron-up mr-1"></i> Ẩn / Hiện
                    </button>
                </div>
                <div class="collapse show" id="loginHistoryCollapse">
                    <div class="table-responsive">
                        <table class="login-history-table">
                            <thead>
                                <tr>
                                    <th>Thời gian</th>
                                    <th>IP</th>
                                    <th>Quốc gia</th>
                                    <th>Nguồn</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                // Lấy lịch sử đăng nhập
                                $loginHistory = $nlogin->getLoginHistory($_SESSION['user_id']);

                                if (empty($loginHistory)) {
                                    echo '<tr><td colspan="4" class="text-center py-4">Chưa có lịch sử đăng nhập</td></tr>';
                                } else {
                                    foreach ($loginHistory as $login) {
                                        $loginTime = new DateTime($login['login_time']);
                                        echo '<tr>';
                                        echo '<td>' . $loginTime->format('H:i d-m-Y') . '</td>';
                                        echo '<td>' . htmlspecialchars($login['ip_address']) . '</td>';
                                        echo '<td>' . htmlspecialchars($login['country_code']) . '</td>';
                                        echo '<td>' . htmlspecialchars($login['source']) . '</td>';
                                        echo '</tr>';
                                    }
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Edit Profile Button -->
            <div class="text-center">
                <a href="/profile" class="btn btn-primary edit-profile-btn">
                    <i class="fas fa-user-edit mr-2"></i> Chỉnh sửa hồ sơ
                </a>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript for toggle button icon -->
<script>
    $(document).ready(function() {
        $('#loginHistoryCollapse').on('show.bs.collapse', function () {
            $('.toggle-btn i').removeClass('fa-chevron-down').addClass('fa-chevron-up');
        });

        $('#loginHistoryCollapse').on('hide.bs.collapse', function () {
            $('.toggle-btn i').removeClass('fa-chevron-up').addClass('fa-chevron-down');
        });
    });
</script>
