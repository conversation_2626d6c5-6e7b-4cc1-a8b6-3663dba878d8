<!-- Hero Section with Animated Background -->
<section class="hero-section">
  <div class="container">
    <div class="hero-grid">
      <div class="hero-content">
        <h1 class="hero-title">Chào mừng đến với <span class="highlight"><?php echo SITE_NAME; ?></span></h1>
        <p class="hero-subtitle">Hệ thống quản lý tài khoản hiện đại, bảo mật cao dành cho cộng đồng DPTMC</p>
        
        <div class="feature-list">
          <div class="feature-item">
            <div class="feature-icon bg-primary">
              <i class="fas fa-shield-alt"></i>
            </div>
            <span>Bảo mật đa lớp</span>
          </div>
          <div class="feature-item">
            <div class="feature-icon bg-success">
              <i class="fas fa-bolt"></i>
            </div>
            <span>T<PERSON>y cập nhanh chóng</span>
          </div>
          <div class="feature-item">
            <div class="feature-icon bg-info">
              <i class="fas fa-chart-line"></i>
            </div>
            <span>Theo dõi hoạt động</span>
          </div>
        </div>
        
        <div class="hero-actions">
          <?php if (!$loggedIn): ?>
            <a href="/register" class="btn btn-primary btn-lg">
              <i class="fas fa-rocket mr-2"></i> Bắt đầu ngay
            </a>
            <a href="/login" class="btn btn-outline-light btn-lg ml-3">
              <i class="fas fa-sign-in-alt mr-2"></i> Đăng nhập
            </a>
          <?php else: ?>
            <a href="/account" class="btn btn-primary btn-lg">
              <i class="fas fa-user-cog mr-2"></i> Tài khoản của tôi
            </a>
          <?php endif; ?>
        </div>
      </div>
      
      <div class="hero-visual">
        <div class="visual-container">
          <img src="assets/images/android-chrome-192x192.png" alt="Hệ thống quản lý tài khoản" class="main-visual">
          <div class="visual-dots"></div>
          <div class="visual-circle"></div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Stats Section -->
<section class="stats-section">
  <div class="container">
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-value">1,000+</div>
        <div class="stat-label">Thành viên</div>
        <div class="stat-icon">
          <i class="fas fa-users"></i>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-value">99.9%</div>
        <div class="stat-label">Thời gian hoạt động</div>
        <div class="stat-icon">
          <i class="fas fa-clock"></i>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-value">24/7</div>
        <div class="stat-label">Hỗ trợ</div>
        <div class="stat-icon">
          <i class="fas fa-headset"></i>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-value">100%</div>
        <div class="stat-label">Bảo mật</div>
        <div class="stat-icon">
          <i class="fas fa-lock"></i>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Features Section -->
<section class="features-section">
  <div class="container">
    <div class="section-header">
      <h2>Tính năng nổi bật</h2>
      <p>Những lý do bạn nên sử dụng hệ thống của chúng tôi</p>
    </div>
    
    <div class="features-grid">
      <div class="feature-card">
        <div class="feature-header">
          <div class="feature-icon-circle bg-primary-light">
            <i class="fas fa-user-shield text-primary"></i>
          </div>
          <h3>Bảo mật cao cấp</h3>
        </div>
        <p>Mã hóa dữ liệu tiên tiến cùng xác thực 2 lớp bảo vệ tài khoản của bạn</p>
        <a href="#" class="feature-link">Tìm hiểu thêm <i class="fas fa-arrow-right"></i></a>
      </div>
      
      <div class="feature-card">
        <div class="feature-header">
          <div class="feature-icon-circle bg-success-light">
            <i class="fas fa-tachometer-alt text-success"></i>
          </div>
          <h3>Quản lý tập trung</h3>
        </div>
        <p>Tất cả thông tin tài khoản được tổ chức khoa học, dễ dàng truy cập</p>
        <a href="#" class="feature-link">Tìm hiểu thêm <i class="fas fa-arrow-right"></i></a>
      </div>
      
      <div class="feature-card">
        <div class="feature-header">
          <div class="feature-icon-circle bg-info-light">
            <i class="fas fa-history text-info"></i>
          </div>
          <h3>Lịch sử chi tiết</h3>
        </div>
        <p>Theo dõi mọi hoạt động đăng nhập và thay đổi thông tin tài khoản</p>
        <a href="#" class="feature-link">Tìm hiểu thêm <i class="fas fa-arrow-right"></i></a>
      </div>
    </div>
  </div>
</section>

<!-- How It Works Section -->
<section class="process-section">
  <div class="container">
    <div class="section-header">
      <h2>Bắt đầu trong 3 bước đơn giản</h2>
      <p>Dễ dàng thiết lập và sử dụng hệ thống</p>
    </div>
    
    <div class="process-steps">
      <div class="process-line"></div>
      
      <div class="process-step">
        <div class="step-number">1</div>
        <div class="step-content">
          <h3>Đăng ký tài khoản</h3>
          <p>Tạo tài khoản mới với email và thông tin cá nhân</p>
        </div>
      </div>
      
      <div class="process-step">
        <div class="step-number">2</div>
        <div class="step-content">
          <h3>Xác thực email</h3>
          <p>Kích hoạt tài khoản qua liên kết trong email</p>
        </div>
      </div>
      
      <div class="process-step">
        <div class="step-number">3</div>
        <div class="step-content">
          <h3>Bắt đầu sử dụng</h3>
          <p>Truy cập vào tất cả dịch vụ của DPTMC</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
  <div class="container">
    <div class="cta-card">
      <div class="cta-content">
        <h2>Sẵn sàng trải nghiệm?</h2>
        <p>Đăng ký ngay để khám phá tất cả tính năng của hệ thống</p>
        
        <div class="cta-actions">
          <?php if (!$loggedIn): ?>
            <a href="/register" class="btn btn-light btn-lg">
              <i class="fas fa-user-plus mr-2"></i> Đăng ký miễn phí
            </a>
          <?php else: ?>
            <a href="/account" class="btn btn-light btn-lg">
              <i class="fas fa-user-cog mr-2"></i> Quản lý tài khoản
            </a>
          <?php endif; ?>
        </div>
      </div>
      <div class="cta-image">
        <img src="assets/images/apple-touch-icon.png" alt="Đăng ký ngay">
      </div>
    </div>
  </div>
</section>

<!-- Support Section -->
<section class="support-section">
  <div class="container">
    <div class="support-card">
      <div class="support-content">
        <h2>Bạn cần hỗ trợ?</h2>
        <p>Đội ngũ của chúng tôi luôn sẵn sàng giải đáp mọi thắc mắc</p>
        
        <div class="support-channels">
          <a href="https://discord.dptmc.com" class="support-channel">
            <div class="channel-icon discord">
              <i class="fab fa-discord"></i>
            </div>
            <span>Discord</span>
          </a>
          
          <a href="mailto:<EMAIL>" class="support-channel">
            <div class="channel-icon email">
              <i class="fas fa-envelope"></i>
            </div>
            <span>Email</span>
          </a>
          
          <a href="/faq" class="support-channel">
            <div class="channel-icon help">
              <i class="fas fa-question-circle"></i>
            </div>
            <span>FAQ</span>
          </a>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
/* Base Styles */
:root {
  --primary: #4361ee;
  --primary-light: #e6ecfe;
  --secondary: #3f37c9;
  --success: #4cc9f0;
  --info: #4895ef;
  --warning: #f72585;
  --dark: #212529;
  --light: #f8f9fa;
  --gray: #6c757d;
  --gray-light: #e9ecef;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: var(--dark);
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero Section */
.hero-section {
  padding: 80px 0;
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
  animation: rotate 20s linear infinite;
}

.hero-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  align-items: center;
  position: relative;
}

.hero-content {
  z-index: 2;
}

.hero-title {
  font-size: 2.8rem;
  font-weight: 800;
  margin-bottom: 20px;
  line-height: 1.2;
}

.highlight {
  color: #ffd166;
}

.hero-subtitle {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.feature-list {
  display: flex;
  gap: 20px;
  margin-bottom: 40px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 10px;
  background: rgba(255,255,255,0.1);
  padding: 8px 15px;
  border-radius: 50px;
  backdrop-filter: blur(5px);
}

.feature-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.hero-actions {
  display: flex;
  gap: 15px;
}

.hero-visual {
  position: relative;
  z-index: 1;
}

.visual-container {
  position: relative;
}

.main-visual {
  max-width: 100%;
  height: auto;
  position: relative;
  z-index: 2;
  animation: float 6s ease-in-out infinite;
}

.visual-dots {
  position: absolute;
  top: -30px;
  right: -30px;
  width: 150px;
  height: 150px;
  background-image: radial-gradient(rgba(255,255,255,0.3) 2px, transparent 2px);
  background-size: 20px 20px;
  z-index: 1;
}

.visual-circle {
  position: absolute;
  bottom: -20px;
  left: -20px;
  width: 120px;
  height: 120px;
  border: 3px dashed rgba(255,255,255,0.3);
  border-radius: 50%;
  z-index: 1;
  animation: rotate 30s linear infinite;
}

/* Stats Section */
.stats-section {
  padding: 60px 0;
  background: var(--light);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.stat-card {
  background: white;
  padding: 30px 20px;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary);
  margin-bottom: 5px;
}

.stat-label {
  font-size: 1rem;
  color: var(--gray);
  margin-bottom: 15px;
}

.stat-icon {
  font-size: 2rem;
  color: var(--primary-light);
}

/* Features Section */
.features-section {
  padding: 80px 0;
}

.section-header {
  text-align: center;
  margin-bottom: 50px;
}

.section-header h2 {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 15px;
  color: var(--dark);
}

.section-header p {
  font-size: 1.1rem;
  color: var(--gray);
  max-width: 600px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

.feature-card {
  background: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0,0,0,0.1);
}

.feature-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.feature-icon-circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.feature-card h3 {
  font-size: 1.3rem;
  margin-bottom: 15px;
}

.feature-card p {
  color: var(--gray);
  margin-bottom: 20px;
}

.feature-link {
  color: var(--primary);
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
}

.feature-link i {
  margin-left: 5px;
  transition: transform 0.3s ease;
}

.feature-link:hover i {
  transform: translateX(5px);
}

/* Process Section */
.process-section {
  padding: 80px 0;
  background: var(--light);
}

.process-steps {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.process-line {
  position: absolute;
  top: 0;
  left: 50px;
  width: 3px;
  height: 100%;
  background: var(--primary-light);
  z-index: 1;
}

.process-step {
  position: relative;
  padding-left: 100px;
  margin-bottom: 50px;
  z-index: 2;
}

.step-number {
  position: absolute;
  left: 30px;
  top: 0;
  width: 50px;
  height: 50px;
  background: var(--primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

.step-content h3 {
  font-size: 1.4rem;
  margin-bottom: 10px;
  color: var(--dark);
}

.step-content p {
  color: var(--gray);
}

/* CTA Section */
.cta-section {
  padding: 80px 0;
}

.cta-card {
  background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
  border-radius: 15px;
  padding: 60px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
}

.cta-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
}

.cta-content h2 {
  font-size: 2.2rem;
  margin-bottom: 15px;
}

.cta-content p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin-bottom: 25px;
}

.cta-image {
  width: 300px;
  animation: float 6s ease-in-out infinite;
}

/* Support Section */
.support-section {
  padding: 60px 0 80px;
}

.support-card {
  background: white;
  border-radius: 15px;
  padding: 50px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.05);
  text-align: center;
}

.support-card h2 {
  font-size: 2rem;
  margin-bottom: 15px;
}

.support-card p {
  font-size: 1.1rem;
  color: var(--gray);
  margin-bottom: 30px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.support-channels {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.support-channel {
  text-decoration: none;
  color: var(--dark);
  transition: transform 0.3s ease;
}

.support-channel:hover {
  transform: translateY(-5px);
}

.channel-icon {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  margin: 0 auto 15px;
  transition: all 0.3s ease;
}

.channel-icon.discord {
  background: rgba(114, 137, 218, 0.1);
  color: #7289da;
}

.channel-icon.email {
  background: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.channel-icon.help {
  background: rgba(32, 201, 151, 0.1);
  color: #20c997;
}

.support-channel:hover .channel-icon {
  transform: scale(1.1);
}

.support-channel span {
  font-weight: 600;
}

/* Animations */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive */
@media (max-width: 992px) {
  .hero-grid {
    grid-template-columns: 1fr;
  }
  
  .hero-visual {
    order: -1;
    max-width: 500px;
    margin: 0 auto;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .cta-card {
    flex-direction: column;
    text-align: center;
    padding: 40px;
  }
  
  .cta-image {
    margin-top: 30px;
    width: 200px;
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.2rem;
  }
  
  .feature-list {
    flex-wrap: wrap;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .process-step {
    padding-left: 80px;
  }
  
  .step-number {
    left: 15px;
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
}

@media (max-width: 576px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .hero-actions {
    flex-direction: column;
  }
  
  .hero-actions .btn {
    width: 100%;
    margin-bottom: 10px;
  }
  
  .hero-actions .btn:last-child {
    margin-bottom: 0;
  }
}
</style>