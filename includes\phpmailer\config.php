<?php
/**
 * PHPMailer Configuration
 *
 * This file contains the configuration for <PERSON><PERSON><PERSON>ail<PERSON> using Composer autoload
 */

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use <PERSON>HPMailer\PHPMailer\Exception;

// SMTP Configuration for dptcloud.vn
define('SMTP_HOST', 'webmail.dptcloud.vn');
define('SMTP_PORT', 587); // TLS port
define('SMTP_SECURE', 'tls'); // Using TLS
define('SMTP_AUTH', true);
define('SMTP_USER', '<EMAIL>'); // SMTP username
define('SMTP_PASS', 'Longhay1@'); // SMTP password

// Development Mode - Skip sending emails in development
define('DEV_MODE', false);  // Set to false in production

// Show OTP on screen (for testing purposes)
define('SHOW_OTP', false);  // Set to false in production

// Email Settings
define('EMAIL_FROM', '<EMAIL>');    // Must be same as SMTP_USER for dptcloud.vn
define('EMAIL_FROM_NAME', 'DPTMC.COM');        // Sender name
define('EMAIL_REPLY_TO', '<EMAIL>'); // Must be valid dptcloud.vn address
define('EMAIL_REPLY_TO_NAME', 'DPTMC.COM Support'); // Reply-to name

// Debug Settings
define('SMTP_DEBUG', SMTP::DEBUG_OFF); // Debug level using PHPMailer constants

// Performance optimization settings
define('SMTP_TIMEOUT', 5); // Reduced connection timeout for faster response
define('SMTP_KEEP_ALIVE', true); // Keep connection alive for multiple emails
define('SMTP_OPTIONS', [
    'ssl' => [
        'verify_peer' => false, // Disable peer verification for faster connection
        'verify_peer_name' => false, // Disable peer name verification for faster connection
        'allow_self_signed' => true, // Allow self-signed certificates
        'verify_depth' => 0, // Minimum verification depth
        'SNI_enabled' => true
    ]
]);
?>
