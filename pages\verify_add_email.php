<?php
// Kiểm tra đăng nhập
if (!isset($_SESSION['user_id'])) {
    $_SESSION['message'] = 'Vui lòng đăng nhập để tiếp tục.';
    $_SESSION['message_type'] = 'danger';
    header('Location: /login');
    exit;
}

// Kiểm tra xem có email tạm thời trong session không
if (!isset($_SESSION['temp_email']) || !isset($_SESSION['temp_email_user_id'])) {
    $_SESSION['message'] = 'Phiên làm việc đã hết hạn. Vui lòng thêm email lại.';
    $_SESSION['message_type'] = 'danger';
    header('Location: /add-email');
    exit;
}

// Kiểm tra xem user_id trong session có khớp với temp_email_user_id không
if ($_SESSION['user_id'] != $_SESSION['temp_email_user_id']) {
    $_SESSION['message'] = 'Phiên làm việc không hợp lệ. Vui lòng thêm email lại.';
    $_SESSION['message_type'] = 'danger';
    header('Location: /add-email');
    exit;
}

$email = $_SESSION['temp_email'];
$user_id = $_SESSION['user_id'];

// Lấy thông tin người dùng
$user = getUserData($user_id);
if (!$user) {
    $_SESSION['message'] = 'Không tìm thấy thông tin tài khoản. Vui lòng đăng nhập lại.';
    $_SESSION['message_type'] = 'danger';
    header('Location: /logout');
    exit;
}

// Xử lý form xác minh OTP
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Lấy OTP từ form
    $otp = '';
    for ($i = 1; $i <= 6; $i++) {
        $otp .= isset($_POST['otp' . $i]) ? $_POST['otp' . $i] : '';
    }

    // Xác minh OTP

    // Kiểm tra OTP
    if (strlen($otp) !== 6 || !ctype_digit($otp)) {
        $_SESSION['message'] = 'Vui lòng nhập đầy đủ mã xác minh 6 số';
        $_SESSION['message_type'] = 'danger';
    } else {
        // Xác minh OTP
        try {
            global $conn;

            // Tìm token với OTP khớp
            $stmt = $conn->prepare("SELECT `key`, value FROM nlogin_data WHERE `key` LIKE 'email_verification_token_add_email_%'");
            $stmt->execute();

            // Tìm token xác minh

            $found = false;
            $tokenKey = '';

            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $tokenData = json_decode($row['value'], true);

                if (isset($tokenData['otp']) && $tokenData['otp'] === $otp &&
                    isset($tokenData['user_id']) && $tokenData['user_id'] == $user_id &&
                    isset($tokenData['type']) && $tokenData['type'] == 'add_email') {

                    // Tìm thấy OTP khớp

                    // Kiểm tra xem token đã hết hạn chưa
                    if (strtotime($tokenData['expires']) < time()) {
                        $_SESSION['message'] = 'Mã xác minh đã hết hạn. Vui lòng yêu cầu mã mới.';
                        $_SESSION['message_type'] = 'danger';
                        break;
                    }

                    // Kiểm tra xem token đã được sử dụng chưa
                    if (isset($tokenData['used']) && $tokenData['used']) {
                        $_SESSION['message'] = 'Mã xác minh đã được sử dụng. Vui lòng yêu cầu mã mới.';
                        $_SESSION['message_type'] = 'danger';
                        break;
                    }

                    // Đánh dấu token đã được sử dụng và xác minh
                    $tokenData['used'] = true;
                    $tokenData['verified'] = true;
                    $tokenData['verified_at'] = date('Y-m-d H:i:s');

                    $updateStmt = $conn->prepare("UPDATE nlogin_data SET value = :value WHERE `key` = :key");
                    $updatedValue = json_encode($tokenData);
                    $updateStmt->bindParam(':value', $updatedValue);
                    $updateStmt->bindParam(':key', $row['key']);
                    $updateStmt->execute();

                    // Cập nhật email cho tài khoản
                    $emailUpdateStmt = $conn->prepare("UPDATE nlogin SET email = :email WHERE ai = :user_id");
                    $emailUpdateStmt->bindParam(':email', $tokenData['email']);
                    $emailUpdateStmt->bindParam(':user_id', $user_id);
                    $emailUpdateStmt->execute();

                    // Kiểm tra xem cột email_verified đã tồn tại chưa
                    try {
                        // Kiểm tra xem cột đã tồn tại chưa
                        $checkColumnStmt = $conn->prepare("SHOW COLUMNS FROM nlogin LIKE 'email_verified'");
                        $checkColumnStmt->execute();

                        if ($checkColumnStmt->rowCount() == 0) {
                            // Cột chưa tồn tại, thêm cột mới
                            $conn->query("ALTER TABLE nlogin ADD COLUMN email_verified TINYINT(1) DEFAULT 0");
                        }

                        // Cập nhật trạng thái xác minh email
                        $verifyStmt = $conn->prepare("UPDATE nlogin SET email_verified = 1 WHERE ai = :user_id");
                        $verifyStmt->bindParam(':user_id', $user_id);
                        $verifyStmt->execute();
                    } catch(PDOException $e) {
                        // Bỏ qua lỗi nếu không thể thêm cột
                    }

                    $found = true;
                    $tokenKey = $row['key'];
                    break;
                }
            }

            if ($found) {
                // Xóa token sau khi xác minh thành công
                $deleteStmt = $conn->prepare("DELETE FROM nlogin_data WHERE `key` = :key");
                $deleteStmt->bindParam(':key', $tokenKey);
                $deleteResult = $deleteStmt->execute();

                // Xóa tất cả các token hết hạn hoặc đã sử dụng
                $cleanupStmt = $conn->prepare("SELECT `key`, value FROM nlogin_data WHERE `key` LIKE 'email_verification_token_%'");
                $cleanupStmt->execute();

                while ($row = $cleanupStmt->fetch(PDO::FETCH_ASSOC)) {
                    $data = json_decode($row['value'], true);

                    // Xóa token nếu đã hết hạn hoặc đã sử dụng
                    if ((isset($data['expires']) && strtotime($data['expires']) < time()) ||
                        (isset($data['used']) && $data['used'] === true)) {
                        $cleanupDeleteStmt = $conn->prepare("DELETE FROM nlogin_data WHERE `key` = :key");
                        $cleanupDeleteStmt->bindParam(':key', $row['key']);
                        $cleanupDeleteStmt->execute();
                    }
                }

                // Xóa biến session tạm thời
                unset($_SESSION['temp_email']);
                unset($_SESSION['temp_email_user_id']);

                // Gửi email xác nhận
                require_once 'includes/phpmailer/mailer.php';
                $username = $user['last_name'] ?? $user['unique_id'];
                $emailResult = sendEmailVerification($email, $username);

                // Chuyển hướng đến trang tài khoản
                $_SESSION['message'] = 'Email đã được xác minh và cập nhật thành công!';
                $_SESSION['message_type'] = 'success';
                header('Location: /account');
                exit;
            } else {
                $_SESSION['message'] = 'Mã xác minh không chính xác. Vui lòng thử lại.';
                $_SESSION['message_type'] = 'danger';
            }
        } catch(PDOException $e) {
            $_SESSION['message'] = 'Đã xảy ra lỗi. Vui lòng thử lại sau.';
            $_SESSION['message_type'] = 'danger';
        }
    }
}

$username = $user['last_name'] ?? $user['unique_id'];
?>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-5">
        <div class="card shadow-sm border-0 mb-5">
            <div class="card-header bg-white">
                <h4 class="text-center mb-0"><i class="fas fa-envelope-open-text text-primary"></i> Xác minh Email</h4>
            </div>
            <div class="card-body p-4">
                <div class="alert alert-info shadow-sm">
                    <i class="fas fa-info-circle mr-2"></i>
                    Chúng tôi đã gửi mã xác minh 6 số đến email <strong><?php echo $email; ?></strong>. Vui lòng kiểm tra hộp thư đến và nhập mã xác minh để hoàn tất quá trình thêm email.
                </div>

                <div class="text-center mb-4">
                    <i class="fas fa-envelope-open text-primary" style="font-size: 3rem;"></i>
                </div>

                <?php if (defined('SHOW_OTP') && SHOW_OTP === true && isset($_SESSION['debug_otp'])): ?>
                <div class="alert alert-warning">
                    <strong>Debug Mode:</strong> Mã OTP: <?php echo $_SESSION['debug_otp']; ?>
                </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['message']) && $_SESSION['message_type'] == 'danger'): ?>
                <div class="alert alert-danger mb-4">
                    <i class="fas fa-exclamation-circle mr-2"></i> <?php echo $_SESSION['message']; ?>
                </div>
                <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
                <?php endif; ?>

                <form method="POST" action="/verify-add-email" class="needs-validation" novalidate>
                    <div class="form-group">
                        <label class="form-label">Mã xác minh</label>
                        <div class="d-flex justify-content-between mb-3">
                            <?php for ($i = 1; $i <= 6; $i++): ?>
                            <input type="text" class="form-control text-center otp-input" id="otp<?php echo $i; ?>" name="otp<?php echo $i; ?>" maxlength="1" pattern="[0-9]" required style="width: 45px; height: 45px; font-size: 1.2rem;">
                            <?php endfor; ?>
                        </div>
                        <div class="invalid-feedback">
                            Vui lòng nhập đầy đủ mã xác minh 6 số.
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-check-circle"></i> Xác minh
                        </button>
                    </div>
                </form>

                <div class="text-center mt-4">
                    <p class="text-muted">Không nhận được mã? <a href="/add-email" class="text-primary">Gửi lại mã</a></p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-focus next input when typing OTP
document.addEventListener('DOMContentLoaded', function() {
    const otpInputs = document.querySelectorAll('.otp-input');

    otpInputs.forEach((input, index) => {
        input.addEventListener('keyup', function(e) {
            // If a digit was entered, focus the next input
            if (this.value.length === 1 && /^[0-9]$/.test(this.value)) {
                if (index < otpInputs.length - 1) {
                    otpInputs[index + 1].focus();
                }
            }

            // If backspace was pressed, focus the previous input
            if (e.key === 'Backspace' && index > 0 && this.value.length === 0) {
                otpInputs[index - 1].focus();
            }
        });

        // Prevent non-numeric input
        input.addEventListener('keypress', function(e) {
            if (!/^[0-9]$/.test(e.key)) {
                e.preventDefault();
            }
        });

        // Handle paste event
        input.addEventListener('paste', function(e) {
            e.preventDefault();
            const pasteData = e.clipboardData.getData('text');
            const digits = pasteData.match(/\d/g);

            if (digits && digits.length > 0) {
                for (let i = 0; i < otpInputs.length && i < digits.length; i++) {
                    otpInputs[i].value = digits[i];
                    if (i < otpInputs.length - 1 && i < digits.length - 1) {
                        otpInputs[i + 1].focus();
                    }
                }
            }
        });
    });

    // Focus first input on page load
    otpInputs[0].focus();
});
</script>
