<?php
// Debug script để kiểm tra lỗi API
echo "<h2>Debug API Error</h2>";

// Test 1: Kiểm tra file tồn tại
echo "<h3>1. Kiểm tra file API:</h3>";
$api_file = 'api/check-payment.php';
if (file_exists($api_file)) {
    echo "✅ File tồn tại: $api_file<br>";
    echo "📏 Kích thước: " . filesize($api_file) . " bytes<br>";
    echo "🕒 Modified: " . date('Y-m-d H:i:s', filemtime($api_file)) . "<br>";
} else {
    echo "❌ File không tồn tại: $api_file<br>";
}

// Test 2: Kiểm tra syntax PHP
echo "<h3>2. Kiểm tra syntax PHP:</h3>";
$output = shell_exec("php -l $api_file 2>&1");
if (strpos($output, 'No syntax errors') !== false) {
    echo "✅ Syntax PHP OK<br>";
} else {
    echo "❌ Syntax Error:<br>";
    echo "<pre>" . htmlspecialchars($output) . "</pre>";
}

// Test 3: Kiểm tra config
echo "<h3>3. Kiểm tra config:</h3>";
try {
    require_once 'config/config.php';
    echo "✅ Config load thành công<br>";
    
    if (isset($conn)) {
        echo "✅ Database connection OK<br>";
    } else {
        echo "❌ Database connection không tồn tại<br>";
    }
} catch (Exception $e) {
    echo "❌ Config error: " . $e->getMessage() . "<br>";
}

// Test 4: Kiểm tra functions
echo "<h3>4. Kiểm tra functions:</h3>";
try {
    require_once 'includes/functions.php';
    echo "✅ Functions load thành công<br>";
    
    if (function_exists('getUserData')) {
        echo "✅ Function getUserData tồn tại<br>";
    } else {
        echo "❌ Function getUserData không tồn tại<br>";
    }
} catch (Exception $e) {
    echo "❌ Functions error: " . $e->getMessage() . "<br>";
}

// Test 5: Test API trực tiếp
echo "<h3>5. Test API trực tiếp:</h3>";

session_start();
if (!isset($_SESSION['user_id'])) {
    echo "❌ Chưa đăng nhập - không thể test API<br>";
    echo "<a href='/login'>Đăng nhập để test</a><br>";
} else {
    echo "✅ Đã đăng nhập: User ID " . $_SESSION['user_id'] . "<br>";
    
    if (isset($_SESSION['pending_transaction'])) {
        echo "✅ Có pending transaction<br>";
        echo "<pre>" . print_r($_SESSION['pending_transaction'], true) . "</pre>";
        
        // Test API call
        echo "<h4>Test API Call:</h4>";
        
        $test_data = json_encode([
            'transaction_code' => $_SESSION['pending_transaction']['transaction_code']
        ]);
        
        echo "<strong>Request data:</strong><br>";
        echo "<pre>" . htmlspecialchars($test_data) . "</pre>";
        
        // Simulate API call
        try {
            ob_start();
            $_POST = json_decode($test_data, true);
            $_SERVER['REQUEST_METHOD'] = 'POST';
            
            // Capture any output/errors
            include 'api/check-payment.php';
            $api_output = ob_get_clean();
            
            echo "<strong>API Output:</strong><br>";
            echo "<pre>" . htmlspecialchars($api_output) . "</pre>";
            
        } catch (Exception $e) {
            ob_end_clean();
            echo "❌ API Exception: " . $e->getMessage() . "<br>";
        } catch (Error $e) {
            ob_end_clean();
            echo "❌ API Fatal Error: " . $e->getMessage() . "<br>";
        }
        
    } else {
        echo "❌ Không có pending transaction<br>";
        echo "Tạo QR code trước để có transaction<br>";
    }
}

// Test 6: Kiểm tra error log
echo "<h3>6. Error Log gần đây:</h3>";
$error_logs = [
    '/var/log/apache2/error.log',
    '/var/log/nginx/error.log',
    '/var/log/php_errors.log',
    ini_get('error_log')
];

foreach ($error_logs as $log_file) {
    if ($log_file && file_exists($log_file)) {
        echo "<h4>$log_file:</h4>";
        $recent_errors = shell_exec("tail -20 $log_file | grep -i 'check-payment\\|Fatal\\|Parse'");
        if ($recent_errors) {
            echo "<pre>" . htmlspecialchars($recent_errors) . "</pre>";
        } else {
            echo "Không có lỗi gần đây<br>";
        }
        break;
    }
}

echo "<hr>";
echo "<h3>Hướng dẫn sửa lỗi:</h3>";
echo "<ol>";
echo "<li>Nếu có <strong>Syntax Error</strong> → Sửa code PHP</li>";
echo "<li>Nếu có <strong>Config Error</strong> → Kiểm tra database connection</li>";
echo "<li>Nếu có <strong>Functions Error</strong> → Kiểm tra file functions.php</li>";
echo "<li>Nếu có <strong>API Exception</strong> → Xem chi tiết lỗi</li>";
echo "<li>Kiểm tra <strong>Error Log</strong> để biết lỗi cụ thể</li>";
echo "</ol>";

echo "<p><strong>Sau khi debug xong, xóa file này!</strong></p>";
?>

<style>
pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
    max-height: 300px;
}

h4 {
    color: #333;
    margin-top: 15px;
}
</style>
