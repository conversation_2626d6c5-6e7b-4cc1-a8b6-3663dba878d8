<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_NAME; ?></title>
    <!-- Favicon và Logo -->
    <link rel="icon" href="assets/images/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="assets/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/images/favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="assets/images/apple-touch-icon.png">
    <link rel="manifest" href="site.webmanifest">
    <!-- Bootstrap vẫn được giữ lại cho grid system và một số tính năng -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css">
    <!-- Google Fonts - Roboto -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Theme hiện đại mới -->
    <link rel="stylesheet" href="assets/css/modern-theme.css">
    <!-- CSS gốc vẫn được giữ lại để tương thích -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <!-- Modern Navbar with Minecraft Style -->
    <nav class="navbar navbar-expand-lg fixed-top navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <img src="assets/images/favicon-32x32.png" alt="Logo" width="32" height="32" class="d-inline-block align-top mr-2">
                <?php echo SITE_NAME; ?>
            </a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav mr-auto">
                    <li class="nav-item <?php echo ($page == 'home') ? 'active' : ''; ?>">
                        <a class="nav-link" href="/">
                            <i class="fas fa-home"></i> Trang chủ
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <?php if ($loggedIn): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="navbarDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <?php
                                    $user = getUserData($_SESSION['user_id']);
                                    $username = $user ? ($user['last_name'] ?? $user['unique_id']) : 'Tài khoản';
                                ?>
                                <img src="<?php echo getMinecraftAvatarUrl($username, 'avatar', 24); ?>" alt="Avatar" class="mr-1" style="width: 24px; height: 24px;">
                                <?php echo $username; ?>
                            </a>
                            <div class="dropdown-menu dropdown-menu-right shadow-sm" aria-labelledby="navbarDropdown">
                                <a class="dropdown-item" href="/account">
                                    <i class="fas fa-id-card text-primary"></i> Tài khoản của tôi
                                </a>
                                <a class="dropdown-item" href="/profile">
                                    <i class="fas fa-user-edit text-primary"></i> Chỉnh sửa hồ sơ
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="/logout">
                                    <i class="fas fa-sign-out-alt text-danger"></i> Đăng xuất
                                </a>
                            </div>
                        </li>
                    <?php else: ?>
                        <li class="nav-item <?php echo ($page == 'login') ? 'active' : ''; ?>">
                            <a class="nav-link" href="/login">
                                <i class="fas fa-sign-in-alt"></i> Đăng nhập
                            </a>
                        </li>
                        <li class="nav-item <?php echo ($page == 'register') ? 'active' : ''; ?>">
                            <a class="nav-link btn btn-success text-white ml-2 px-3" href="/register">
                                <i class="fas fa-user-plus"></i> Đăng ký
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-5 pt-3">
        <?php if (isset($_SESSION['message'])): ?>
            <div class="alert alert-<?php echo $_SESSION['message_type']; ?> shadow-sm">
                <?php
                    echo $_SESSION['message'];
                    unset($_SESSION['message']);
                    unset($_SESSION['message_type']);
                ?>
            </div>
        <?php endif; ?>
