-- <PERSON><PERSON><PERSON> bảng lưu giao dịch nạp tiền QR
CREATE TABLE IF NOT EXISTS `nlogin_qr_transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `username` varchar(100) NOT NULL,
  `transaction_id` varchar(255) NOT NULL,
  `amount` int(11) NOT NULL,
  `server` varchar(50) NOT NULL,
  `transaction_code` varchar(100) NOT NULL,
  `payment_data` text,
  `created_at` datetime NOT NULL,
  `processed_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `transaction_id` (`transaction_id`),
  KEY `user_id` (`user_id`),
  KEY `transaction_code` (`transaction_code`),
  <PERSON><PERSON><PERSON> `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- <PERSON><PERSON><PERSON> bảng lưu giao dịch pending (chờ xử lý)
CREATE TABLE IF NOT EXISTS `nlogin_qr_pending` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `username` varchar(100) NOT NULL,
  `amount` int(11) NOT NULL,
  `server` varchar(50) NOT NULL,
  `transaction_code` varchar(100) NOT NULL,
  `created_at` datetime NOT NULL,
  `processed` tinyint(1) DEFAULT 0,
  `processed_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `transaction_code` (`transaction_code`),
  KEY `user_id` (`user_id`),
  KEY `processed` (`processed`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
