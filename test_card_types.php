<?php
// Test script để kiểm tra các loại thẻ và trạng thái
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<h2>Test Card Types & Status</h2>";

// Test các loại type khác nhau
echo "<h3>1. Test Card Type Names:</h3>";
$test_types = [
    1, 2, 3, 4, 5, 6, 7, 8,  // Số
    'VIETTEL', 'MOBIFONE', 'VINAPHONE', 'VIETNAMOBILE',  // Text
    '', null, 'UNKNOWN'  // Edge cases
];

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Input Type</th><th>Output Name</th></tr>";

foreach ($test_types as $type) {
    $name = getCardTypeName($type);
    echo "<tr>";
    echo "<td>" . htmlspecialchars(var_export($type, true)) . "</td>";
    echo "<td>" . htmlspecialchars($name) . "</td>";
    echo "</tr>";
}
echo "</table><br>";

// Test guessCardTypeFromSeri
echo "<h3>2. Test Guess Card Type from Seri:</h3>";
$test_seris = [
    'ODLPJLQS', 'SYRFIRPN', 'DGMWXQFU',  // Viettel patterns
    '123456789', '987654321',  // Mobifone patterns
    'VNP123456',  // Vinaphone pattern
    '--', '', null  // Edge cases
];

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Seri</th><th>Guessed Type</th></tr>";

foreach ($test_seris as $seri) {
    $guessed = guessCardTypeFromSeri($seri, '');
    echo "<tr>";
    echo "<td>" . htmlspecialchars($seri ?? 'NULL') . "</td>";
    echo "<td>" . htmlspecialchars($guessed) . "</td>";
    echo "</tr>";
}
echo "</table><br>";

// Test status
echo "<h3>3. Test Card Status:</h3>";
$test_records = [
    ['success' => 1, 'waiting' => 0, 'transaction_id' => 'TXN123'],
    ['success' => 0, 'waiting' => 1, 'transaction_id' => 'TXN456'],
    ['success' => 0, 'waiting' => 0, 'transaction_id' => 'TXN789'],
    ['success' => 0, 'waiting' => 0, 'transaction_id' => ''],
    ['success' => 0, 'waiting' => 0, 'transaction_id' => null],
];

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>Success</th><th>Waiting</th><th>Transaction ID</th><th>Status Text</th><th>Status Class</th></tr>";

foreach ($test_records as $record) {
    $status_text = getCardStatusText($record);
    $status_class = getCardStatusClass($record);
    
    echo "<tr>";
    echo "<td>" . $record['success'] . "</td>";
    echo "<td>" . $record['waiting'] . "</td>";
    echo "<td>" . htmlspecialchars($record['transaction_id'] ?? 'NULL') . "</td>";
    echo "<td>" . htmlspecialchars($status_text) . "</td>";
    echo "<td><span class='badge badge-" . $status_class . "'>" . $status_class . "</span></td>";
    echo "</tr>";
}
echo "</table><br>";

// Test với dữ liệu thực từ database
if ($card_conn) {
    echo "<h3>4. Test với dữ liệu thực từ database:</h3>";
    
    try {
        $stmt = $card_conn->query("SELECT * FROM dotman_napthe_log ORDER BY time DESC LIMIT 10");
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>UUID</th><th>Type (Raw)</th><th>Type (Processed)</th><th>Seri</th><th>Price</th><th>Status</th></tr>";
        
        foreach ($records as $record) {
            // Process record như trong hàm getCardRechargeHistory
            if (isset($record['type']) && !empty($record['type'])) {
                $type_name = getCardTypeName($record['type']);
            } else {
                $type_name = guessCardTypeFromSeri($record['seri'] ?? '', $record['transaction_id'] ?? '');
            }
            
            $status_text = getCardStatusText($record);
            $formatted_price = number_format($record['price'], 0, ',', '.') . ' VNĐ';
            
            echo "<tr>";
            echo "<td>" . $record['id'] . "</td>";
            echo "<td>" . substr($record['uuid'], 0, 8) . "...</td>";
            echo "<td>" . htmlspecialchars($record['type'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($type_name) . "</td>";
            echo "<td>" . htmlspecialchars($record['seri'] ?? 'NULL') . "</td>";
            echo "<td>" . $formatted_price . "</td>";
            echo "<td>" . htmlspecialchars($status_text) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } catch(PDOException $e) {
        echo "❌ Lỗi: " . $e->getMessage();
    }
} else {
    echo "❌ Không thể kết nối database lịch sử nạp thẻ";
}

echo "<br><hr>";
echo "<p><strong>Lưu ý:</strong> Sau khi test xong, hãy xóa file này để bảo mật!</p>";
?>
