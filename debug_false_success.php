<?php
// Debug script để tìm nguyên nhân báo thành công sai
require_once 'config/config.php';

echo "<h2>Debug False Success Issue</h2>";

session_start();

// 1. Ki<PERSON>m tra session
echo "<h3>1. Session Information:</h3>";
if (isset($_SESSION['user_id'])) {
    echo "✅ User ID: " . $_SESSION['user_id'] . "<br>";
    
    if (isset($_SESSION['pending_transaction'])) {
        echo "✅ Có pending transaction:<br>";
        echo "<pre>" . print_r($_SESSION['pending_transaction'], true) . "</pre>";
        
        $pending = $_SESSION['pending_transaction'];
        $time_since_created = time() - $pending['created_at'];
        echo "⏰ Thời gian từ khi tạo: $time_since_created giây<br>";
        echo "⏰ Còn lại: " . (60 - $time_since_created) . " giây<br>";
        
        if ($time_since_created > 60) {
            echo "❌ <strong><PERSON><PERSON><PERSON> dịch đã hết hạn!</strong><br>";
        }
    } else {
        echo "❌ Không có pending transaction<br>";
    }
} else {
    echo "❌ User chưa đăng nhập<br>";
}

// 2. Kiểm tra database pending
echo "<h3>2. Database Pending Transactions:</h3>";
try {
    if (isset($_SESSION['user_id'])) {
        $stmt = $conn->prepare("
            SELECT * FROM nlogin_qr_pending 
            WHERE user_id = :user_id 
            ORDER BY created_at DESC 
            LIMIT 5
        ");
        $stmt->bindParam(':user_id', $_SESSION['user_id']);
        $stmt->execute();
        $pending_txs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($pending_txs) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Transaction Code</th><th>Amount</th><th>Created</th><th>Processed</th><th>Processed At</th></tr>";
            
            foreach ($pending_txs as $tx) {
                $status = $tx['processed'] ? '✅ Yes' : '❌ No';
                $time_diff = time() - strtotime($tx['created_at']);
                
                echo "<tr>";
                echo "<td>{$tx['id']}</td>";
                echo "<td><code>{$tx['transaction_code']}</code></td>";
                echo "<td>" . number_format($tx['amount']) . "</td>";
                echo "<td>{$tx['created_at']} ({$time_diff}s ago)</td>";
                echo "<td>$status</td>";
                echo "<td>" . ($tx['processed_at'] ?: '-') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "Không có giao dịch pending nào.<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Lỗi database: " . $e->getMessage() . "<br>";
}

// 3. Kiểm tra completed transactions
echo "<h3>3. Completed Transactions (Recent):</h3>";
try {
    if (isset($_SESSION['user_id'])) {
        $stmt = $conn->prepare("
            SELECT * FROM nlogin_qr_transactions 
            WHERE user_id = :user_id 
            ORDER BY created_at DESC 
            LIMIT 3
        ");
        $stmt->bindParam(':user_id', $_SESSION['user_id']);
        $stmt->execute();
        $completed_txs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($completed_txs) {
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID</th><th>Transaction Code</th><th>Amount</th><th>Server</th><th>Created</th><th>Processed</th></tr>";
            
            foreach ($completed_txs as $tx) {
                $time_diff = time() - strtotime($tx['created_at']);
                
                echo "<tr>";
                echo "<td>{$tx['id']}</td>";
                echo "<td><code>{$tx['transaction_code']}</code></td>";
                echo "<td>" . number_format($tx['amount']) . "</td>";
                echo "<td>{$tx['server']}</td>";
                echo "<td>{$tx['created_at']} ({$time_diff}s ago)</td>";
                echo "<td>{$tx['processed_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "Không có giao dịch hoàn thành nào.<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Lỗi database: " . $e->getMessage() . "<br>";
}

// 4. Test API payment check
echo "<h3>4. Test API Payment Check:</h3>";
if (isset($_SESSION['pending_transaction'])) {
    $transaction_code = $_SESSION['pending_transaction']['transaction_code'];
    
    echo "<strong>Testing transaction:</strong> <code>$transaction_code</code><br>";
    
    // Simulate API call
    $test_data = json_encode(['transaction_code' => $transaction_code]);
    
    echo "<button onclick='testAPI()'>Test API Call</button><br>";
    echo "<div id='api-result'></div>";
    
    echo "<script>
    function testAPI() {
        const resultDiv = document.getElementById('api-result');
        resultDiv.innerHTML = '⏳ Testing...';
        
        fetch('/api/check-payment.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                transaction_code: '$transaction_code'
            })
        })
        .then(response => response.json())
        .then(data => {
            resultDiv.innerHTML = '<h4>API Response:</h4><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            
            if (data.success) {
                resultDiv.innerHTML += '<p style=\"color: red; font-weight: bold;\">⚠️ API trả về SUCCESS - Đây có thể là nguyên nhân!</p>';
            } else {
                resultDiv.innerHTML += '<p style=\"color: green;\">✅ API trả về FAILED - Đúng như mong đợi</p>';
            }
        })
        .catch(error => {
            resultDiv.innerHTML = '<p style=\"color: red;\">❌ Error: ' + error.message + '</p>';
        });
    }
    </script>";
} else {
    echo "Không có pending transaction để test.<br>";
}

// 5. Kiểm tra payment API
echo "<h3>5. Check Payment API Data:</h3>";
try {
    $payment_api_url = 'http://*************:3000/payments';
    $response = file_get_contents($payment_api_url);
    
    if ($response) {
        $payments = json_decode($response, true);
        
        if (is_array($payments) && count($payments) > 0) {
            echo "✅ API hoạt động, có " . count($payments) . " giao dịch<br>";
            
            // Hiển thị 3 giao dịch gần nhất
            echo "<strong>3 giao dịch gần nhất:</strong><br>";
            foreach (array_slice($payments, 0, 3) as $i => $payment) {
                $time_diff = time() - strtotime($payment['date']);
                echo ($i + 1) . ". Amount: {$payment['amount']}, Content: " . substr($payment['content'], 0, 50) . "..., Time: {$time_diff}s ago<br>";
            }
            
            // Kiểm tra có giao dịch nào khớp với pending không
            if (isset($_SESSION['pending_transaction'])) {
                $pending = $_SESSION['pending_transaction'];
                $found_match = false;
                
                foreach ($payments as $payment) {
                    if ($payment['amount'] == $pending['amount'] && 
                        strpos($payment['content'], $pending['transaction_code']) !== false) {
                        $found_match = true;
                        echo "<p style='color: red; font-weight: bold;'>⚠️ FOUND MATCHING PAYMENT: " . $payment['content'] . "</p>";
                        break;
                    }
                }
                
                if (!$found_match) {
                    echo "<p style='color: green;'>✅ Không tìm thấy giao dịch khớp - Đúng như mong đợi</p>";
                }
            }
        } else {
            echo "❌ API không trả về dữ liệu hợp lệ<br>";
        }
    } else {
        echo "❌ Không thể kết nối đến API<br>";
    }
} catch (Exception $e) {
    echo "❌ Lỗi API: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h3>🔍 Phân tích:</h3>";
echo "<ul>";
echo "<li>Nếu <strong>pending transaction hết hạn</strong> → Lỗi logic timeout</li>";
echo "<li>Nếu <strong>API trả về success</strong> → Lỗi logic kiểm tra giao dịch</li>";
echo "<li>Nếu <strong>có matching payment</strong> → Có giao dịch thật khớp</li>";
echo "<li>Nếu <strong>completed transaction gần đây</strong> → Giao dịch đã xử lý trước đó</li>";
echo "</ul>";

echo "<p><strong>Sau khi debug xong, xóa file này!</strong></p>";
?>

<style>
table {
    border-collapse: collapse;
    margin: 10px 0;
    font-size: 12px;
}

table th, table td {
    border: 1px solid #ddd;
    padding: 6px;
    text-align: left;
}

table th {
    background-color: #f2f2f2;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
    font-size: 12px;
}

button {
    background: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin: 10px 0;
}
</style>
