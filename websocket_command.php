<?php
/**
 * <PERSON>ript để gửi lệnh qua WebSocket Pterodactyl
 * Sử dụng Node.js hoặc Python script
 */

function sendPterodactylCommand($server_id, $api_key, $command) {
    $base_url = 'https://panel.gamehosting.vn';
    
    // Tạo script Node.js tạm thời
    $node_script = createNodeScript($server_id, $api_key, $command, $base_url);
    
    // Lưu script vào file tạm
    $temp_file = sys_get_temp_dir() . '/pterodactyl_command_' . uniqid() . '.js';
    file_put_contents($temp_file, $node_script);
    
    try {
        // Chạy Node.js script
        $output = shell_exec("node $temp_file 2>&1");
        
        // Xóa file tạm
        unlink($temp_file);
        
        // Parse output
        $result = json_decode($output, true);
        
        if ($result && isset($result['success'])) {
            return $result;
        } else {
            return [
                'success' => false,
                'error' => 'Failed to parse Node.js output: ' . $output
            ];
        }
        
    } catch (Exception $e) {
        // Xóa file tạm nếu có lỗi
        if (file_exists($temp_file)) {
            unlink($temp_file);
        }
        
        return [
            'success' => false,
            'error' => 'Exception: ' . $e->getMessage()
        ];
    }
}

function createNodeScript($server_id, $api_key, $command, $base_url) {
    return <<<NODEJS
const https = require('https');
const WebSocket = require('ws');

async function sendCommand() {
    try {
        // Bước 1: Lấy WebSocket token
        const websocketUrl = '$base_url/api/client/servers/$server_id/websocket';
        
        const options = {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Authorization': 'Bearer $api_key'
            }
        };
        
        const req = https.request(websocketUrl, options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    
                    if (res.statusCode !== 200) {
                        console.log(JSON.stringify({
                            success: false,
                            error: 'Failed to get WebSocket token: HTTP ' + res.statusCode
                        }));
                        return;
                    }
                    
                    if (!response.data || !response.data.socket) {
                        console.log(JSON.stringify({
                            success: false,
                            error: 'Invalid WebSocket response'
                        }));
                        return;
                    }
                    
                    // Bước 2: Kết nối WebSocket và gửi lệnh
                    const ws = new WebSocket(response.data.socket, {
                        headers: {
                            'Authorization': 'Bearer ' + response.data.token
                        }
                    });
                    
                    ws.on('open', () => {
                        // Gửi auth message
                        ws.send(JSON.stringify({
                            event: 'auth',
                            args: [response.data.token]
                        }));
                        
                        // Gửi lệnh sau 1 giây
                        setTimeout(() => {
                            ws.send(JSON.stringify({
                                event: 'send command',
                                args: ['$command']
                            }));
                            
                            // Đóng connection sau 2 giây
                            setTimeout(() => {
                                ws.close();
                                console.log(JSON.stringify({
                                    success: true,
                                    error: null
                                }));
                            }, 2000);
                        }, 1000);
                    });
                    
                    ws.on('error', (error) => {
                        console.log(JSON.stringify({
                            success: false,
                            error: 'WebSocket error: ' + error.message
                        }));
                    });
                    
                } catch (e) {
                    console.log(JSON.stringify({
                        success: false,
                        error: 'JSON parse error: ' + e.message
                    }));
                }
            });
        });
        
        req.on('error', (error) => {
            console.log(JSON.stringify({
                success: false,
                error: 'HTTPS request error: ' + error.message
            }));
        });
        
        req.end();
        
    } catch (error) {
        console.log(JSON.stringify({
            success: false,
            error: 'Script error: ' + error.message
        }));
    }
}

sendCommand();
NODEJS;
}

// Test function
if (isset($_GET['test'])) {
    $result = sendPterodactylCommand('8661ab4b', 'ptlc_DzHfXQUGVnsUhaA9cz6M27J47hRIEyAGo9t7YYMl0Gs', 'list');
    
    echo "<h2>Test WebSocket Command</h2>";
    echo "<pre>" . json_encode($result, JSON_PRETTY_PRINT) . "</pre>";
    
    if ($result['success']) {
        echo "<p>✅ Command sent successfully!</p>";
    } else {
        echo "<p>❌ Error: " . htmlspecialchars($result['error']) . "</p>";
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Command Test</title>
</head>
<body>
    <h2>WebSocket Command Sender</h2>
    <p>This script requires Node.js and 'ws' package to be installed.</p>
    
    <h3>Installation:</h3>
    <pre>
npm install ws
    </pre>
    
    <h3>Test:</h3>
    <a href="?test=1">Test WebSocket Command</a>
    
    <h3>Note:</h3>
    <p>This is a proof of concept. For production, consider:</p>
    <ul>
        <li>Using a dedicated Node.js service</li>
        <li>Using ReactPHP with Ratchet/Pawl</li>
        <li>Using Python with websockets library</li>
    </ul>
</body>
</html>
