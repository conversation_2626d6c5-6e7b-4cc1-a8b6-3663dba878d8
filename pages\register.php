<?php
// <PERSON>iến để lưu thông báo lỗi
$error_message = '';
$error_type = '';

// Process registration form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize($_POST['username']);
    $email = sanitize($_POST['email']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];

    // Validate input
    if (empty($username) || empty($email) || empty($password) || empty($confirm_password)) {
        $error_message = 'Vui lòng điền đầy đủ thông tin bắt buộc';
        $error_type = 'danger';
    } elseif ($password !== $confirm_password) {
        $error_message = 'Mật khẩu nhập lại không khớp';
        $error_type = 'danger';
    } elseif (strlen($password) < 8) {
        $error_message = 'Mật khẩu phải có ít nhất 8 ký tự';
        $error_type = 'danger';
    } else {
        // <PERSON><PERSON><PERSON> tra username hoặc email đã tồn tại chưa
        $stmt = $conn->prepare("SELECT * FROM nlogin WHERE unique_id = :username OR last_name = :username OR email = :email");
        $stmt->bindParam(':username', $username);
        $stmt->bindParam(':email', $email);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($user['unique_id'] === $username || $user['last_name'] === $username) {
                $error_message = 'Tên tài khoản đã tồn tại';
            } else {
                $error_message = 'Email đã được sử dụng';
            }
            $error_type = 'danger';
        } else {
            // Additional data với last_name thay vì full_name
            $additional_data = [
                'last_name' => $username
            ];

            // Attempt registration
            $result = $nlogin->register($username, $email, $password, $additional_data);

            if (isset($result['error'])) {
                // Hiển thị thông báo lỗi từ nlogin
                $error_message = $result['error'];
                $error_type = 'danger';
            } else {
                // Successful registration
                // Create email verification token and send OTP
                $user_id = $result['ai'];
                $username = $result['last_name'] ?? $result['unique_id'];

                // Store registration info in session for verification page
                $_SESSION['register_email'] = $email;
                $_SESSION['register_user_id'] = $user_id;

                // Create verification token and send OTP
                $verificationResult = createEmailVerificationToken($user_id, $email, $username, 'register');

                if ($verificationResult['status']) {
                    // Add email_verified column to nlogin table if it doesn't exist
                    // This can be done in the background to speed up the response
                    register_shutdown_function(function() use ($conn) {
                        try {
                            $conn->query("ALTER TABLE nlogin ADD COLUMN IF NOT EXISTS email_verified TINYINT(1) DEFAULT 0");
                        } catch(PDOException $e) {
                            // Bỏ qua lỗi nếu không thể thêm cột
                        }
                    });

                    // Redirect to verification page immediately
                    $_SESSION['message'] = 'Đăng ký thành công! Vui lòng xác minh email của bạn.';
                    $_SESSION['message_type'] = 'success';

                    // For development/testing, show OTP in session
                    if (defined('SHOW_OTP') && SHOW_OTP === true) {
                        $_SESSION['debug_otp'] = $verificationResult['data']['otp'];
                    }

                    header('Location: /verify-email');
                    exit;
                } else {
                    // Failed to create verification token
                    $_SESSION['message'] = 'Đăng ký thành công nhưng không thể gửi email xác minh. Vui lòng liên hệ quản trị viên.';
                    $_SESSION['message_type'] = 'warning';
                    header('Location: /login');
                    exit;
                }
            }
        }
    }

    // Nếu có lỗi, lưu vào session để hiển thị
    if (!empty($error_message)) {
        $_SESSION['message'] = $error_message;
        $_SESSION['message_type'] = $error_type;
    }
}
?>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow-sm border-0 mb-5">
            <div class="card-header bg-white">
                <h4 class="text-center mb-0"><i class="fas fa-user-plus text-primary"></i> Đăng Ký Tài Khoản</h4>
            </div>
            <div class="card-body p-4">
                <div class="text-center mb-4">
                    <i class="fas fa-user-plus text-primary" style="font-size: 3rem;"></i>
                </div>

                <!-- Hiển thị thông báo lỗi tại đây -->
                <?php if (!empty($error_message)): ?>
                <div class="alert alert-<?php echo $error_type; ?>">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                </div>
                <?php endif; ?>

                <div id="form-error-message" class="alert alert-danger" style="display: none;">
                    <i class="fas fa-exclamation-circle"></i> <span id="error-text"></span>
                </div>

                <form method="POST" action="/register" class="needs-validation" id="register-form" novalidate>
                    <div class="form-group">
                        <label class="form-label" for="email">
                            <i class="fas fa-envelope text-primary"></i> Địa chỉ email*
                        </label>
                        <input type="email" class="form-control" id="email" name="email" placeholder="Nhập địa chỉ email" required>
                        <div class="invalid-feedback" id="email-feedback">
                            Vui lòng nhập địa chỉ email hợp lệ.
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="username">
                            <i class="fas fa-user text-primary"></i> Tên tài khoản*
                        </label>
                        <input type="text" class="form-control" id="username" name="username" placeholder="Chọn tên tài khoản" required>
                        <div class="invalid-feedback" id="username-feedback">
                            Vui lòng nhập tên tài khoản.
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="password">
                            <i class="fas fa-lock text-primary"></i> Mật khẩu*
                        </label>
                        <input type="password" class="form-control" id="password" name="password" placeholder="Chọn mật khẩu" required>
                        <div class="invalid-feedback" id="password-feedback">
                            Mật khẩu phải có ít nhất 8 ký tự.
                        </div>
                        <div class="progress mt-2" style="height: 5px;">
                            <div id="password-strength" class="progress-bar bg-success" role="progressbar" style="width: 0%;"></div>
                        </div>
                        <small class="form-text text-muted">Mật khẩu phải có ít nhất 8 ký tự.</small>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="confirm_password">
                            <i class="fas fa-lock text-primary"></i> Nhập lại mật khẩu*
                        </label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="Nhập lại mật khẩu" required>
                        <div class="invalid-feedback" id="confirm-password-feedback">
                            Mật khẩu không khớp.
                        </div>
                    </div>



                    <div class="form-group">
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-user-plus"></i> Đăng Ký
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <p>Đã có tài khoản? <a href="/login" class="text-primary">Đăng nhập</a></p>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const emailInput = document.getElementById('email');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirm_password');
    const registerForm = document.getElementById('register-form');
    const errorMessageDiv = document.getElementById('form-error-message');
    const errorText = document.getElementById('error-text');

    // Kiểm tra email đã tồn tại chưa
    emailInput.addEventListener('blur', function() {
        if (emailInput.value && emailInput.checkValidity()) {
            checkFieldExists('email', emailInput.value);
        }
    });

    // Kiểm tra username đã tồn tại chưa
    usernameInput.addEventListener('blur', function() {
        if (usernameInput.value && usernameInput.checkValidity()) {
            checkFieldExists('username', usernameInput.value);
        }
    });

    // Kiểm tra mật khẩu nhập lại có khớp không
    confirmPasswordInput.addEventListener('input', function() {
        if (passwordInput.value !== confirmPasswordInput.value) {
            confirmPasswordInput.setCustomValidity('Mật khẩu không khớp');
            document.getElementById('confirm-password-feedback').textContent = 'Mật khẩu không khớp';
        } else {
            confirmPasswordInput.setCustomValidity('');
        }
    });

    // Kiểm tra độ dài mật khẩu
    passwordInput.addEventListener('input', function() {
        if (passwordInput.value.length < 8) {
            passwordInput.setCustomValidity('Mật khẩu phải có ít nhất 8 ký tự');
            document.getElementById('password-feedback').textContent = 'Mật khẩu phải có ít nhất 8 ký tự';
        } else {
            passwordInput.setCustomValidity('');
        }

        // Cập nhật confirm password validation
        if (confirmPasswordInput.value) {
            if (passwordInput.value !== confirmPasswordInput.value) {
                confirmPasswordInput.setCustomValidity('Mật khẩu không khớp');
            } else {
                confirmPasswordInput.setCustomValidity('');
            }
        }
    });

    // Xử lý form submit
    registerForm.addEventListener('submit', function(event) {
        // Ẩn thông báo lỗi cũ
        errorMessageDiv.style.display = 'none';

        // Kiểm tra form hợp lệ
        if (!registerForm.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();

            // Hiển thị thông báo lỗi
            errorText.textContent = 'Vui lòng điền đầy đủ thông tin bắt buộc';
            errorMessageDiv.style.display = 'block';
        }

        registerForm.classList.add('was-validated');
    });

    // Hàm kiểm tra field đã tồn tại chưa
    function checkFieldExists(field, value) {
        const formData = new FormData();
        formData.append('field', field);
        formData.append('value', value);

        fetch('/api/check_user.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (!data.status) {
                // Field đã tồn tại
                if (field === 'email') {
                    emailInput.setCustomValidity(data.message);
                    document.getElementById('email-feedback').textContent = data.message;
                } else {
                    usernameInput.setCustomValidity(data.message);
                    document.getElementById('username-feedback').textContent = data.message;
                }

                // Hiển thị thông báo lỗi
                errorText.textContent = data.message;
                errorMessageDiv.style.display = 'block';
            } else {
                // Field hợp lệ
                if (field === 'email') {
                    emailInput.setCustomValidity('');
                } else {
                    usernameInput.setCustomValidity('');
                }

                // Ẩn thông báo lỗi nếu không còn lỗi nào
                if (emailInput.checkValidity() && usernameInput.checkValidity() &&
                    passwordInput.checkValidity() && confirmPasswordInput.checkValidity()) {
                    errorMessageDiv.style.display = 'none';
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
    }
});
</script>
