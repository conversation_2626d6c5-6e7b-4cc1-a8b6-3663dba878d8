<?php
// Debug script để kiểm tra trạng thái hiện tại
require_once 'config/config.php';

echo "<h2>Debug Current State</h2>";

session_start();

// 1. Ki<PERSON>m tra session hiện tại
echo "<h3>1. Current Session:</h3>";
if (isset($_SESSION['user_id'])) {
    echo "✅ User ID: " . $_SESSION['user_id'] . "<br>";
    
    if (isset($_SESSION['pending_transaction'])) {
        $pending = $_SESSION['pending_transaction'];
        $time_since_created = time() - $pending['created_at'];
        
        echo "✅ Pending Transaction:<br>";
        echo "- Transaction Code: <code>{$pending['transaction_code']}</code><br>";
        echo "- Amount: " . number_format($pending['amount']) . " VNĐ<br>";
        echo "- Server: {$pending['server']}<br>";
        echo "- Created: " . date('Y-m-d H:i:s', $pending['created_at']) . "<br>";
        echo "- Time since created: <strong>{$time_since_created} seconds</strong><br>";
        
        if ($time_since_created > 60) {
            echo "⚠️ <span style='color: red;'>Transaction is over 60 seconds old!</span><br>";
        } else {
            echo "✅ <span style='color: green;'>Transaction is still valid</span><br>";
        }
    } else {
        echo "❌ No pending transaction in session<br>";
    }
} else {
    echo "❌ User not logged in<br>";
}

// 2. Kiểm tra database pending
echo "<h3>2. Database Pending Transactions (Last 5):</h3>";
try {
    if (isset($_SESSION['user_id'])) {
        $stmt = $conn->prepare("
            SELECT *, 
                   TIMESTAMPDIFF(SECOND, created_at, NOW()) as age_seconds
            FROM nlogin_qr_pending 
            WHERE user_id = :user_id 
            ORDER BY created_at DESC 
            LIMIT 5
        ");
        $stmt->bindParam(':user_id', $_SESSION['user_id']);
        $stmt->execute();
        $pending_txs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($pending_txs) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th>ID</th><th>Transaction Code</th><th>Amount</th><th>Created</th><th>Age (s)</th><th>Processed</th><th>Status</th>";
            echo "</tr>";
            
            foreach ($pending_txs as $tx) {
                $status_color = $tx['processed'] ? '#dc3545' : '#28a745';
                $status_text = $tx['processed'] ? 'Processed/Cancelled' : 'Active';
                
                if (!$tx['processed'] && $tx['age_seconds'] > 60) {
                    $status_color = '#ffc107';
                    $status_text = 'Should be expired';
                }
                
                echo "<tr>";
                echo "<td>{$tx['id']}</td>";
                echo "<td><code>{$tx['transaction_code']}</code></td>";
                echo "<td>" . number_format($tx['amount']) . "</td>";
                echo "<td>{$tx['created_at']}</td>";
                echo "<td><strong>{$tx['age_seconds']}</strong></td>";
                echo "<td>" . ($tx['processed'] ? 'Yes' : 'No') . "</td>";
                echo "<td style='color: $status_color; font-weight: bold;'>$status_text</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "No pending transactions found.<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// 3. Kiểm tra completed transactions
echo "<h3>3. Recent Completed Transactions:</h3>";
try {
    if (isset($_SESSION['user_id'])) {
        $stmt = $conn->prepare("
            SELECT * FROM nlogin_qr_transactions 
            WHERE user_id = :user_id 
            ORDER BY created_at DESC 
            LIMIT 3
        ");
        $stmt->bindParam(':user_id', $_SESSION['user_id']);
        $stmt->execute();
        $completed_txs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($completed_txs) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th>ID</th><th>Transaction Code</th><th>Amount</th><th>Server</th><th>Created</th>";
            echo "</tr>";
            
            foreach ($completed_txs as $tx) {
                echo "<tr>";
                echo "<td>{$tx['id']}</td>";
                echo "<td><code>{$tx['transaction_code']}</code></td>";
                echo "<td>" . number_format($tx['amount']) . "</td>";
                echo "<td>{$tx['server']}</td>";
                echo "<td>{$tx['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "No completed transactions found.<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// 4. Test API call nếu có pending transaction
echo "<h3>4. Test API Call:</h3>";
if (isset($_SESSION['pending_transaction'])) {
    $transaction_code = $_SESSION['pending_transaction']['transaction_code'];
    
    echo "<strong>Testing transaction:</strong> <code>$transaction_code</code><br>";
    echo "<button onclick='testAPI()' style='margin: 10px 0; padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;'>Test API Call</button><br>";
    echo "<div id='api-result' style='margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px;'></div>";
    
    echo "<script>
    function testAPI() {
        const resultDiv = document.getElementById('api-result');
        resultDiv.innerHTML = '⏳ Testing API call...';
        
        fetch('/api/check-payment.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                transaction_code: '$transaction_code'
            })
        })
        .then(response => response.json())
        .then(data => {
            let resultHTML = '<h4>API Response:</h4>';
            resultHTML += '<pre style=\"background: #fff; padding: 10px; border: 1px solid #ddd; border-radius: 4px; overflow-x: auto;\">' + JSON.stringify(data, null, 2) + '</pre>';
            
            if (data.success) {
                resultHTML += '<p style=\"color: green; font-weight: bold;\">✅ API returned SUCCESS</p>';
            } else if (data.already_processed) {
                resultHTML += '<p style=\"color: orange; font-weight: bold;\">⚠️ API returned ALREADY_PROCESSED</p>';
            } else if (data.expired) {
                resultHTML += '<p style=\"color: red; font-weight: bold;\">❌ API returned EXPIRED</p>';
            } else {
                resultHTML += '<p style=\"color: blue; font-weight: bold;\">ℹ️ API returned NO PAYMENT YET</p>';
            }
            
            resultDiv.innerHTML = resultHTML;
        })
        .catch(error => {
            resultDiv.innerHTML = '<p style=\"color: red;\">❌ Error: ' + error.message + '</p>';
        });
    }
    </script>";
} else {
    echo "No pending transaction to test.<br>";
}

// 5. Cleanup actions
echo "<h3>5. Cleanup Actions:</h3>";
echo "<button onclick='cleanupOld()' style='margin: 5px; padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;'>Cleanup Old Transactions</button>";
echo "<button onclick='clearSession()' style='margin: 5px; padding: 8px 16px; background: #ffc107; color: black; border: none; border-radius: 4px; cursor: pointer;'>Clear Session</button>";

echo "<script>
function cleanupOld() {
    if (confirm('Cleanup old transactions (older than 2 minutes)?')) {
        fetch('/cleanup_old_transactions.php')
        .then(response => response.text())
        .then(data => {
            alert('Cleanup completed: ' + data);
            location.reload();
        });
    }
}

function clearSession() {
    if (confirm('Clear current session pending transaction?')) {
        fetch('/clear_session.php')
        .then(response => response.text())
        .then(data => {
            alert('Session cleared');
            location.reload();
        });
    }
}
</script>";

echo "<hr>";
echo "<p><strong>Sau khi debug xong, xóa file này!</strong></p>";
?>

<style>
table {
    border-collapse: collapse;
    margin: 10px 0;
    font-size: 12px;
}

table th, table td {
    border: 1px solid #ddd;
    padding: 6px;
    text-align: left;
}

table th {
    background-color: #f2f2f2;
}

code {
    background: #e9ecef;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

h3 {
    color: #333;
    margin-top: 20px;
}
</style>
