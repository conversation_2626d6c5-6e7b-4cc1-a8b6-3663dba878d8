/**
 * Minecraft Theme CSS
 * A complete theme for DPTMC.COM based on Minecraft aesthetics
 */

/* Import Minecraft Font */
@import url('https://fonts.googleapis.com/css2?family=VT323&display=swap');
@font-face {
    font-family: 'Minecraft';
    src: url('../fonts/Minecraft.woff2') format('woff2'),
         url('../fonts/Minecraft.woff') format('woff');
    font-weight: normal;
    font-style: normal;
}

/* Root Variables */
:root {
    /* Main Colors */
    --mc-dirt: #8B5A2B;
    --mc-dirt-dark: #694229;
    --mc-grass: #5D9C3A;
    --mc-grass-dark: #3B6424;
    --mc-stone: #828282;
    --mc-stone-dark: #5A5A5A;
    --mc-wood: #A0722A;
    --mc-wood-dark: #7D5821;
    --mc-sand: #E4D6A7;
    --mc-sand-dark: #C4B489;
    --mc-water: #3D99F5;
    --mc-water-dark: #2A6DB0;
    --mc-lava: #E25822;
    --mc-lava-dark: #B33E11;
    --mc-bedrock: #333333;
    --mc-bedrock-dark: #1A1A1A;
    
    /* UI Colors */
    --mc-ui-bg: #C6C6C6;
    --mc-ui-border: #555555;
    --mc-ui-highlight: #FFFFFF;
    --mc-ui-shadow: #555555;
    --mc-ui-text: #333333;
    --mc-ui-text-light: #FFFFFF;
    
    /* Status Colors */
    --mc-success: #5D9C3A;
    --mc-warning: #F9A825;
    --mc-danger: #C83E30;
    --mc-info: #3D99F5;
}

/* Base Styles */
body {
    font-family: 'Minecraft', 'VT323', monospace;
    background-color: var(--mc-bedrock);
    background-image: url('../img/minecraft/dirt_bg.png');
    color: var(--mc-ui-text);
    line-height: 1.6;
    padding-top: 70px;
}

/* Container with Minecraft style */
.mc-container {
    background-color: rgba(0, 0, 0, 0.7);
    border: 4px solid var(--mc-ui-border);
    box-shadow: inset 0 0 0 2px var(--mc-ui-highlight);
    padding: 20px;
    margin-bottom: 30px;
    position: relative;
}

.mc-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('../img/minecraft/stone_bg.png');
    opacity: 0.1;
    z-index: -1;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: 'Minecraft', 'VT323', monospace;
    color: var(--mc-ui-text-light);
    text-shadow: 2px 2px 0 var(--mc-bedrock-dark);
    margin-bottom: 20px;
    letter-spacing: 1px;
}

/* Minecraft Button */
.mc-btn {
    display: inline-block;
    background-color: var(--mc-stone);
    border: 2px solid var(--mc-ui-border);
    box-shadow: inset 0 0 0 2px var(--mc-ui-highlight), 3px 3px 0 0 var(--mc-bedrock-dark);
    color: var(--mc-ui-text-light);
    font-family: 'Minecraft', 'VT323', monospace;
    font-size: 16px;
    padding: 8px 16px;
    text-align: center;
    text-decoration: none;
    text-transform: uppercase;
    transition: all 0.1s ease;
    cursor: pointer;
    margin: 5px;
}

.mc-btn:hover {
    background-color: var(--mc-stone-dark);
    transform: translate(1px, 1px);
    box-shadow: inset 0 0 0 2px var(--mc-ui-highlight), 2px 2px 0 0 var(--mc-bedrock-dark);
    color: var(--mc-ui-text-light);
    text-decoration: none;
}

.mc-btn:active {
    transform: translate(3px, 3px);
    box-shadow: inset 0 0 0 2px var(--mc-ui-highlight), 0 0 0 0 var(--mc-bedrock-dark);
}

/* Button Variants */
.mc-btn-primary {
    background-color: var(--mc-grass);
}
.mc-btn-primary:hover {
    background-color: var(--mc-grass-dark);
}

.mc-btn-secondary {
    background-color: var(--mc-stone);
}
.mc-btn-secondary:hover {
    background-color: var(--mc-stone-dark);
}

.mc-btn-success {
    background-color: var(--mc-success);
}
.mc-btn-success:hover {
    background-color: var(--mc-grass-dark);
}

.mc-btn-danger {
    background-color: var(--mc-danger);
}
.mc-btn-danger:hover {
    background-color: #A32B1F;
}

.mc-btn-warning {
    background-color: var(--mc-warning);
}
.mc-btn-warning:hover {
    background-color: #D18A1A;
}

.mc-btn-info {
    background-color: var(--mc-info);
}
.mc-btn-info:hover {
    background-color: var(--mc-water-dark);
}

.mc-btn-block {
    display: block;
    width: 100%;
}

/* Form Elements */
.mc-form-group {
    margin-bottom: 20px;
}

.mc-form-control {
    display: block;
    width: 100%;
    padding: 10px;
    font-family: 'Minecraft', 'VT323', monospace;
    font-size: 16px;
    background-color: var(--mc-ui-bg);
    border: 2px solid var(--mc-ui-border);
    box-shadow: inset 3px 3px 0 0 var(--mc-ui-shadow);
    color: var(--mc-ui-text);
}

.mc-form-control:focus {
    outline: none;
    border-color: var(--mc-water);
    box-shadow: inset 3px 3px 0 0 var(--mc-water-dark);
}

.mc-form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: var(--mc-ui-text-light);
    text-shadow: 1px 1px 0 var(--mc-bedrock-dark);
}

/* Alerts */
.mc-alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 2px solid var(--mc-ui-border);
    box-shadow: inset 0 0 0 2px var(--mc-ui-highlight), 3px 3px 0 0 var(--mc-bedrock-dark);
    font-family: 'Minecraft', 'VT323', monospace;
}

.mc-alert-success {
    background-color: var(--mc-success);
    color: white;
}

.mc-alert-warning {
    background-color: var(--mc-warning);
    color: var(--mc-ui-text);
}

.mc-alert-danger {
    background-color: var(--mc-danger);
    color: white;
}

.mc-alert-info {
    background-color: var(--mc-info);
    color: white;
}

/* Cards */
.mc-card {
    background-color: var(--mc-ui-bg);
    border: 2px solid var(--mc-ui-border);
    box-shadow: inset 0 0 0 2px var(--mc-ui-highlight), 5px 5px 0 0 var(--mc-bedrock-dark);
    margin-bottom: 20px;
}

.mc-card-header {
    background-color: var(--mc-stone);
    color: var(--mc-ui-text-light);
    padding: 10px 15px;
    border-bottom: 2px solid var(--mc-ui-border);
    font-weight: bold;
}

.mc-card-body {
    padding: 15px;
}

.mc-card-footer {
    background-color: var(--mc-stone);
    padding: 10px 15px;
    border-top: 2px solid var(--mc-ui-border);
}

/* Navbar */
.mc-navbar {
    background-color: var(--mc-bedrock);
    border-bottom: 4px solid var(--mc-ui-border);
    box-shadow: 0 2px 0 0 var(--mc-ui-highlight);
    padding: 10px 0;
}

.mc-navbar-brand {
    font-size: 24px;
    color: var(--mc-ui-text-light);
    text-shadow: 2px 2px 0 var(--mc-bedrock-dark);
    text-decoration: none;
}

.mc-navbar-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.mc-nav-item {
    margin-right: 15px;
}

.mc-nav-link {
    color: var(--mc-ui-text-light);
    text-decoration: none;
    padding: 5px 10px;
    transition: all 0.2s ease;
}

.mc-nav-link:hover {
    background-color: var(--mc-stone);
    color: var(--mc-ui-text-light);
    text-decoration: none;
}

/* Utilities */
.mc-text-center {
    text-align: center;
}

.mc-text-right {
    text-align: right;
}

.mc-text-left {
    text-align: left;
}

.mc-mt-3 {
    margin-top: 15px;
}

.mc-mb-3 {
    margin-bottom: 15px;
}

.mc-p-3 {
    padding: 15px;
}
