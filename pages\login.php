<?php
// Biến để lưu thông báo lỗi
$error_message = '';
$error_type = '';

// Process login form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitize($_POST['username']);
    $password = $_POST['password'];
    $remember = isset($_POST['remember']) ? true : false;

    // Validate input
    if (empty($username) || empty($password)) {
        $error_message = 'Vui lòng điền đầy đủ thông tin';
        $error_type = 'danger';
    } else {
        // Attempt login
        $result = $nlogin->login($username, $password, $remember);

        if (isset($result['error'])) {
            if ($result['error'] == 'Invalid username or password') {
                $error_message = 'Tài khoản game hoặc mật khẩu không chính xác';

                // Debug information - remove in production
                global $conn;

                // Thử tìm kiếm người dùng theo last_name thay vì unique_id
                $stmt = $conn->prepare("SELECT * FROM nlogin WHERE last_name = :username OR email = :email");
                $stmt->bindParam(':username', $username);
                $stmt->bindParam(':email', $username);
                $stmt->execute();

                if ($stmt->rowCount() > 0) {
                    // Người dùng tồn tại nhưng mật khẩu không đúng
                    $error_message = 'Mật khẩu không chính xác';
                } else {
                    // Người dùng không tồn tại
                    $error_message = 'Tài khoản không tồn tại';
                }
            } else {
                $error_message = $result['error'];
            }
            $error_type = 'danger';
        } else {
            // Successful login
            $_SESSION['message'] = 'Đăng nhập thành công! Chào mừng trở lại, ' . $result['last_name'];
            $_SESSION['message_type'] = 'success';

            // Kiểm tra xem tài khoản đã có email chưa
            if (empty($result['email'])) {
                // Chuyển hướng đến trang bổ sung email
                header('Location: /add-email');
                exit;
            }
            header('Location: /account');
            exit;
        }
    }
}
?>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-5">
        <div class="card shadow-sm border-0 mb-5">
            <div class="card-header bg-white">
                <h4 class="text-center mb-0"><i class="fas fa-sign-in-alt text-primary"></i> Đăng Nhập</h4>
            </div>
            <div class="card-body p-4">
                <div class="text-center mb-4">
                    <i class="fas fa-user-circle text-primary" style="font-size: 4rem;"></i>
                </div>

                <!-- Hiển thị thông báo lỗi tại đây -->
                <?php if (!empty($error_message)): ?>
                <div class="alert alert-<?php echo $error_type; ?>">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                </div>
                <?php endif; ?>

                <div id="form-error-message" class="alert alert-danger" style="display: none;">
                    <i class="fas fa-exclamation-circle"></i> <span id="error-text"></span>
                </div>

                <form method="POST" action="/login" class="needs-validation" id="login-form" novalidate>
                    <div class="form-group">
                        <label class="form-label" for="username">
                            <i class="fas fa-user text-primary"></i> Tài khoản game hoặc Email
                        </label>
                        <input type="text" class="form-control" id="username" name="username" placeholder="Nhập tài khoản hoặc email" required value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
                        <div class="invalid-feedback">
                            Vui lòng nhập tài khoản game hoặc email.
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="password">
                            <i class="fas fa-lock text-primary"></i> Mật khẩu tài khoản game
                        </label>
                        <input type="password" class="form-control" id="password" name="password" placeholder="Nhập mật khẩu" required>
                        <div class="invalid-feedback">
                            Vui lòng nhập mật khẩu tài khoản game.
                        </div>
                    </div>



                    <div class="form-group">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="remember" name="remember">
                            <label class="custom-control-label" for="remember">Ghi nhớ đăng nhập</label>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-sign-in-alt"></i> Đăng Nhập
                        </button>
                    </div>

                    <div class="text-center mt-4">
                        <p>Chưa có tài khoản? <a href="/register" class="text-primary">Đăng ký ngay</a></p>
                        <p><a href="/forgot-password" class="text-muted"><small>Quên mật khẩu?</small></a></p>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const loginForm = document.getElementById('login-form');
    const errorMessageDiv = document.getElementById('form-error-message');
    const errorText = document.getElementById('error-text');

    // Xử lý form submit
    loginForm.addEventListener('submit', function(event) {
        // Ẩn thông báo lỗi cũ
        errorMessageDiv.style.display = 'none';

        // Kiểm tra form hợp lệ
        if (!loginForm.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();

            // Hiển thị thông báo lỗi
            if (!usernameInput.value && !passwordInput.value) {
                errorText.textContent = 'Vui lòng nhập tài khoản và mật khẩu';
            } else if (!usernameInput.value) {
                errorText.textContent = 'Vui lòng nhập tài khoản';
            } else if (!passwordInput.value) {
                errorText.textContent = 'Vui lòng nhập mật khẩu';
            }

            errorMessageDiv.style.display = 'block';
        }

        loginForm.classList.add('was-validated');
    });
});
</script>
