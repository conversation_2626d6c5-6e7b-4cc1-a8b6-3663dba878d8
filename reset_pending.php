<?php
// <PERSON><PERSON>t để reset pending transactions
require_once 'config/config.php';

header('Content-Type: text/plain');

try {
    // Reset pending transactions về unprocessed nếu chưa quá 2 phút
    $stmt = $conn->prepare("
        UPDATE nlogin_qr_pending 
        SET processed = 0, processed_at = NULL 
        WHERE processed = 1 
        AND created_at > DATE_SUB(NOW(), INTERVAL 2 MINUTE)
    ");
    $stmt->execute();
    $reset_count = $stmt->rowCount();
    
    echo "Reset $reset_count pending transactions to unprocessed";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
