<?php
// Check if registration email is set in session
if (!isset($_SESSION['register_email']) || !isset($_SESSION['register_user_id'])) {
    $_SESSION['message'] = 'Phiên làm việc đã hết hạn. Vui lòng đăng ký lại.';
    $_SESSION['message_type'] = 'danger';
    header('Location: /register');
    exit;
}

$email = $_SESSION['register_email'];
$user_id = $_SESSION['register_user_id'];

// Process OTP verification form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get OTP from form
    $otp = '';
    for ($i = 1; $i <= 6; $i++) {
        $otp .= isset($_POST['otp' . $i]) ? $_POST['otp' . $i] : '';
    }

    // Xác minh OTP

    // Validate OTP
    if (strlen($otp) !== 6 || !ctype_digit($otp)) {
        $_SESSION['message'] = 'Vui lòng nhập đầy đủ mã xác minh 6 số';
        $_SESSION['message_type'] = 'danger';
    } else {
        // Verify OTP
        try {
            global $conn;

            // Find token with matching OTP
            $stmt = $conn->prepare("SELECT `key`, value FROM nlogin_data WHERE `key` LIKE 'email_verification_token_%'");
            $stmt->execute();

            // Tìm token xác minh

            $found = false;
            $tokenKey = '';

            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $tokenData = json_decode($row['value'], true);

                if (isset($tokenData['otp']) && $tokenData['otp'] === $otp && isset($tokenData['user_id']) && $tokenData['user_id'] == $user_id) {
                    // Tìm thấy OTP khớp

                    // Check if token is expired
                    if (strtotime($tokenData['expires']) < time()) {
                        $_SESSION['message'] = 'Mã xác minh đã hết hạn. Vui lòng yêu cầu mã mới.';
                        $_SESSION['message_type'] = 'danger';
                        break;
                    }

                    // Check if token has been used
                    if (isset($tokenData['used']) && $tokenData['used']) {
                        $_SESSION['message'] = 'Mã xác minh đã được sử dụng. Vui lòng yêu cầu mã mới.';
                        $_SESSION['message_type'] = 'danger';
                        break;
                    }

                    // Mark token as used and verified
                    $tokenData['used'] = true;
                    $tokenData['verified'] = true;
                    $tokenData['verified_at'] = date('Y-m-d H:i:s');

                    $updateStmt = $conn->prepare("UPDATE nlogin_data SET value = :value WHERE `key` = :key");
                    $updatedValue = json_encode($tokenData);
                    $updateStmt->bindParam(':value', $updatedValue);
                    $updateStmt->bindParam(':key', $row['key']);
                    $updateStmt->execute();

                    // Mark user as verified in nlogin table
                    $userStmt = $conn->prepare("UPDATE nlogin SET email_verified = 1 WHERE ai = :user_id");
                    $userStmt->bindParam(':user_id', $user_id);
                    $userStmt->execute();

                    $found = true;
                    $tokenKey = $row['key'];
                    break;
                }
            }

            if ($found) {
                // Delete the OTP record after successful verification
                $deleteStmt = $conn->prepare("DELETE FROM nlogin_data WHERE `key` = :key");
                $deleteStmt->bindParam(':key', $tokenKey);
                $deleteResult = $deleteStmt->execute();

                // Xóa tất cả các token hết hạn hoặc đã sử dụng
                $cleanupStmt = $conn->prepare("SELECT `key`, value FROM nlogin_data WHERE `key` LIKE 'email_verification_token_%'");
                $cleanupStmt->execute();

                while ($row = $cleanupStmt->fetch(PDO::FETCH_ASSOC)) {
                    $data = json_decode($row['value'], true);

                    // Xóa token nếu đã hết hạn hoặc đã sử dụng
                    if ((isset($data['expires']) && strtotime($data['expires']) < time()) ||
                        (isset($data['used']) && $data['used'] === true)) {
                        $cleanupDeleteStmt = $conn->prepare("DELETE FROM nlogin_data WHERE `key` = :key");
                        $cleanupDeleteStmt->bindParam(':key', $row['key']);
                        $cleanupDeleteStmt->execute();
                    }
                }

                // Clear session variables
                unset($_SESSION['register_email']);
                unset($_SESSION['register_user_id']);

                // Redirect to login page
                $_SESSION['message'] = 'Xác minh email thành công! Bạn có thể đăng nhập ngay bây giờ.';
                $_SESSION['message_type'] = 'success';
                header('Location: /login');
                exit;
            } else {
                $_SESSION['message'] = 'Mã xác minh không chính xác. Vui lòng thử lại.';
                $_SESSION['message_type'] = 'danger';
            }
        } catch(PDOException $e) {
            $_SESSION['message'] = 'Đã xảy ra lỗi. Vui lòng thử lại sau.';
            $_SESSION['message_type'] = 'danger';
        }
    }
}

// Get user data
$user = getUserData($user_id);
$username = isset($user['last_name']) ? $user['last_name'] : $user['unique_id'];
?>

<div class="row justify-content-center">
    <div class="col-md-8 col-lg-5">
        <div class="card shadow-sm border-0 mb-5">
            <div class="card-header bg-white">
                <h4 class="text-center mb-0"><i class="fas fa-envelope-open-text text-primary"></i> Xác minh Email</h4>
            </div>
            <div class="card-body p-4">
                <div class="alert alert-info shadow-sm">
                    <i class="fas fa-info-circle mr-2"></i>
                    Chúng tôi đã gửi mã xác minh 6 số đến email <strong><?php echo $email; ?></strong>. Vui lòng kiểm tra hộp thư đến và nhập mã xác minh để hoàn tất đăng ký.
                </div>

                <div class="text-center mb-4">
                    <i class="fas fa-envelope-open text-primary" style="font-size: 3rem;"></i>
                </div>

                <?php if (isset($_SESSION['message']) && $_SESSION['message_type'] == 'danger'): ?>
                <div class="alert alert-danger mb-4">
                    <i class="fas fa-exclamation-circle mr-2"></i> <?php echo $_SESSION['message']; ?>
                </div>
                <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
                <?php endif; ?>

                <form method="POST" action="/verify-email" class="needs-validation" novalidate>
                    <div class="form-group">
                        <label class="form-label">Mã xác minh</label>
                        <div class="d-flex justify-content-between mb-3">
                            <?php for ($i = 1; $i <= 6; $i++): ?>
                            <input type="text" class="form-control text-center otp-input" id="otp<?php echo $i; ?>" name="otp<?php echo $i; ?>" maxlength="1" pattern="[0-9]" required style="width: 45px; height: 45px; font-size: 1.2rem;">
                            <?php endfor; ?>
                        </div>
                        <div class="invalid-feedback">
                            Vui lòng nhập đầy đủ mã xác minh 6 số.
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-check-circle"></i> Xác minh
                        </button>
                    </div>
                </form>

                <!-- Removed resend OTP link -->
            </div>
        </div>
    </div>
</div>

<script>
// Auto-focus next input when typing OTP
document.addEventListener('DOMContentLoaded', function() {
    const otpInputs = document.querySelectorAll('.otp-input');

    otpInputs.forEach((input, index) => {
        input.addEventListener('keyup', function(e) {
            // If a digit was entered, focus the next input
            if (this.value.length === 1 && /^[0-9]$/.test(this.value)) {
                if (index < otpInputs.length - 1) {
                    otpInputs[index + 1].focus();
                }
            }

            // If backspace was pressed, focus the previous input
            if (e.key === 'Backspace' && index > 0 && this.value.length === 0) {
                otpInputs[index - 1].focus();
            }
        });

        // Prevent non-numeric input
        input.addEventListener('keypress', function(e) {
            if (!/^[0-9]$/.test(e.key)) {
                e.preventDefault();
            }
        });

        // Handle paste event
        input.addEventListener('paste', function(e) {
            e.preventDefault();
            const pasteData = e.clipboardData.getData('text');
            const digits = pasteData.match(/\d/g);

            if (digits && digits.length > 0) {
                for (let i = 0; i < otpInputs.length && i < digits.length; i++) {
                    otpInputs[i].value = digits[i];
                    if (i < otpInputs.length - 1 && i < digits.length - 1) {
                        otpInputs[i + 1].focus();
                    }
                }
            }
        });
    });

    // Focus first input on page load
    otpInputs[0].focus();
});
</script>
