/* Main Styles */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Card Styles */
.card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
}

/* Form Styles */
.form-control {
    border-radius: 5px;
    padding: 10px 15px;
}

.btn {
    border-radius: 5px;
    padding: 8px 20px;
}

/* Profile Page */
.profile-image {
    max-width: 150px;
    border: 3px solid #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.account-sidebar {
    background-color: #fff;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Minecraft Avatar */
.minecraft-avatar {
    width: 150px;
    height: 150px;
    border: 5px solid #333;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
    image-rendering: pixelated;
}

.minecraft-avatar:hover {
    transform: scale(1.05);
}

.minecraft-skin-controls {
    display: flex;
    justify-content: center;
    margin-top: 10px;
}

.minecraft-skin-control-btn {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    margin: 0 5px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.2s;
    cursor: pointer;
}

.minecraft-skin-control-btn:hover {
    background-color: rgba(255, 255, 255, 0.4);
}

/* Tạo hiệu ứng nền giống Minecraft */
.minecraft-skin-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url('https://i.imgur.com/SLv7F3y.png');
    background-size: cover;
    opacity: 0.2;
    border-radius: 10px;
    z-index: -1;
}

/* Thêm hiệu ứng pixel cho tên người chơi */
@keyframes pixelate {
    0% { text-shadow: 2px 2px 0 #000; }
    50% { text-shadow: 2px 2px 0 #333; }
    100% { text-shadow: 2px 2px 0 #000; }
}

.minecraft-skin-username {
    animation: pixelate 2s infinite;
}

.account-sidebar .nav-link {
    color: #495057;
    padding: 10px 15px;
    border-radius: 5px;
    margin-bottom: 5px;
}

.account-sidebar .nav-link:hover,
.account-sidebar .nav-link.active {
    background-color: #007bff;
    color: #fff;
}

.account-sidebar .nav-link i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

/* Login/Register Forms */
.auth-form {
    max-width: 500px;
    margin: 0 auto;
}

.auth-form .card {
    border: none;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.auth-form .card-header {
    background-color: #007bff;
    color: white;
    text-align: center;
    padding: 20px;
}

.auth-form .card-body {
    padding: 30px;
}

.auth-form .form-group {
    margin-bottom: 20px;
}

.auth-form .btn-primary {
    width: 100%;
    padding: 12px;
    font-weight: bold;
}

.auth-form .form-check {
    margin-bottom: 20px;
}

.auth-form .form-text {
    margin-top: 20px;
    text-align: center;
}

/* Account Dashboard */
.stats-card {
    text-align: center;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    color: white;
}

.stats-card i {
    font-size: 3rem;
    margin-bottom: 15px;
}

.stats-card h3 {
    font-size: 2rem;
    margin-bottom: 10px;
}

.stats-card.bg-primary {
    background-color: #007bff;
}

.stats-card.bg-success {
    background-color: #28a745;
}

.stats-card.bg-info {
    background-color: #17a2b8;
}

.stats-card.bg-warning {
    background-color: #ffc107;
    color: #212529;
}
