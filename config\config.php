<?php
// Database configuration (Main database for nlogin)
define('DB_HOST', 'localhost:3306');
define('DB_USER', 'nlogin');
define('DB_PASS', 'K532kRH6fEYtj3Rf');
define('DB_NAME', 'nlogin');

// Database configuration for card recharge history
define('CARD_DB_HOST', '**************:3306');
define('CARD_DB_USER', 'dotmanearth');
define('CARD_DB_PASS', '5BW67Yb23RGKjWAY');
define('CARD_DB_NAME', 'dotmanearth');

// Website configuration
define('SITE_NAME', '');
define('SITE_URL', 'http://localhost/web_dptmc');

// Connect to main database
try {
    $conn = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8", DB_USER, DB_PASS);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    // If we're in setup mode, don't show error
    if (!isset($setup_mode)) {
        die("Kết nối database chính thất bại: " . $e->getMessage());
    }
}

// Connect to card recharge database
try {
    $card_conn = new PDO("mysql:host=" . CARD_DB_HOST . ";dbname=" . CARD_DB_NAME . ";charset=utf8", CARD_DB_USER, CARD_DB_PASS);
    $card_conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    // Log error but don't stop the application
    error_log("Kết nối database lịch sử nạp thẻ thất bại: " . $e->getMessage());
    $card_conn = null;
}
?>
