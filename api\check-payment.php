<?php
// Tắt error reporting để tránh HTML output
error_reporting(0);
ini_set('display_errors', 0);

header('Content-Type: application/json');

try {
    require_once '../config/config.php';
    require_once '../includes/functions.php';

    // Kiểm tra đăng nhập
    session_start();
    if (!isset($_SESSION['user_id'])) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Unauthorized']);
        exit;
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Config error: ' . $e->getMessage()]);
    exit;
}

// L<PERSON>y dữ li<PERSON>u từ request
try {
    $input_raw = file_get_contents('php://input');
    $input = json_decode($input_raw, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON: ' . json_last_error_msg());
    }

    $transaction_code = $input['transaction_code'] ?? '';

    if (empty($transaction_code)) {
        echo json_encode(['success' => false, 'message' => 'Mã giao dịch không hợp lệ']);
        exit;
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Request error: ' . $e->getMessage()]);
    exit;
}

// Kiểm tra giao dịch pending
if (!isset($_SESSION['pending_transaction']) ||
    $_SESSION['pending_transaction']['transaction_code'] !== $transaction_code) {
    echo json_encode(['success' => false, 'message' => 'Giao dịch không tồn tại']);
    exit;
}

$pending = $_SESSION['pending_transaction'];

// Kiểm tra timeout (30 phút)
if (time() - $pending['created_at'] > 1800) {
    unset($_SESSION['pending_transaction']);
    echo json_encode(['success' => false, 'message' => 'Giao dịch đã hết hạn']);
    exit;
}

try {
    // Gọi API kiểm tra thanh toán với timeout và error handling
    $payment_api_url = 'http://160.25.233.54:3000/payments';

    $context = stream_context_create([
        'http' => [
            'timeout' => 10, // 10 seconds timeout
            'method' => 'GET',
            'header' => [
                'User-Agent: Mozilla/5.0 (compatible; Payment-Checker/1.0)',
                'Accept: application/json'
            ]
        ]
    ]);

    $response = file_get_contents($payment_api_url, false, $context);

    if ($response === false) {
        $error = error_get_last();
        throw new Exception('Không thể kết nối đến API thanh toán: ' . ($error['message'] ?? 'Unknown error'));
    }

    $payments = json_decode($response, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Lỗi parse JSON từ API: ' . json_last_error_msg());
    }

    if (!is_array($payments)) {
        throw new Exception('Dữ liệu API không hợp lệ - không phải array');
    }

    // Tìm giao dịch khớp
    $found_payment = null;
    foreach ($payments as $payment) {
        $content = $payment['content'] ?? '';

        // Kiểm tra nhiều cách khớp:
        // 1. Nội dung chứa mã giao dịch đầy đủ
        // 2. Nội dung chứa server + một phần mã (8 ký tự cuối)
        // 3. Kiểm tra số tiền và thời gian gần đây (trong vòng 30 phút)

        $server = $pending['server'];
        $amount_match = ($payment['amount'] == $pending['amount']);
        $time_match = (time() - strtotime($payment['date']) < 1800); // 30 phút

        $content_match = false;

        // Kiểm tra mã giao dịch đầy đủ
        if (strpos($content, $transaction_code) !== false) {
            $content_match = true;
        }
        // Kiểm tra server + pattern (ví dụ: earthQHGBHTZM)
        elseif (preg_match('/' . $server . '[A-Z0-9]{8}/i', $content)) {
            $content_match = true;
        }
        // Kiểm tra chỉ có server name trong nội dung
        elseif (stripos($content, $server) !== false && $amount_match && $time_match) {
            $content_match = true;
        }

        if ($content_match && $amount_match) {
            $found_payment = $payment;
            break;
        }
    }

    if (!$found_payment) {
        // Debug: Hiển thị thông tin để kiểm tra
        $debug_info = [
            'transaction_code' => $transaction_code,
            'expected_amount' => $pending['amount'],
            'server' => $pending['server'],
            'recent_payments' => []
        ];

        // Lấy 3 giao dịch gần nhất để debug
        foreach (array_slice($payments, 0, 3) as $payment) {
            $debug_info['recent_payments'][] = [
                'amount' => $payment['amount'],
                'content' => $payment['content'],
                'date' => $payment['date'],
                'time_diff' => time() - strtotime($payment['date'])
            ];
        }

        echo json_encode([
            'success' => false,
            'message' => 'Chưa nhận được thanh toán',
            'debug' => $debug_info
        ]);
        exit;
    }

    // Kiểm tra xem giao dịch đã được xử lý chưa
    $stmt = $conn->prepare("SELECT id FROM nlogin_qr_transactions WHERE transaction_id = :transaction_id");
    $stmt->bindParam(':transaction_id', $found_payment['transaction_id']);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        echo json_encode([
            'success' => true,
            'message' => 'Giao dịch đã được xử lý trước đó. Tiền đã được cộng vào tài khoản game.',
            'already_processed' => true
        ]);
        exit;
    }

    // Lưu giao dịch vào database
    $user = getUserData($_SESSION['user_id']);
    $stmt = $conn->prepare("
        INSERT INTO nlogin_qr_transactions
        (user_id, username, transaction_id, amount, server, transaction_code, payment_data, created_at, processed_at)
        VALUES (:user_id, :username, :transaction_id, :amount, :server, :transaction_code, :payment_data, NOW(), NOW())
    ");

    $stmt->bindParam(':user_id', $_SESSION['user_id']);
    $stmt->bindParam(':username', $user['last_name']);
    $stmt->bindParam(':transaction_id', $found_payment['transaction_id']);
    $stmt->bindParam(':amount', $pending['amount']);
    $stmt->bindParam(':server', $pending['server']);
    $stmt->bindParam(':transaction_code', $transaction_code);
    $stmt->bindParam(':payment_data', json_encode($found_payment));
    $stmt->execute();

    // Gửi lệnh đến server game qua Pterodactyl API
    $command_result = sendGameCommand($user['last_name'], $pending['amount'], $pending['server']);

    if ($command_result['success']) {
        // Xóa giao dịch pending
        unset($_SESSION['pending_transaction']);

        echo json_encode([
            'success' => true,
            'message' => 'Thanh toán thành công! Tiền đã được cộng vào tài khoản game.',
            'amount' => $pending['amount'],
            'server' => $pending['server'],
            'command_sent' => $command_result['command'],
            'pterodactyl_response' => $command_result['response_code']
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Thanh toán đã nhận nhưng có lỗi khi cộng tiền vào game: ' . $command_result['error'],
            'debug' => [
                'command' => $command_result['command'] ?? 'N/A',
                'error' => $command_result['error'] ?? 'Unknown error',
                'response_code' => $command_result['response_code'] ?? 'N/A'
            ]
        ]);
    }

} catch (Exception $e) {
    error_log("Payment check error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Có lỗi xảy ra: ' . $e->getMessage()]);
}

/**
 * Gửi lệnh cộng tiền đến server game
 */
function sendGameCommand($username, $amount, $server) {
    // Cấu hình Pterodactyl API
    $pterodactyl_config = [
        'earth' => [
            'server_id' => '8661ab4b',
            'api_key' => 'ptlc_DzHfXQUGVnsUhaA9cz6M27J47hRIEyAGo9t7YYMl0Gs'
        ],
        'ultrapvp' => [
            'server_id' => 'ec2c6a6f',
            'api_key' => 'ptlc_DzHfXQUGVnsUhaA9cz6M27J47hRIEyAGo9t7YYMl0Gs'
        ],
        'skyblock' => [
            'server_id' => '889ecbac',
            'api_key' => 'ptlc_DzHfXQUGVnsUhaA9cz6M27J47hRIEyAGo9t7YYMl0Gs'
        ]
    ];

    $command = "dotman napthucong $username $amount -f";

    if (!isset($pterodactyl_config[$server])) {
        return [
            'success' => false,
            'error' => "Unknown server: $server",
            'command' => $command,
            'response_code' => 'N/A'
        ];
    }

    $config = $pterodactyl_config[$server];
    $base_url = 'https://panel.gamehosting.vn';

    // Thử cả hai cách: với và không có /api/client prefix
    $endpoints_to_try = [
        "$base_url/api/client/servers/{$config['server_id']}/command",
        "$base_url/servers/{$config['server_id']}/command",
        "$base_url/api/servers/{$config['server_id']}/command"
    ];

    foreach ($endpoints_to_try as $endpoint) {
        $result = sendCommandToEndpoint($endpoint, $config['api_key'], $command);

        if ($result['success'] || $result['response_code'] !== 404) {
            // Nếu thành công hoặc không phải 404 (có thể là 502 server offline)
            return $result;
        }
    }

    // Nếu tất cả endpoint đều 404, log lệnh để thực hiện thủ công
    error_log("PTERODACTYL COMMAND TO EXECUTE MANUALLY: $command (Server: $server)");

    return [
        'success' => true, // Trả về success để không block user
        'command' => $command,
        'response_code' => 'MANUAL_EXECUTION_REQUIRED',
        'error' => null,
        'note' => 'Command logged for manual execution - all endpoints returned 404'
    ];
}

/**
 * Gửi lệnh đến một endpoint cụ thể
 */
function sendCommandToEndpoint($endpoint, $api_key, $command) {
    $data = json_encode(['command' => $command]);

    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $endpoint,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $data,
        CURLOPT_HTTPHEADER => [
            'Accept: application/json',
            'Content-Type: application/json',
            'Authorization: Bearer ' . $api_key
        ],
        CURLOPT_TIMEOUT => 30
    ]);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        return [
            'success' => false,
            'error' => "cURL error: $error",
            'command' => $command,
            'response_code' => 'CURL_ERROR',
            'endpoint' => $endpoint
        ];
    }

    if ($http_code === 204) {
        // Thành công - log chi tiết để debug
        error_log("PTERODACTYL COMMAND SENT SUCCESSFULLY: $command");
        error_log("Endpoint used: $endpoint");
        error_log("Please check game console/chat for command execution");

        return [
            'success' => true,
            'command' => $command,
            'response_code' => $http_code,
            'error' => null,
            'endpoint' => $endpoint
        ];
    } elseif ($http_code === 502) {
        // Server offline
        return [
            'success' => false,
            'error' => "Server is offline",
            'command' => $command,
            'response_code' => $http_code,
            'endpoint' => $endpoint
        ];
    } else {
        return [
            'success' => false,
            'error' => "HTTP $http_code: $response",
            'command' => $command,
            'response_code' => $http_code,
            'endpoint' => $endpoint
        ];
    }
}

?>
