<?php
// Tắt error reporting để tránh HTML output
error_reporting(0);
ini_set('display_errors', 0);

// Bắt tất cả errors
set_error_handler(function($severity, $message, $file, $line) {
    error_log("API Error: $message in $file on line $line");
    return true;
});

header('Content-Type: application/json');

try {
    require_once '../config/config.php';
    require_once '../includes/functions.php';

    // Kiểm tra đăng nhập
    session_start();
    if (!isset($_SESSION['user_id'])) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Unauthorized']);
        exit;
    }
} catch (Exception $e) {
    error_log("API Config Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Config error: ' . $e->getMessage()]);
    exit;
} catch (Error $e) {
    error_log("API Fatal Error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Fatal error: ' . $e->getMessage()]);
    exit;
}

// Lấy dữ liệu từ request
try {
    $input_raw = file_get_contents('php://input');
    $input = json_decode($input_raw, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Invalid JSON: ' . json_last_error_msg());
    }

    $transaction_code = $input['transaction_code'] ?? '';

    if (empty($transaction_code)) {
        echo json_encode(['success' => false, 'message' => 'Mã giao dịch không hợp lệ']);
        exit;
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Request error: ' . $e->getMessage()]);
    exit;
}

// Kiểm tra giao dịch pending
if (!isset($_SESSION['pending_transaction']) ||
    $_SESSION['pending_transaction']['transaction_code'] !== $transaction_code) {
    echo json_encode(['success' => false, 'message' => 'Giao dịch không tồn tại', 'expired' => true]);
    exit;
}

$pending = $_SESSION['pending_transaction'];

// Kiểm tra timeout (6 phút = 360 giây để có buffer)
if (time() - $pending['created_at'] > 360) {
    unset($_SESSION['pending_transaction']);
    echo json_encode(['success' => false, 'message' => 'Giao dịch đã hết hạn', 'expired' => true]);
    exit;
}

// Kiểm tra trong database xem giao dịch có bị hủy không
$stmt = $conn->prepare("
    SELECT status, processed_at FROM nlogin_qr_pending
    WHERE user_id = :user_id AND transaction_code = :transaction_code
");
$stmt->bindParam(':user_id', $_SESSION['user_id']);
$stmt->bindParam(':transaction_code', $transaction_code);
$stmt->execute();
$pending_db = $stmt->fetch(PDO::FETCH_ASSOC);

if ($pending_db) {
    if ($pending_db['status'] == 3) {
        // Status = 3: Cancelled
        unset($_SESSION['pending_transaction']);
        echo json_encode(['success' => false, 'message' => 'Giao dịch đã bị hủy', 'expired' => true]);
        exit;
    } elseif ($pending_db['status'] == 1) {
        // Status = 1: Success
        unset($_SESSION['pending_transaction']);
        echo json_encode(['success' => true, 'message' => 'Giao dịch đã được xử lý thành công trước đó']);
        exit;
    }
    // Status = 0: Pending - tiếp tục xử lý
}

try {
    // Gọi API kiểm tra thanh toán với timeout và error handling
    $payment_api_url = 'http://160.25.233.54:3000/payments';

    $context = stream_context_create([
        'http' => [
            'timeout' => 10, // 10 seconds timeout
            'method' => 'GET',
            'header' => [
                'User-Agent: Mozilla/5.0 (compatible; Payment-Checker/1.0)',
                'Accept: application/json'
            ]
        ]
    ]);

    $response = file_get_contents($payment_api_url, false, $context);

    if ($response === false) {
        $error = error_get_last();
        throw new Exception('Không thể kết nối đến API thanh toán: ' . ($error['message'] ?? 'Unknown error'));
    }

    $payments = json_decode($response, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('Lỗi parse JSON từ API: ' . json_last_error_msg());
    }

    if (!is_array($payments)) {
        throw new Exception('Dữ liệu API không hợp lệ - không phải array');
    }

    // Tìm giao dịch khớp - CHỈ trong 5 phút gần đây
    $found_payment = null;
    $current_time = time();

    foreach ($payments as $payment) {
        // Bỏ qua giao dịch cũ hơn 5 phút ngay từ đầu
        $payment_time = strtotime($payment['date']);
        if (($current_time - $payment_time) > 300) {
            continue;
        }

        $content = $payment['content'] ?? '';

        // Kiểm tra nhiều cách khớp:
        // 1. Nội dung chứa mã giao dịch đầy đủ
        // 2. Nội dung chứa server + một phần mã (8 ký tự cuối)
        // 3. Kiểm tra số tiền và thời gian gần đây (trong vòng 5 phút)

        $server = $pending['server'];
        $amount_match = ($payment['amount'] == $pending['amount']);
        $time_match = (time() - strtotime($payment['date']) < 600); // 10 phút để có buffer

        $content_match = false;

        // Lấy phần hash (8 ký tự cuối) từ transaction code
        $hash_part = substr($transaction_code, strlen($server)); // Bỏ phần server, lấy hash

        // Debug log
        error_log("Checking payment content: " . substr($content, 0, 100) . "...");
        error_log("Transaction code: $transaction_code, Hash part: $hash_part");

        // Kiểm tra mã giao dịch đầy đủ (case insensitive)
        if (stripos($content, $transaction_code) !== false) {
            $content_match = true;
            error_log("Match found: Full transaction code");
        }
        // Kiểm tra với uppercase
        elseif (stripos($content, strtoupper($transaction_code)) !== false) {
            $content_match = true;
            error_log("Match found: Full transaction code (uppercase)");
        }
        // Kiểm tra CHỈ phần hash (8 ký tự) - LINH HOẠT HƠN
        elseif (stripos($content, $hash_part) !== false) {
            $content_match = true;
            error_log("Match found: Hash part ($hash_part)");
        }
        // Kiểm tra hash uppercase
        elseif (stripos($content, strtoupper($hash_part)) !== false) {
            $content_match = true;
            error_log("Match found: Hash part uppercase (" . strtoupper($hash_part) . ")");
        }
        // Kiểm tra chỉ có server name trong nội dung
        elseif (stripos($content, $server) !== false && $amount_match && $time_match) {
            $content_match = true;
        }

        if ($content_match && $amount_match) {
            $found_payment = $payment;
            break;
        }
    }

    if (!$found_payment) {
        // Kiểm tra xem giao dịch có hết hạn không
        $time_since_created = time() - $pending['created_at'];

        if ($time_since_created > 360) {
            // Giao dịch đã hết hạn (6 phút)
            unset($_SESSION['pending_transaction']);
            echo json_encode([
                'success' => false,
                'message' => 'Giao dịch đã hết hạn và chưa nhận được thanh toán',
                'expired' => true
            ]);
        } else {
            // Giao dịch chưa hết hạn, chưa có thanh toán
            $debug_info = [
                'transaction_code' => $transaction_code,
                'expected_amount' => $pending['amount'],
                'server' => $pending['server'],
                'time_left' => 300 - $time_since_created,
                'recent_payments' => []
            ];

            // Lấy 3 giao dịch gần nhất để debug
            foreach (array_slice($payments, 0, 3) as $payment) {
                $debug_info['recent_payments'][] = [
                    'amount' => $payment['amount'],
                    'content' => $payment['content'],
                    'date' => $payment['date'],
                    'time_diff' => time() - strtotime($payment['date'])
                ];
            }

            echo json_encode([
                'success' => false,
                'message' => 'Chưa nhận được thanh toán',
                'expired' => false,
                'debug' => $debug_info
            ]);
        }
        exit;
    }

    // Kiểm tra xem giao dịch đã được xử lý chưa - CHỈ kiểm tra giao dịch trong 1 giờ qua
    $stmt = $conn->prepare("
        SELECT id, transaction_code, created_at FROM nlogin_qr_transactions
        WHERE transaction_id = :transaction_id
        AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
    ");
    $stmt->bindParam(':transaction_id', $found_payment['transaction_id']);
    $stmt->execute();
    $existing_transaction = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($existing_transaction) {
        // Nếu giao dịch đã xử lý NHƯNG có cùng transaction_code hiện tại thì OK
        if ($existing_transaction['transaction_code'] === $transaction_code) {
            echo json_encode([
                'success' => true,
                'message' => 'Giao dịch đã được xử lý thành công trước đó.',
                'already_processed' => false // Đây là giao dịch hiện tại, không phải cũ
            ]);
        } else {
            // Giao dịch đã xử lý với transaction_code KHÁC trong 1 giờ qua
            echo json_encode([
                'success' => false,
                'message' => 'Giao dịch này đã được xử lý với mã khác trong 1 giờ qua. Đây không phải giao dịch hiện tại.',
                'already_processed' => true,
                'expired' => true
            ]);
        }
        exit;
    }

    // Lưu giao dịch vào database
    $user = getUserData($_SESSION['user_id']);
    $stmt = $conn->prepare("
        INSERT INTO nlogin_qr_transactions
        (user_id, username, transaction_id, amount, server, transaction_code, payment_data, created_at, processed_at)
        VALUES (:user_id, :username, :transaction_id, :amount, :server, :transaction_code, :payment_data, NOW(), NOW())
    ");

    $stmt->bindParam(':user_id', $_SESSION['user_id']);
    $stmt->bindParam(':username', $user['last_name']);
    $stmt->bindParam(':transaction_id', $found_payment['transaction_id']);
    $stmt->bindParam(':amount', $pending['amount']);
    $stmt->bindParam(':server', $pending['server']);
    $stmt->bindParam(':transaction_code', $transaction_code);
    $stmt->bindParam(':payment_data', json_encode($found_payment));
    $stmt->execute();

    // Cập nhật status pending transaction thành success
    $stmt = $conn->prepare("
        UPDATE nlogin_qr_pending
        SET status = 1, processed = 1, processed_at = NOW()
        WHERE user_id = :user_id AND transaction_code = :transaction_code
    ");
    $stmt->bindParam(':user_id', $_SESSION['user_id']);
    $stmt->bindParam(':transaction_code', $transaction_code);
    $stmt->execute();

    // Gửi lệnh đến server game qua Pterodactyl API
    $command_result = sendGameCommand($user['last_name'], $pending['amount'], $pending['server']);

    if ($command_result['success']) {
        // Xóa giao dịch pending
        unset($_SESSION['pending_transaction']);

        echo json_encode([
            'success' => true,
            'message' => 'Thanh toán thành công! Tiền đã được cộng vào tài khoản game.',
            'amount' => $pending['amount'],
            'server' => $pending['server'],
            'command_sent' => $command_result['command'],
            'pterodactyl_response' => $command_result['response_code']
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Thanh toán đã nhận nhưng có lỗi khi cộng tiền vào game: ' . $command_result['error'],
            'debug' => [
                'command' => $command_result['command'] ?? 'N/A',
                'error' => $command_result['error'] ?? 'Unknown error',
                'response_code' => $command_result['response_code'] ?? 'N/A'
            ]
        ]);
    }

} catch (Exception $e) {
    error_log("Payment check error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Có lỗi xảy ra: ' . $e->getMessage()]);
} catch (Error $e) {
    error_log("Payment check fatal error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Lỗi nghiêm trọng: ' . $e->getMessage()]);
} catch (Throwable $e) {
    error_log("Payment check throwable: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Lỗi không xác định: ' . $e->getMessage()]);
}

/**
 * Gửi lệnh cộng tiền đến server game
 */
function sendGameCommand($username, $amount, $server) {
    // Cấu hình Pterodactyl API
    $pterodactyl_config = [
        'earth' => [
            'server_id' => '8661ab4b',
            'api_key' => 'ptlc_DzHfXQUGVnsUhaA9cz6M27J47hRIEyAGo9t7YYMl0Gs'
        ],
        'ultrapvp' => [
            'server_id' => 'ec2c6a6f',
            'api_key' => 'ptlc_DzHfXQUGVnsUhaA9cz6M27J47hRIEyAGo9t7YYMl0Gs'
        ],
        'skyblock' => [
            'server_id' => '889ecbac',
            'api_key' => 'ptlc_DzHfXQUGVnsUhaA9cz6M27J47hRIEyAGo9t7YYMl0Gs'
        ]
    ];

    $command = "dotman napthucong $username $amount -f";

    if (!isset($pterodactyl_config[$server])) {
        return [
            'success' => false,
            'error' => "Unknown server: $server",
            'command' => $command,
            'response_code' => 'N/A'
        ];
    }

    $config = $pterodactyl_config[$server];
    $base_url = 'https://panel.gamehosting.vn';

    // Thử cả hai cách: với và không có /api/client prefix
    $endpoints_to_try = [
        "$base_url/api/client/servers/{$config['server_id']}/command",
        "$base_url/servers/{$config['server_id']}/command",
        "$base_url/api/servers/{$config['server_id']}/command"
    ];

    foreach ($endpoints_to_try as $endpoint) {
        $result = sendCommandToEndpoint($endpoint, $config['api_key'], $command);

        if ($result['success'] || $result['response_code'] !== 404) {
            // Nếu thành công hoặc không phải 404 (có thể là 502 server offline)
            return $result;
        }
    }

    // Nếu tất cả endpoint đều 404, log lệnh để thực hiện thủ công
    error_log("PTERODACTYL COMMAND TO EXECUTE MANUALLY: $command (Server: $server)");

    return [
        'success' => true, // Trả về success để không block user
        'command' => $command,
        'response_code' => 'MANUAL_EXECUTION_REQUIRED',
        'error' => null,
        'note' => 'Command logged for manual execution - all endpoints returned 404'
    ];
}

/**
 * Gửi lệnh đến một endpoint cụ thể
 */
function sendCommandToEndpoint($endpoint, $api_key, $command) {
    $data = json_encode(['command' => $command]);

    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $endpoint,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => $data,
        CURLOPT_HTTPHEADER => [
            'Accept: application/json',
            'Content-Type: application/json',
            'Authorization: Bearer ' . $api_key
        ],
        CURLOPT_TIMEOUT => 30
    ]);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        return [
            'success' => false,
            'error' => "cURL error: $error",
            'command' => $command,
            'response_code' => 'CURL_ERROR',
            'endpoint' => $endpoint
        ];
    }

    if ($http_code === 204) {
        // Thành công - log chi tiết để debug
        error_log("PTERODACTYL COMMAND SENT SUCCESSFULLY: $command");
        error_log("Endpoint used: $endpoint");
        error_log("Please check game console/chat for command execution");

        return [
            'success' => true,
            'command' => $command,
            'response_code' => $http_code,
            'error' => null,
            'endpoint' => $endpoint
        ];
    } elseif ($http_code === 502) {
        // Server offline
        return [
            'success' => false,
            'error' => "Server is offline",
            'command' => $command,
            'response_code' => $http_code,
            'endpoint' => $endpoint
        ];
    } else {
        return [
            'success' => false,
            'error' => "HTTP $http_code: $response",
            'command' => $command,
            'response_code' => $http_code,
            'endpoint' => $endpoint
        ];
    }
}

?>
