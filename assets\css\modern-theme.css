/**
 * Modern Theme CSS
 * A clean, modern theme for DPTMC.COM ID system
 */

:root {
    /* Main Colors */
    --primary: #3498db;
    --primary-dark: #2980b9;
    --secondary: #2c3e50;
    --secondary-dark: #1a252f;
    --success: #2ecc71;
    --success-dark: #27ae60;
    --danger: #e74c3c;
    --danger-dark: #c0392b;
    --warning: #f39c12;
    --warning-dark: #d35400;
    --info: #3498db;
    --info-dark: #2980b9;
    --light: #ecf0f1;
    --light-dark: #bdc3c7;
    --dark: #34495e;
    --dark-dark: #2c3e50;
    --white: #ffffff;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    --gray-400: #ced4da;
    --gray-500: #adb5bd;
    --gray-600: #6c757d;
    --gray-700: #495057;
    --gray-800: #343a40;
    --gray-900: #212529;
    --black: #000000;
    
    /* Fonts */
    --font-family-sans-serif: 'Roboto', -apple-system, BlinkMacSystemFont, "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    
    /* Spacing */
    --spacer: 1rem;
    
    /* Border Radius */
    --border-radius: 0.25rem;
    --border-radius-lg: 0.5rem;
    --border-radius-sm: 0.2rem;
    
    /* Box Shadow */
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

/* Base Styles */
body {
    font-family: var(--font-family-sans-serif);
    background-color: var(--gray-100);
    color: var(--gray-800);
    line-height: 1.6;
    padding-top: 70px;
}

a {
    color: var(--primary);
    text-decoration: none;
    transition: all 0.2s ease;
}

a:hover {
    color: var(--primary-dark);
    text-decoration: none;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 500;
    line-height: 1.2;
    margin-bottom: 1rem;
    color: var(--secondary);
}

.text-primary { color: var(--primary) !important; }
.text-secondary { color: var(--secondary) !important; }
.text-success { color: var(--success) !important; }
.text-danger { color: var(--danger) !important; }
.text-warning { color: var(--warning) !important; }
.text-info { color: var(--info) !important; }
.text-light { color: var(--light) !important; }
.text-dark { color: var(--dark) !important; }
.text-muted { color: var(--gray-600) !important; }
.text-white { color: var(--white) !important; }

/* Buttons */
.btn {
    display: inline-block;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.5rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: var(--border-radius);
    transition: all 0.15s ease-in-out;
    cursor: pointer;
}

.btn:focus, .btn:hover {
    text-decoration: none;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.btn-primary {
    color: var(--white);
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:hover {
    color: var(--white);
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-secondary {
    color: var(--white);
    background-color: var(--secondary);
    border-color: var(--secondary);
}

.btn-secondary:hover {
    color: var(--white);
    background-color: var(--secondary-dark);
    border-color: var(--secondary-dark);
}

.btn-success {
    color: var(--white);
    background-color: var(--success);
    border-color: var(--success);
}

.btn-success:hover {
    color: var(--white);
    background-color: var(--success-dark);
    border-color: var(--success-dark);
}

.btn-danger {
    color: var(--white);
    background-color: var(--danger);
    border-color: var(--danger);
}

.btn-danger:hover {
    color: var(--white);
    background-color: var(--danger-dark);
    border-color: var(--danger-dark);
}

.btn-warning {
    color: var(--dark);
    background-color: var(--warning);
    border-color: var(--warning);
}

.btn-warning:hover {
    color: var(--dark);
    background-color: var(--warning-dark);
    border-color: var(--warning-dark);
}

.btn-info {
    color: var(--white);
    background-color: var(--info);
    border-color: var(--info);
}

.btn-info:hover {
    color: var(--white);
    background-color: var(--info-dark);
    border-color: var(--info-dark);
}

.btn-light {
    color: var(--dark);
    background-color: var(--light);
    border-color: var(--light);
}

.btn-light:hover {
    color: var(--dark);
    background-color: var(--light-dark);
    border-color: var(--light-dark);
}

.btn-dark {
    color: var(--white);
    background-color: var(--dark);
    border-color: var(--dark);
}

.btn-dark:hover {
    color: var(--white);
    background-color: var(--dark-dark);
    border-color: var(--dark-dark);
}

.btn-outline-primary {
    color: var(--primary);
    background-color: transparent;
    border-color: var(--primary);
}

.btn-outline-primary:hover {
    color: var(--white);
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-block {
    display: block;
    width: 100%;
}

/* Forms */
.form-group {
    margin-bottom: 1rem;
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.5rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--gray-700);
    background-color: var(--white);
    background-clip: padding-box;
    border: 1px solid var(--gray-400);
    border-radius: var(--border-radius);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    color: var(--gray-700);
    background-color: var(--white);
    border-color: var(--primary);
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-label {
    margin-bottom: 0.5rem;
    font-weight: 500;
}

/* Cards */
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: var(--white);
    background-clip: border-box;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-sm);
    margin-bottom: 1.5rem;
}

.card-header {
    padding: 1rem;
    margin-bottom: 0;
    background-color: var(--gray-100);
    border-bottom: 1px solid var(--gray-300);
}

.card-body {
    flex: 1 1 auto;
    padding: 1.5rem;
}

.card-footer {
    padding: 1rem;
    background-color: var(--gray-100);
    border-top: 1px solid var(--gray-300);
}

/* Alerts */
.alert {
    position: relative;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
}

.alert-primary {
    color: var(--primary-dark);
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.alert-secondary {
    color: var(--secondary-dark);
    background-color: #e2e3e5;
    border-color: #d6d8db;
}

.alert-success {
    color: var(--success-dark);
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: var(--danger-dark);
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-warning {
    color: var(--warning-dark);
    background-color: #fff3cd;
    border-color: #ffeeba;
}

.alert-info {
    color: var(--info-dark);
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* Navbar */
.navbar {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    z-index: 1030;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 1rem;
    background-color: var(--white);
    box-shadow: var(--box-shadow-sm);
}

.navbar-brand {
    display: inline-block;
    padding-top: 0.3125rem;
    padding-bottom: 0.3125rem;
    margin-right: 1rem;
    font-size: 1.25rem;
    line-height: inherit;
    white-space: nowrap;
    font-weight: 600;
    color: var(--primary);
}

.navbar-nav {
    display: flex;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
}

.nav-link {
    display: block;
    padding: 0.5rem 1rem;
    color: var(--gray-700);
}

.nav-link:hover, .nav-link:focus {
    color: var(--primary);
    text-decoration: none;
}

.nav-link.active {
    color: var(--primary);
    font-weight: 500;
}

/* Utilities */
.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 1rem !important; }
.mt-4 { margin-top: 1.5rem !important; }
.mt-5 { margin-top: 3rem !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 1rem !important; }
.mb-4 { margin-bottom: 1.5rem !important; }
.mb-5 { margin-bottom: 3rem !important; }

.ml-auto { margin-left: auto !important; }
.mr-auto { margin-right: auto !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 1rem !important; }
.p-4 { padding: 1.5rem !important; }
.p-5 { padding: 3rem !important; }

.rounded { border-radius: var(--border-radius) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-circle { border-radius: 50% !important; }

.shadow { box-shadow: var(--box-shadow) !important; }
.shadow-sm { box-shadow: var(--box-shadow-sm) !important; }
.shadow-lg { box-shadow: var(--box-shadow-lg) !important; }

.bg-primary { background-color: var(--primary) !important; }
.bg-secondary { background-color: var(--secondary) !important; }
.bg-success { background-color: var(--success) !important; }
.bg-danger { background-color: var(--danger) !important; }
.bg-warning { background-color: var(--warning) !important; }
.bg-info { background-color: var(--info) !important; }
.bg-light { background-color: var(--light) !important; }
.bg-dark { background-color: var(--dark) !important; }
.bg-white { background-color: var(--white) !important; }
.bg-transparent { background-color: transparent !important; }
