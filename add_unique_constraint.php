<?php
// <PERSON><PERSON><PERSON> để thêm unique constraint cho transaction_code
require_once 'config/config.php';

echo "<h2>Add Unique Constraint for Transaction Codes</h2>";

try {
    // 1. <PERSON><PERSON><PERSON> tra và xóa duplicate transaction codes trước
    echo "<h3>1. Kiểm tra duplicate transaction codes:</h3>";
    
    $stmt = $conn->query("
        SELECT transaction_code, COUNT(*) as count 
        FROM nlogin_qr_pending 
        GROUP BY transaction_code 
        HAVING COUNT(*) > 1
    ");
    $duplicates_pending = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $stmt = $conn->query("
        SELECT transaction_code, COUNT(*) as count 
        FROM nlogin_qr_transactions 
        GROUP BY transaction_code 
        HAVING COUNT(*) > 1
    ");
    $duplicates_transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($duplicates_pending) > 0) {
        echo "⚠️ Found " . count($duplicates_pending) . " duplicate codes in nlogin_qr_pending:<br>";
        foreach ($duplicates_pending as $dup) {
            echo "- {$dup['transaction_code']}: {$dup['count']} times<br>";
        }
    } else {
        echo "✅ No duplicates in nlogin_qr_pending<br>";
    }
    
    if (count($duplicates_transactions) > 0) {
        echo "⚠️ Found " . count($duplicates_transLogo

Nạp tiền bằng QR Code
Quét mã QR để nạp tiền vào tài khoản game

Chọn Server:
Earth
UltraPvP
Skyblock
Chọn số tiền:
10.000 VNĐ
20.000 VNĐ
50.000 VNĐ
100.000 VNĐ
200.000 VNĐ
500.000 VNĐ
Hoặc nhập số tiền khác:
2000
Quét mã QR để thanh toán
Mã QR đã hết hạn
Giao dịch đã được xử lý trước đó. Vui lòng tạo mã QR mới.

 Lưu ý: Giao dịch đã bị hủy để đảm bảo bảo mật. Nếu bạn đã chuyển khoản cho giao dịch này, vui lòng liên hệ admin.
 Có thể do: Giao dịch cũ chưa được dọn dẹp hoặc bạn đã thực hiện giao dịch này trước đó.
Thông tin giao dịch:
Số tiền: 2.000 VNĐ

Server: earth

Mã giao dịch: earth5FD0C6B5 

Ngân hàng: MB Bank

Số tài khoản: **********

Tên tài khoản: DPTMC Server

Nội dung chuyển khoản: earth5FD0C6B5

 Hướng dẫn:
1. Mở ứng dụng ngân hàng trên điện thoại
2. Quét mã QR hoặc chuyển khoản thủ công
3. Kiểm tra thông tin và xác nhận thanh toán
4. Tiền sẽ được cộng vào tài khoản game TỰ ĐỘNG trong vòng 1-3 phút
 Hệ thống tự động: Sau khi chuyển khoản thành công, hệ thống sẽ tự động kiểm tra và cộng tiền vào game. Bạn không cần thao tác gì thêm.
© 2025 . Đã đăng ký bản quyền.

 Được hỗ trợ bởi DPTCLOUDactions) . " duplicate codes in nlogin_qr_transactions:<br>";
        foreach ($duplicates_transactions as $dup) {
            echo "- {$dup['transaction_code']}: {$dup['count']} times<br>";
        }
    } else {
        echo "✅ No duplicates in nlogin_qr_transactions<br>";
    }
    
    // 2. Xóa duplicates nếu có
    if (count($duplicates_pending) > 0) {
        echo "<h3>2. Cleaning up duplicates in nlogin_qr_pending:</h3>";
        
        foreach ($duplicates_pending as $dup) {
            $transaction_code = $dup['transaction_code'];
            
            // Giữ lại record mới nhất, xóa các record cũ
            $stmt = $conn->prepare("
                DELETE FROM nlogin_qr_pending 
                WHERE transaction_code = :transaction_code 
                AND id NOT IN (
                    SELECT * FROM (
                        SELECT MAX(id) FROM nlogin_qr_pending 
                        WHERE transaction_code = :transaction_code2
                    ) as temp
                )
            ");
            $stmt->bindParam(':transaction_code', $transaction_code);
            $stmt->bindParam(':transaction_code2', $transaction_code);
            $stmt->execute();
            
            $deleted = $stmt->rowCount();
            echo "✅ Deleted $deleted duplicate records for: $transaction_code<br>";
        }
    }
    
    if (count($duplicates_transactions) > 0) {
        echo "<h3>3. Cleaning up duplicates in nlogin_qr_transactions:</h3>";
        
        foreach ($duplicates_transactions as $dup) {
            $transaction_code = $dup['transaction_code'];
            
            // Giữ lại record mới nhất, xóa các record cũ
            $stmt = $conn->prepare("
                DELETE FROM nlogin_qr_transactions 
                WHERE transaction_code = :transaction_code 
                AND id NOT IN (
                    SELECT * FROM (
                        SELECT MAX(id) FROM nlogin_qr_transactions 
                        WHERE transaction_code = :transaction_code2
                    ) as temp
                )
            ");
            $stmt->bindParam(':transaction_code', $transaction_code);
            $stmt->bindParam(':transaction_code2', $transaction_code);
            $stmt->execute();
            
            $deleted = $stmt->rowCount();
            echo "✅ Deleted $deleted duplicate records for: $transaction_code<br>";
        }
    }
    
    // 3. Thêm unique constraint
    echo "<h3>4. Adding unique constraints:</h3>";
    
    try {
        $conn->exec("ALTER TABLE nlogin_qr_pending ADD UNIQUE KEY unique_transaction_code (transaction_code)");
        echo "✅ Added unique constraint to nlogin_qr_pending.transaction_code<br>";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "ℹ️ Unique constraint already exists on nlogin_qr_pending.transaction_code<br>";
        } else {
            echo "❌ Error adding constraint to nlogin_qr_pending: " . $e->getMessage() . "<br>";
        }
    }
    
    try {
        $conn->exec("ALTER TABLE nlogin_qr_transactions ADD UNIQUE KEY unique_transaction_code (transaction_code)");
        echo "✅ Added unique constraint to nlogin_qr_transactions.transaction_code<br>";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "ℹ️ Unique constraint already exists on nlogin_qr_transactions.transaction_code<br>";
        } else {
            echo "❌ Error adding constraint to nlogin_qr_transactions: " . $e->getMessage() . "<br>";
        }
    }
    
    // 4. Kiểm tra constraints đã được thêm
    echo "<h3>5. Verifying constraints:</h3>";
    
    $stmt = $conn->query("SHOW INDEX FROM nlogin_qr_pending WHERE Key_name = 'unique_transaction_code'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Unique constraint verified on nlogin_qr_pending<br>";
    } else {
        echo "❌ Unique constraint not found on nlogin_qr_pending<br>";
    }
    
    $stmt = $conn->query("SHOW INDEX FROM nlogin_qr_transactions WHERE Key_name = 'unique_transaction_code'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Unique constraint verified on nlogin_qr_transactions<br>";
    } else {
        echo "❌ Unique constraint not found on nlogin_qr_transactions<br>";
    }
    
    // 5. Test tạo mã giao dịch
    echo "<h3>6. Testing transaction code generation:</h3>";
    
    $test_server = 'earth';
    $test_user_id = 12345;
    
    for ($i = 1; $i <= 3; $i++) {
        $timestamp = time() + $i;
        $random_string = $timestamp . $test_user_id . rand(1000, 9999);
        $hash = strtoupper(substr(md5($random_string), 0, 8));
        $test_code = $test_server . $hash;
        
        echo "Test code $i: <code>$test_code</code><br>";
    }
    
    echo "<hr>";
    echo "<h3>✅ Setup hoàn thành!</h3>";
    echo "<p><strong>Kết quả:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Đã xóa tất cả duplicate transaction codes</li>";
    echo "<li>✅ Đã thêm unique constraints</li>";
    echo "<li>✅ Database đã sẵn sàng cho hệ thống mã giao dịch unique</li>";
    echo "</ul>";
    
    echo "<p><strong>Lưu ý:</strong></p>";
    echo "<ul>";
    echo "<li>Mỗi transaction code bây giờ sẽ là unique trong toàn bộ hệ thống</li>";
    echo "<li>Hệ thống sẽ tự động tạo mã mới nếu phát hiện trùng lặp</li>";
    echo "<li>Xóa file này sau khi chạy xong để bảo mật</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage();
}
?>

<style>
code {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
}

h3 {
    color: #333;
    margin-top: 20px;
}

ul {
    margin: 10px 0;
}

li {
    margin: 5px 0;
}
</style>
