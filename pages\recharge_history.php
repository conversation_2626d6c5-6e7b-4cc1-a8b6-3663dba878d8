<?php
// Ki<PERSON>m tra đăng nhập
if (!isset($_SESSION['user_id'])) {
    $_SESSION['message'] = 'Vui lòng đăng nhập để xem lịch sử nạp thẻ.';
    $_SESSION['message_type'] = 'danger';
    header('Location: /login');
    exit;
}

// Lấy thông tin người dùng
$user = getUserData($_SESSION['user_id']);

// Nếu không tìm thấy thông tin người dùng, chuyển hướng đến trang đăng nhập
if (!$user) {
    $_SESSION['message'] = 'Không tìm thấy thông tin tài khoản. Vui lòng đăng nhập lại.';
    $_SESSION['message_type'] = 'danger';
    header('Location: /logout');
    exit;
}

// Lấy UUID của người dùng
$uuid = getUserUUID($_SESSION['user_id']);

// Thiết lập phân trang
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1; // Đảm bảo page >= 1
$limit = 20; // Số bản ghi trên mỗi trang
$offset = ($page - 1) * $limit;

// Lấy lịch sử nạp thẻ
$history = [];
$total_records = 0;

if ($uuid) {
    $history = getCardRechargeHistory($uuid, $limit, $offset);
    $total_records = getCardRechargeHistoryCount($uuid);

    // Debug: Hiển thị thông tin debug (chỉ cho admin hoặc trong development)
    if (isset($_GET['debug']) && $_GET['debug'] == '1') {
        echo "<div style='background: #f8f9fa; padding: 1rem; margin: 1rem 0; border: 1px solid #dee2e6;'>";
        echo "<h5>Debug Info:</h5>";
        echo "<p><strong>UUID:</strong> " . htmlspecialchars($uuid) . "</p>";
        echo "<p><strong>Total Records:</strong> " . $total_records . "</p>";
        echo "<p><strong>History Count:</strong> " . count($history) . "</p>";
        if (!empty($history)) {
            echo "<p><strong>First Record:</strong> " . htmlspecialchars(json_encode($history[0])) . "</p>";
        }
        echo "</div>";
    }
}

// Tính toán phân trang
$total_pages = ceil($total_records / $limit);
?>

<!-- Custom CSS cho trang lịch sử nạp thẻ -->
<style>
.account-container {
    margin-top: 2rem;
    margin-bottom: 3rem;
}

.profile-card {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.profile-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.profile-header {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    padding: 2rem 0;
    position: relative;
}

.avatar-container {
    position: relative;
    width: 150px;
    height: 150px;
    margin: 0 auto;
}

.minecraft-avatar {
    width: 150px;
    height: 150px;
    border: 5px solid #fff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease;
}

.minecraft-avatar:hover {
    transform: scale(1.05);
}

.profile-info {
    padding: 1.5rem;
    background-color: #fff;
}

.nav-pills .nav-link {
    border-radius: 10px;
    padding: 0.8rem 1.2rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.nav-pills .nav-link i {
    margin-right: 0.5rem;
    width: 20px;
}

.nav-pills .nav-link:hover {
    background-color: rgba(var(--primary-rgb), 0.1);
    color: var(--primary);
    transform: translateX(5px);
}

.nav-pills .nav-link.active {
    background-color: var(--primary);
    color: white;
}

.history-card {
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.history-card .card-header {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 1.5rem;
    border: none;
}

.history-card .card-body {
    padding: 0;
}

.history-table {
    margin: 0;
}

.history-table th {
    background-color: #f8f9fa;
    border: none;
    padding: 1rem;
    font-weight: 600;
    color: #495057;
}

.history-table td {
    padding: 1rem;
    border-top: 1px solid #dee2e6;
    vertical-align: middle;
}

.history-table tbody tr:hover {
    background-color: rgba(var(--primary-rgb), 0.05);
}

.status-badge {
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.status-success {
    background-color: #d4edda;
    color: #155724;
}

.status-warning {
    background-color: #fff3cd;
    color: #856404;
}

.status-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

.pagination .page-link {
    border-radius: 10px;
    margin: 0 0.2rem;
    border: none;
    color: var(--primary);
}

.pagination .page-item.active .page-link {
    background-color: var(--primary);
    border-color: var(--primary);
}

.pagination .page-link:hover {
    background-color: rgba(var(--primary-rgb), 0.1);
}

@media (max-width: 768px) {
    .history-table {
        font-size: 0.9rem;
    }

    .history-table th,
    .history-table td {
        padding: 0.5rem;
    }

    .d-none-mobile {
        display: none !important;
    }
}
</style>

<div class="container account-container">
    <div class="row">
        <!-- Sidebar / Profile Card -->
        <div class="col-lg-4 mb-4">
            <div class="profile-card">
                <div class="profile-header">
                    <div class="avatar-container">
                        <img src="<?php echo getMinecraftAvatarUrl($user['last_name'], 'avatar', 150); ?>" alt="Minecraft Avatar" class="minecraft-avatar rounded-circle">
                    </div>
                </div>
                <div class="profile-info">
                    <div class="text-center mb-4">
                        <h4 class="font-weight-bold mb-1"><?php echo $user['last_name'] ?? $user['unique_id']; ?></h4>
                        <p class="text-muted"><?php echo $user['email'] ?? '<span class="text-danger">Chưa có email</span>'; ?></p>
                    </div>

                    <ul class="nav nav-pills flex-column">
                        <li class="nav-item">
                            <a href="/account" class="nav-link">
                                <i class="fas fa-tachometer-alt"></i> Trang chủ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/profile" class="nav-link">
                                <i class="fas fa-user-edit"></i> Chỉnh sửa hồ sơ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/recharge-history" class="nav-link active">
                                <i class="fas fa-credit-card"></i> Lịch sử nạp thẻ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/logout" class="nav-link text-danger">
                                <i class="fas fa-sign-out-alt"></i> Đăng xuất
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-lg-8">
            <div class="history-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-credit-card mr-2"></i>
                        Lịch sử nạp thẻ
                        <?php if ($total_records > 0): ?>
                            <span class="badge badge-light ml-2"><?php echo number_format($total_records); ?> giao dịch</span>
                        <?php endif; ?>
                    </h5>
                </div>
                <div class="card-body">
                    <?php
                    // Debug thông tin
                    if ($total_records > 0 && empty($history)) {
                        echo "<div style='background: #fff3cd; padding: 1rem; margin: 1rem; border: 1px solid #ffeaa7; border-radius: 5px;'>";
                        echo "<h6>🔍 Debug Info:</h6>";
                        echo "<p><strong>UUID:</strong> " . htmlspecialchars($uuid ?? 'NULL') . "</p>";
                        echo "<p><strong>Total Records:</strong> " . $total_records . "</p>";
                        echo "<p><strong>History Array Count:</strong> " . count($history) . "</p>";
                        echo "<p><strong>Page:</strong> " . $page . " | <strong>Limit:</strong> " . $limit . " | <strong>Offset:</strong> " . $offset . "</p>";

                        // Test trực tiếp
                        if ($uuid && $card_conn) {
                            try {
                                $test_stmt = $card_conn->prepare("SELECT COUNT(*) as count FROM dotman_napthe_log WHERE uuid = :uuid1 OR uuid = :uuid2");
                                $formatted_uuid = formatUUID($uuid);
                                $unformatted_uuid = unformatUUID($uuid);
                                $test_stmt->bindParam(':uuid1', $formatted_uuid);
                                $test_stmt->bindParam(':uuid2', $unformatted_uuid);
                                $test_stmt->execute();
                                $test_result = $test_stmt->fetch(PDO::FETCH_ASSOC);
                                echo "<p><strong>Direct Query Count:</strong> " . $test_result['count'] . "</p>";

                                // Lấy 1 bản ghi để test
                                $test_stmt = $card_conn->prepare("SELECT * FROM dotman_napthe_log WHERE uuid = :uuid1 OR uuid = :uuid2 LIMIT 1");
                                $test_stmt->bindParam(':uuid1', $formatted_uuid);
                                $test_stmt->bindParam(':uuid2', $unformatted_uuid);
                                $test_stmt->execute();
                                $test_record = $test_stmt->fetch(PDO::FETCH_ASSOC);
                                if ($test_record) {
                                    echo "<p><strong>Sample Record:</strong> " . htmlspecialchars(json_encode($test_record)) . "</p>";
                                }
                            } catch(Exception $e) {
                                echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
                            }
                        }
                        echo "</div>";
                    }
                    ?>

                    <?php if (empty($history)): ?>
                        <div class="empty-state">
                            <i class="fas fa-credit-card"></i>
                            <h5>Chưa có lịch sử nạp thẻ</h5>
                            <p>Bạn chưa thực hiện giao dịch nạp thẻ nào.</p>
                            <?php if ($total_records > 0): ?>
                                <p class="text-warning"><small>Phát hiện có <?php echo $total_records; ?> giao dịch nhưng không thể hiển thị. Vui lòng liên hệ admin.</small></p>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table history-table">
                                <thead>
                                    <tr>
                                        <th>Thời gian</th>
                                        <th>Loại thẻ</th>
                                        <th>Mệnh giá</th>
                                        <th class="d-none-mobile">Seri</th>
                                        <th class="d-none-mobile">Server</th>
                                        <th>Trạng thái</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($history as $record): ?>
                                        <tr>
                                            <td>
                                                <small class="text-muted"><?php echo $record['formatted_time']; ?></small>
                                            </td>
                                            <td>
                                                <strong><?php echo $record['type_name'] ?? 'N/A'; ?></strong>
                                            </td>
                                            <td>
                                                <span class="font-weight-bold text-primary"><?php echo $record['formatted_price']; ?></span>
                                            </td>
                                            <td class="d-none-mobile">
                                                <code><?php echo $record['seri'] ?? 'N/A'; ?></code>
                                            </td>
                                            <td class="d-none-mobile">
                                                <span class="badge badge-secondary"><?php echo $record['server'] ?? 'N/A'; ?></span>
                                            </td>
                                            <td>
                                                <span class="status-badge status-<?php echo $record['status_class']; ?>">
                                                    <?php echo $record['status_text']; ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Phân trang -->
                        <?php if ($total_pages > 1): ?>
                            <div class="pagination-wrapper">
                                <nav aria-label="Phân trang lịch sử nạp thẻ">
                                    <ul class="pagination">
                                        <?php if ($page > 1): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<?php echo $page - 1; ?>">
                                                    <i class="fas fa-chevron-left"></i>
                                                </a>
                                            </li>
                                        <?php endif; ?>

                                        <?php
                                        $start = max(1, $page - 2);
                                        $end = min($total_pages, $page + 2);

                                        for ($i = $start; $i <= $end; $i++):
                                        ?>
                                            <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                                <a class="page-link" href="?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                            </li>
                                        <?php endfor; ?>

                                        <?php if ($page < $total_pages): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?page=<?php echo $page + 1; ?>">
                                                    <i class="fas fa-chevron-right"></i>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </nav>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
