<?php
// Debug script cho transaction cụ thể
require_once 'config/config.php';

echo "<h2>Debug Specific Transaction: earthBDEA4882</h2>";

$target_transaction = 'earthBDEA4882';
$target_amount = 2000;

// 1. <PERSON><PERSON><PERSON> tra trong pending
echo "<h3>1. Pending Transactions:</h3>";
try {
    $stmt = $conn->prepare("
        SELECT * FROM nlogin_qr_pending 
        WHERE transaction_code LIKE :code 
        ORDER BY created_at DESC
    ");
    $like_code = '%' . $target_transaction . '%';
    $stmt->bindParam(':code', $like_code);
    $stmt->execute();
    $pending_txs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($pending_txs) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>User</th><th>Transaction Code</th><th>Amount</th><th>Status</th><th>Processed</th><th>Check Count</th><th>Created</th>";
        echo "</tr>";
        
        foreach ($pending_txs as $tx) {
            $status_color = '';
            if ($tx['status'] == 0) $status_color = '#ffc107';
            elseif ($tx['status'] == 1) $status_color = '#28a745';
            elseif ($tx['status'] == 3) $status_color = '#dc3545';
            
            echo "<tr>";
            echo "<td>{$tx['id']}</td>";
            echo "<td>{$tx['user_id']}</td>";
            echo "<td><code>{$tx['transaction_code']}</code></td>";
            echo "<td>" . number_format($tx['amount']) . "</td>";
            echo "<td style='background: $status_color; color: white;'>{$tx['status']}</td>";
            echo "<td>{$tx['processed']}</td>";
            echo "<td>" . ($tx['check_count'] ?? 0) . "</td>";
            echo "<td>{$tx['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ Không tìm thấy trong pending<br>";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

// 2. Kiểm tra trong completed
echo "<h3>2. Completed Transactions:</h3>";
try {
    $stmt = $conn->prepare("
        SELECT * FROM nlogin_qr_transactions 
        WHERE transaction_code LIKE :code 
        OR transaction_id LIKE :tid
        ORDER BY created_at DESC
    ");
    $like_code = '%' . $target_transaction . '%';
    $like_tid = '%FT25153229021389%';
    $stmt->bindParam(':code', $like_code);
    $stmt->bindParam(':tid', $like_tid);
    $stmt->execute();
    $completed_txs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($completed_txs) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>User</th><th>Transaction Code</th><th>Transaction ID</th><th>Amount</th><th>Created</th>";
        echo "</tr>";
        
        foreach ($completed_txs as $tx) {
            echo "<tr>";
            echo "<td>{$tx['id']}</td>";
            echo "<td>{$tx['user_id']}</td>";
            echo "<td><code>{$tx['transaction_code']}</code></td>";
            echo "<td><code>{$tx['transaction_id']}</code></td>";
            echo "<td>" . number_format($tx['amount']) . "</td>";
            echo "<td>{$tx['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ Không tìm thấy trong completed<br>";
    }
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

// 3. Kiểm tra API payment
echo "<h3>3. API Payment Data:</h3>";
try {
    $payment_api_url = 'http://*************:3000/payments';
    $response = file_get_contents($payment_api_url);
    
    if ($response) {
        $payments = json_decode($response, true);
        $found_payments = [];
        
        foreach ($payments as $payment) {
            $content = $payment['content'] ?? '';
            
            // Tìm payment khớp với transaction code (case insensitive)
            if (stripos($content, $target_transaction) !== false || 
                stripos($content, strtoupper($target_transaction)) !== false ||
                stripos($content, strtolower($target_transaction)) !== false) {
                $found_payments[] = $payment;
            }
        }
        
        if ($found_payments) {
            echo "<strong>Tìm thấy " . count($found_payments) . " payment khớp:</strong><br>";
            
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr style='background: #f0f0f0;'>";
            echo "<th>Transaction ID</th><th>Amount</th><th>Content</th><th>Date</th><th>Age (minutes)</th>";
            echo "</tr>";
            
            foreach ($found_payments as $payment) {
                $time_diff = (time() - strtotime($payment['date'])) / 60;
                
                echo "<tr>";
                echo "<td><code>{$payment['transaction_id']}</code></td>";
                echo "<td>" . number_format($payment['amount']) . "</td>";
                echo "<td><code>" . htmlspecialchars($payment['content']) . "</code></td>";
                echo "<td>{$payment['date']}</td>";
                echo "<td>" . round($time_diff, 1) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "❌ Không tìm thấy payment khớp trong API<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ API Error: " . $e->getMessage() . "<br>";
}

// 4. Test API call trực tiếp
echo "<h3>4. Test API Call:</h3>";
session_start();
if (isset($_SESSION['user_id'])) {
    echo "<button onclick='testAPI()' style='padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;'>Test API với $target_transaction</button><br>";
    echo "<div id='api-result' style='margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px;'></div>";
    
    echo "<script>
    function testAPI() {
        const resultDiv = document.getElementById('api-result');
        resultDiv.innerHTML = '⏳ Testing API call...';
        
        fetch('/api/check-payment.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                transaction_code: '$target_transaction'
            })
        })
        .then(response => response.json())
        .then(data => {
            let resultHTML = '<h4>API Response:</h4>';
            resultHTML += '<pre style=\"background: #fff; padding: 10px; border: 1px solid #ddd; border-radius: 4px; overflow-x: auto;\">' + JSON.stringify(data, null, 2) + '</pre>';
            
            if (data.success) {
                resultHTML += '<p style=\"color: green; font-weight: bold;\">✅ API returned SUCCESS</p>';
            } else if (data.already_processed) {
                resultHTML += '<p style=\"color: orange; font-weight: bold;\">⚠️ API returned ALREADY_PROCESSED</p>';
            } else if (data.expired) {
                resultHTML += '<p style=\"color: red; font-weight: bold;\">❌ API returned EXPIRED</p>';
            } else {
                resultHTML += '<p style=\"color: blue; font-weight: bold;\">ℹ️ API returned NO PAYMENT YET</p>';
            }
            
            resultDiv.innerHTML = resultHTML;
        })
        .catch(error => {
            resultDiv.innerHTML = '<p style=\"color: red;\">❌ Error: ' + error.message + '</p>';
        });
    }
    </script>";
} else {
    echo "❌ User chưa đăng nhập - không thể test API<br>";
}

// 5. Kiểm tra case sensitivity
echo "<h3>5. Case Sensitivity Check:</h3>";
$variations = [
    'earthBDEA4882',
    'EarthBDEA4882', 
    'EARTHBDEA4882',
    'earthbdea4882'
];

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr style='background: #f0f0f0;'>";
echo "<th>Variation</th><th>In Pending</th><th>In Completed</th><th>In API</th>";
echo "</tr>";

foreach ($variations as $var) {
    // Check pending
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM nlogin_qr_pending WHERE transaction_code = :code");
    $stmt->bindParam(':code', $var);
    $stmt->execute();
    $pending_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Check completed
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM nlogin_qr_transactions WHERE transaction_code = :code");
    $stmt->bindParam(':code', $var);
    $stmt->execute();
    $completed_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    // Check API (simplified)
    $api_found = false;
    if (isset($payments)) {
        foreach ($payments as $payment) {
            if (stripos($payment['content'], $var) !== false) {
                $api_found = true;
                break;
            }
        }
    }
    
    echo "<tr>";
    echo "<td><code>$var</code></td>";
    echo "<td>" . ($pending_count > 0 ? "✅ $pending_count" : "❌ 0") . "</td>";
    echo "<td>" . ($completed_count > 0 ? "✅ $completed_count" : "❌ 0") . "</td>";
    echo "<td>" . ($api_found ? "✅ Yes" : "❌ No") . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<hr>";
echo "<h3>🔍 Phân tích:</h3>";
echo "<ul>";
echo "<li>Nếu có <strong>completed transaction</strong> với cùng transaction_id → Đây là nguyên nhân</li>";
echo "<li>Nếu có <strong>case mismatch</strong> → Cần sửa logic case insensitive</li>";
echo "<li>Nếu <strong>API test failed</strong> → Có vấn đề với logic API</li>";
echo "<li>Kiểm tra <strong>thời gian</strong> của payment trong API</li>";
echo "</ul>";

echo "<p><strong>Sau khi debug xong, xóa file này!</strong></p>";
?>

<style>
table {
    border-collapse: collapse;
    margin: 10px 0;
    font-size: 11px;
    width: 100%;
}

table th, table td {
    border: 1px solid #ddd;
    padding: 4px;
    text-align: left;
}

table th {
    background-color: #f2f2f2;
}

code {
    background: #e9ecef;
    padding: 1px 3px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 10px;
}

h3 {
    color: #333;
    margin-top: 20px;
}

pre {
    font-size: 11px;
    max-height: 200px;
    overflow-y: auto;
}
</style>
