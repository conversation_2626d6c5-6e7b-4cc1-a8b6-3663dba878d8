<?php
// <PERSON><PERSON><PERSON> để fix conflict cho transaction cụ thể
require_once 'config/config.php';

echo "<h2>Fix Transaction Conflict: earthBDEA4882</h2>";

$target_transaction = 'earthBDEA4882';
$target_tid = 'mbbank-FT25153229021389';

try {
    // 1. Kiểm tra conflict hiện tại
    echo "<h3>1. Current Conflict Status:</h3>";

    // Check completed transactions với transaction_id này
    $stmt = $conn->prepare("
        SELECT * FROM nlogin_qr_transactions
        WHERE transaction_id = :tid
    ");
    $stmt->bindParam(':tid', $target_tid);
    $stmt->execute();
    $existing_completed = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if ($existing_completed) {
        echo "⚠️ Found " . count($existing_completed) . " existing completed transactions:<br>";

        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr style='background: #fff3cd;'>";
        echo "<th>ID</th><th>User</th><th>Transaction Code</th><th>Amount</th><th>Created</th><th>Action</th>";
        echo "</tr>";

        foreach ($existing_completed as $tx) {
            echo "<tr>";
            echo "<td>{$tx['id']}</td>";
            echo "<td>{$tx['user_id']}</td>";
            echo "<td><code>{$tx['transaction_code']}</code></td>";
            echo "<td>" . number_format($tx['amount']) . "</td>";
            echo "<td>{$tx['created_at']}</td>";
            echo "<td><button onclick='deleteCompleted({$tx['id']})' style='background: #dc3545; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer;'>Delete</button></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "✅ No existing completed transactions found<br>";
    }

    // 2. Kiểm tra pending transactions
    echo "<h3>2. Pending Transactions:</h3>";

    $stmt = $conn->prepare("
        SELECT * FROM nlogin_qr_pending
        WHERE transaction_code LIKE :code
        ORDER BY created_at DESC
    ");
    $like_code = '%' . $target_transaction . '%';
    $stmt->bindParam(':code', $like_code);
    $stmt->execute();
    $pending_txs = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if ($pending_txs) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>User</th><th>Transaction Code</th><th>Status</th><th>Processed</th><th>Created</th><th>Action</th>";
        echo "</tr>";

        foreach ($pending_txs as $tx) {
            $status_color = '';
            if ($tx['status'] == 0) $status_color = '#ffc107';
            elseif ($tx['status'] == 1) $status_color = '#28a745';
            elseif ($tx['status'] == 3) $status_color = '#dc3545';

            echo "<tr>";
            echo "<td>{$tx['id']}</td>";
            echo "<td>{$tx['user_id']}</td>";
            echo "<td><code>{$tx['transaction_code']}</code></td>";
            echo "<td style='background: $status_color; color: white;'>{$tx['status']}</td>";
            echo "<td>{$tx['processed']}</td>";
            echo "<td>{$tx['created_at']}</td>";
            echo "<td>";
            if ($tx['status'] == 3) {
                echo "<button onclick='resetPending({$tx['id']})' style='background: #28a745; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer;'>Reset to Pending</button>";
            }
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "❌ No pending transactions found<br>";
    }

    // 3. Test API payment
    echo "<h3>3. API Payment Check:</h3>";

    $payment_api_url = 'http://160.25.233.54:3000/payments';
    $response = file_get_contents($payment_api_url);

    if ($response) {
        $payments = json_decode($response, true);
        $found_payment = null;

        foreach ($payments as $payment) {
            if ($payment['transaction_id'] === $target_tid) {
                $found_payment = $payment;
                break;
            }
        }

        if ($found_payment) {
            echo "✅ Found payment in API:<br>";
            echo "<pre style='background: #d4edda; padding: 10px; border-radius: 5px;'>";
            echo json_encode($found_payment, JSON_PRETTY_PRINT);
            echo "</pre>";

            echo "<button onclick='processPayment()' style='background: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; font-weight: bold;'>Process This Payment</button>";
        } else {
            echo "❌ Payment not found in API<br>";
        }
    }

    // 4. Actions
    echo "<h3>4. Fix Actions:</h3>";
    echo "<button onclick='cleanupConflicts()' style='margin: 5px; padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;'>Cleanup All Conflicts</button>";
    echo "<button onclick='resetTransaction()' style='margin: 5px; padding: 8px 16px; background: #ffc107; color: black; border: none; border-radius: 4px; cursor: pointer;'>Reset Transaction</button>";

    echo "<script>
    function deleteCompleted(id) {
        if (confirm('Delete completed transaction ID ' + id + '?')) {
            fetch('/fix_transaction_conflict.php?action=delete_completed&id=' + id)
            .then(response => response.text())
            .then(data => {
                alert('Deleted: ' + data);
                location.reload();
            });
        }
    }

    function resetPending(id) {
        if (confirm('Reset pending transaction ID ' + id + ' to status 0?')) {
            fetch('/fix_transaction_conflict.php?action=reset_pending&id=' + id)
            .then(response => response.text())
            .then(data => {
                alert('Reset: ' + data);
                location.reload();
            });
        }
    }

    function processPayment() {
        if (confirm('Process the payment from API?')) {
            fetch('/fix_transaction_conflict.php?action=process_payment')
            .then(response => response.text())
            .then(data => {
                alert('Processed: ' + data);
                location.reload();
            });
        }
    }

    function cleanupConflicts() {
        if (confirm('Cleanup all conflicts for this transaction?')) {
            fetch('/fix_transaction_conflict.php?action=cleanup_all')
            .then(response => response.text())
            .then(data => {
                alert('Cleanup: ' + data);
                location.reload();
            });
        }
    }

    function resetTransaction() {
        if (confirm('Reset transaction to allow fresh processing?')) {
            fetch('/fix_transaction_conflict.php?action=reset_transaction')
            .then(response => response.text())
            .then(data => {
                alert('Reset: ' + data);
                location.reload();
            });
        }
    }
    </script>";

} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage();
}

// Handle AJAX actions
if (isset($_GET['action'])) {
    $action = $_GET['action'];

    switch ($action) {
        case 'delete_completed':
            $id = $_GET['id'];
            $stmt = $conn->prepare("DELETE FROM nlogin_qr_transactions WHERE id = :id");
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            echo "Deleted completed transaction ID $id";
            break;

        case 'reset_pending':
            $id = $_GET['id'];
            $stmt = $conn->prepare("UPDATE nlogin_qr_pending SET status = 0, processed = 0, check_count = 0 WHERE id = :id");
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            echo "Reset pending transaction ID $id to status 0";
            break;

        case 'process_payment':
            // Process the payment manually
            try {
                // 1. Delete old conflicting completed transaction
                $stmt = $conn->prepare("DELETE FROM nlogin_qr_transactions WHERE transaction_id = :tid AND transaction_code != :code");
                $stmt->bindParam(':tid', $target_tid);
                $stmt->bindParam(':code', $target_transaction);
                $stmt->execute();
                $deleted = $stmt->rowCount();

                // 2. Reset pending transaction to status 0
                $stmt = $conn->prepare("UPDATE nlogin_qr_pending SET status = 0, processed = 0, check_count = 0 WHERE transaction_code = :code");
                $stmt->bindParam(':code', $target_transaction);
                $stmt->execute();
                $reset = $stmt->rowCount();

                echo "Deleted $deleted conflicting transactions, reset $reset pending transactions. Now run cron to process payment.";
            } catch (Exception $e) {
                echo "Error: " . $e->getMessage();
            }
            break;

        case 'cleanup_all':
            // Delete all completed transactions with this transaction_id
            $stmt = $conn->prepare("DELETE FROM nlogin_qr_transactions WHERE transaction_id = :tid");
            $stmt->bindParam(':tid', $target_tid);
            $stmt->execute();
            $deleted = $stmt->rowCount();

            // Reset pending transactions
            $stmt = $conn->prepare("UPDATE nlogin_qr_pending SET status = 0, processed = 0, check_count = 0 WHERE transaction_code LIKE :code");
            $like_code = '%' . $target_transaction . '%';
            $stmt->bindParam(':code', $like_code);
            $stmt->execute();
            $reset = $stmt->rowCount();

            echo "Deleted $deleted completed, reset $reset pending transactions";
            break;

        case 'reset_transaction':
            // Reset everything for fresh start
            $stmt = $conn->prepare("DELETE FROM nlogin_qr_transactions WHERE transaction_id = :tid");
            $stmt->bindParam(':tid', $target_tid);
            $stmt->execute();

            $stmt = $conn->prepare("UPDATE nlogin_qr_pending SET status = 0, processed = 0, check_count = 0 WHERE transaction_code LIKE :code");
            $like_code = '%' . $target_transaction . '%';
            $stmt->bindParam(':code', $like_code);
            $stmt->execute();

            echo "Transaction reset for fresh processing";
            break;
    }
    exit;
}
?>

<style>
table {
    border-collapse: collapse;
    margin: 10px 0;
    font-size: 12px;
    width: 100%;
}

table th, table td {
    border: 1px solid #ddd;
    padding: 6px;
    text-align: left;
}

table th {
    background-color: #f2f2f2;
}

code {
    background: #e9ecef;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

h3 {
    color: #333;
    margin-top: 20px;
}

pre {
    font-size: 11px;
    max-height: 200px;
    overflow-y: auto;
}
</style>
