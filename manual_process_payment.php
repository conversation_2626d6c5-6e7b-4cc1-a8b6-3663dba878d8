<?php
// <PERSON><PERSON>t để xử lý payment thủ công
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<h2>Manual Process Payment: earthBDEA4882</h2>";

$target_transaction = 'earthBDEA4882';
$target_tid = 'mbbank-FT25153229021389';

try {
    // 1. Cleanup conflict trước
    echo "<h3>1. Cleanup Conflicts:</h3>";
    
    // Delete old conflicting completed transaction
    $stmt = $conn->prepare("DELETE FROM nlogin_qr_transactions WHERE transaction_id = :tid AND transaction_code != :code");
    $stmt->bindParam(':tid', $target_tid);
    $stmt->bindParam(':code', $target_transaction);
    $stmt->execute();
    $deleted = $stmt->rowCount();
    
    echo "✅ Deleted $deleted conflicting completed transactions<br>";
    
    // Reset pending transaction to status 0
    $stmt = $conn->prepare("UPDATE nlogin_qr_pending SET status = 0, processed = 0, check_count = 0 WHERE transaction_code = :code");
    $stmt->bindParam(':code', $target_transaction);
    $stmt->execute();
    $reset = $stmt->rowCount();
    
    echo "✅ Reset $reset pending transactions to status 0<br>";
    
    // 2. Lấy thông tin pending transaction
    echo "<h3>2. Get Pending Transaction:</h3>";
    
    $stmt = $conn->prepare("SELECT * FROM nlogin_qr_pending WHERE transaction_code = :code AND status = 0");
    $stmt->bindParam(':code', $target_transaction);
    $stmt->execute();
    $pending = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$pending) {
        echo "❌ No pending transaction found<br>";
        exit;
    }
    
    echo "✅ Found pending transaction: ID {$pending['id']}, User {$pending['username']}, Amount " . number_format($pending['amount']) . " VNĐ<br>";
    
    // 3. Lấy payment từ API
    echo "<h3>3. Get Payment from API:</h3>";
    
    $payment_api_url = 'http://160.25.233.54:3000/payments';
    $response = file_get_contents($payment_api_url);
    
    if (!$response) {
        echo "❌ Cannot connect to payment API<br>";
        exit;
    }
    
    $payments = json_decode($response, true);
    $found_payment = null;
    
    foreach ($payments as $payment) {
        if ($payment['transaction_id'] === $target_tid) {
            $found_payment = $payment;
            break;
        }
    }
    
    if (!$found_payment) {
        echo "❌ Payment not found in API<br>";
        exit;
    }
    
    echo "✅ Found payment in API: {$found_payment['amount']} VNĐ, Date: {$found_payment['date']}<br>";
    
    // 4. Kiểm tra match
    echo "<h3>4. Verify Match:</h3>";
    
    $amount_match = ($found_payment['amount'] == $pending['amount']);
    $content_match = (stripos($found_payment['content'], $target_transaction) !== false);
    
    echo "Amount match: " . ($amount_match ? "✅ Yes" : "❌ No") . " ({$found_payment['amount']} vs {$pending['amount']})<br>";
    echo "Content match: " . ($content_match ? "✅ Yes" : "❌ No") . "<br>";
    echo "Content: <code>" . htmlspecialchars($found_payment['content']) . "</code><br>";
    
    if (!$amount_match || !$content_match) {
        echo "❌ Payment does not match transaction<br>";
        exit;
    }
    
    // 5. Lưu vào completed transactions
    echo "<h3>5. Save to Completed Transactions:</h3>";
    
    $stmt = $conn->prepare("
        INSERT INTO nlogin_qr_transactions 
        (user_id, username, amount, server, transaction_code, transaction_id, created_at, processed_at) 
        VALUES (:user_id, :username, :amount, :server, :transaction_code, :transaction_id, :created_at, NOW())
    ");
    
    $stmt->bindParam(':user_id', $pending['user_id']);
    $stmt->bindParam(':username', $pending['username']);
    $stmt->bindParam(':amount', $pending['amount']);
    $stmt->bindParam(':server', $pending['server']);
    $stmt->bindParam(':transaction_code', $pending['transaction_code']);
    $stmt->bindParam(':transaction_id', $found_payment['transaction_id']);
    $stmt->bindParam(':created_at', $pending['created_at']);
    $stmt->execute();
    
    $completed_id = $conn->lastInsertId();
    echo "✅ Saved to completed transactions: ID $completed_id<br>";
    
    // 6. Cập nhật pending transaction thành success
    echo "<h3>6. Update Pending Transaction:</h3>";
    
    $stmt = $conn->prepare("
        UPDATE nlogin_qr_pending 
        SET status = 1, processed = 1, processed_at = NOW() 
        WHERE id = :id
    ");
    $stmt->bindParam(':id', $pending['id']);
    $stmt->execute();
    
    echo "✅ Updated pending transaction to status 1 (success)<br>";
    
    // 7. Gửi lệnh đến game server
    echo "<h3>7. Send Command to Game Server:</h3>";
    
    $command_result = sendGameCommand($pending['username'], $pending['amount'], $pending['server']);
    
    if ($command_result['success']) {
        echo "✅ Game command sent successfully<br>";
        echo "Command: <code>{$command_result['command']}</code><br>";
        echo "Response: {$command_result['response']}<br>";
    } else {
        echo "❌ Failed to send game command: {$command_result['error']}<br>";
    }
    
    // 8. Kết quả
    echo "<h3>8. Final Result:</h3>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 5px solid #28a745;'>";
    echo "<h4>✅ Payment Processed Successfully!</h4>";
    echo "<ul>";
    echo "<li><strong>User:</strong> {$pending['username']} (ID: {$pending['user_id']})</li>";
    echo "<li><strong>Amount:</strong> " . number_format($pending['amount']) . " VNĐ</li>";
    echo "<li><strong>Server:</strong> {$pending['server']}</li>";
    echo "<li><strong>Transaction Code:</strong> {$pending['transaction_code']}</li>";
    echo "<li><strong>Transaction ID:</strong> {$found_payment['transaction_id']}</li>";
    echo "<li><strong>Game Command:</strong> " . ($command_result['success'] ? "✅ Sent" : "❌ Failed") . "</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>User should see money in game account</li>";
    echo "<li>Transaction will appear in recharge history</li>";
    echo "<li>Delete this file after use</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
h3 {
    color: #333;
    margin-top: 20px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}

code {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
}

ul {
    margin: 10px 0;
}

li {
    margin: 5px 0;
}
</style>
