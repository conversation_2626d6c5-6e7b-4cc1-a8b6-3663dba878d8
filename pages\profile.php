<?php
// Get user data
$user = getUserData($_SESSION['user_id']);

// If user not found, redirect to login
if (!$user) {
    $_SESSION['message'] = 'Không tìm thấy thông tin tài khoản. Vui lòng đăng nhập lại.';
    $_SESSION['message_type'] = 'danger';
    header('Location: /logout');
    exit;
}

// Format dates
$created_at = isset($user['creation_date']) && !empty($user['creation_date']) ? new DateTime($user['creation_date']) : null;
$last_login = isset($user['last_seen']) && !empty($user['last_seen']) ? new DateTime($user['last_seen']) : null;

// Get login history
$loginHistory = getLoginHistory($_SESSION['user_id']);

// Lấy IP thực của người truy cập
function getRealUserIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        // Trường hợp qua proxy, lấy IP đầu tiên
        $ips = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
        return trim($ips[0]);
    } else {
        return $_SERVER['REMOTE_ADDR'] ?? '';
    }
}
$user_ip = getRealUserIP();

// Get password change history
$passwordHistory = getPasswordChangeHistory($_SESSION['user_id']);

// Process profile update form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];

    if ($action === 'update_profile') {
        // Không có dữ liệu cần cập nhật
        $_SESSION['message'] = 'Không có thông tin nào được thay đổi';
        $_SESSION['message_type'] = 'info';
    } elseif ($action === 'change_email') {
        // Change email
        $new_email = sanitize($_POST['new_email']);
        $password = $_POST['email_password'];

        // Verify password
        if ($nlogin->verifyPassword($_SESSION['user_id'], $password)) {
            // Update email
            $data = [
                'email' => $new_email
            ];

            $_SESSION['message'] = 'Email đã được cập nhật thành công';
            $_SESSION['message_type'] = 'success';
        } else {
            $_SESSION['message'] = 'Mật khẩu không chính xác';
            $_SESSION['message_type'] = 'danger';
        }
    } elseif ($action === 'change_password') {
        // Change password
        $current_password = $_POST['current_password'];
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_password'];

        // Validate passwords
        if ($new_password !== $confirm_password) {
            $_SESSION['message'] = 'Mật khẩu mới không khớp';
            $_SESSION['message_type'] = 'danger';
        } elseif (strlen($new_password) < 8) {
            $_SESSION['message'] = 'Mật khẩu mới phải có ít nhất 8 ký tự';
            $_SESSION['message_type'] = 'danger';
        } else {
            // Change password
            $result = changeUserPassword($_SESSION['user_id'], $current_password, $new_password);

            $_SESSION['message'] = $result['message'];
            $_SESSION['message_type'] = $result['status'] ? 'success' : 'danger';

            // Refresh password history if successful
            if ($result['status']) {
                $passwordHistory = getPasswordChangeHistory($_SESSION['user_id']);
            }
        }
    }

    // Không còn xử lý tải lên ảnh đại diện

    // Update profile if data is set
    if (isset($data) && !empty($data)) {
        if (updateUserProfile($_SESSION['user_id'], $data)) {
            $_SESSION['message'] = 'Cập nhật hồ sơ thành công';
            $_SESSION['message_type'] = 'success';

            // Refresh user data
            $user = getUserData($_SESSION['user_id']);
        } else {
            $_SESSION['message'] = 'Không thể cập nhật hồ sơ';
            $_SESSION['message_type'] = 'danger';
        }
    }
}
?>

<!-- Custom CSS for this page (reuse from account.php for consistency) -->
<style>
.account-container {
    margin-top: 2rem;
    margin-bottom: 3rem;
}
.profile-card {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.profile-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}
.profile-header {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    padding: 2rem 0;
    position: relative;
}
.avatar-container {
    position: relative;
    width: 150px;
    height: 150px;
    margin: 0 auto;
}
.minecraft-avatar {
    width: 150px;
    height: 150px;
    border: 5px solid #fff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease;
}
.minecraft-avatar:hover {
    transform: scale(1.05);
}
.profile-info {
    padding: 1.5rem;
    background-color: #fff;
}
.nav-pills .nav-link {
    border-radius: 10px;
    padding: 0.8rem 1.2rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}
.nav-pills .nav-link:hover {
    background-color: rgba(var(--primary-rgb), 0.1);
    transform: translateX(5px);
}
.nav-pills .nav-link.active {
    background-color: var(--primary);
    color: white;
    box-shadow: 0 5px 15px rgba(var(--primary-rgb), 0.3);
}
.nav-pills .nav-link i {
    margin-right: 10px;
    font-size: 1.1rem;
}
.info-card {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}
.info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}
.info-card .card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.2rem 1.5rem;
}
.info-card .card-header h6 {
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
}
.info-card .card-body {
    padding: 1.5rem;
}
.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.info-item:last-child {
    border-bottom: none;
}
.info-label {
    color: var(--gray-600);
    font-size: 0.95rem;
}
.info-value {
    font-weight: 600;
    color: var(--gray-800);
}
.welcome-card {
    border-radius: 15px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
}
.login-history-card {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
}
.login-history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.2rem 1.5rem;
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.login-history-header h6 {
    margin: 0;
    font-weight: 600;
    display: flex;
    align-items: center;
}
.toggle-btn {
    background: none;
    border: none;
    color: var(--primary);
    font-size: 0.9rem;
    cursor: pointer;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    transition: all 0.3s ease;
}
.toggle-btn:hover {
    background-color: rgba(var(--primary-rgb), 0.1);
}
.login-history-table {
    width: 100%;
}
.login-history-table th {
    background-color: rgba(var(--primary-rgb), 0.05);
    color: var(--gray-700);
    font-weight: 600;
    padding: 1rem;
    text-align: left;
}
.login-history-table td {
    padding: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}
.login-history-table tr:last-child td {
    border-bottom: none;
}
.login-history-table tr:hover {
    background-color: rgba(var(--primary-rgb), 0.03);
}
.edit-profile-btn {
    padding: 0.8rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    box-shadow: 0 5px 15px rgba(var(--primary-rgb), 0.3);
    transition: all 0.3s ease;
}
.edit-profile-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(var(--primary-rgb), 0.4);
}
@media (max-width: 767.98px) {
    .profile-card {
        margin-bottom: 2rem;
    }
}
</style>

<div class="container account-container">
    <div class="row">
        <!-- Sidebar / Profile Card -->
        <div class="col-lg-4 mb-4">
            <div class="profile-card">
                <div class="profile-header">
                    <div class="avatar-container">
                        <img src="<?php echo getMinecraftAvatarUrl($user['last_name'], 'avatar', 150); ?>" alt="Minecraft Avatar" class="minecraft-avatar rounded-circle">
                    </div>
                </div>
                <div class="profile-info">
                    <div class="text-center mb-4">
                        <h4 class="font-weight-bold mb-1">
                            <?php echo isset($user['last_name']) && $user['last_name'] ? htmlspecialchars($user['last_name']) : '-'; ?>
                        </h4>
                        <p class="text-muted">
                            <?php echo isset($user['email']) && $user['email'] ? htmlspecialchars($user['email']) : '<span class="text-danger">Chưa có email</span>'; ?>
                        </p>
                        <div class="mt-2">
                            <span class="badge badge-secondary">Mã định danh (UUID):
                                <?php
                                if (isset($user['unique_id']) && preg_match('/^[0-9a-fA-F\-]{32,36}$/', $user['unique_id'])) {
                                    echo htmlspecialchars($user['unique_id']);
                                } else {
                                    echo '<span class="text-danger">Thiếu hoặc sai UUID</span>';
                                }
                                ?>
                            </span>
                        </div>
                    </div>
                    <ul class="nav nav-pills flex-column">
                        <li class="nav-item">
                            <a href="/account" class="nav-link">
                                <i class="fas fa-tachometer-alt"></i> Trang chủ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/profile" class="nav-link active">
                                <i class="fas fa-user-edit"></i> Chỉnh sửa hồ sơ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/recharge-history" class="nav-link">
                                <i class="fas fa-credit-card"></i> Lịch sử nạp thẻ
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/logout" class="nav-link text-danger">
                                <i class="fas fa-sign-out-alt"></i> Đăng xuất
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <!-- Main Content -->
        <div class="col-lg-8">
            <div class="welcome-card">
                <div class="d-flex align-items-center">
                    <div class="mr-3">
                        <i class="fas fa-user-circle fa-3x text-primary"></i>
                    </div>
                    <div>
                        <h4 class="mb-1">Xin chào, <?php echo isset($user['last_name']) && $user['last_name'] ? $user['last_name'] : $user['unique_id']; ?>!</h4>
                        <p class="mb-0">Đây là trang quản lý tài khoản của bạn, nơi bạn có thể quản lý hồ sơ và cài đặt tài khoản.</p>
                    </div>
                </div>
            </div>
            <div class="row mb-4">
                <div class="col-md-6 mb-4">
                    <div class="info-card">
                        <div class="card-header">
                            <h6><i class="fas fa-id-card text-primary mr-2"></i> Thông tin cá nhân</h6>
                        </div>
                        <div class="card-body">
                            <div class="info-item">
                                <span class="info-label">Tên tài khoản</span>
                                <span class="info-value"><?php echo isset($user['last_name']) && $user['last_name'] ? $user['last_name'] : '-'; ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Mã định danh</span>
                                <span class="info-value">
                                    <?php
                                    if (isset($user['unique_id']) && preg_match('/^[0-9a-fA-F\-]{32,36}$/', $user['unique_id'])) {
                                        echo htmlspecialchars($user['unique_id']);
                                    } else {
                                        echo '<span class="text-danger">Thiếu hoặc sai UUID</span>';
                                    }
                                    ?>
                                </span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Email</span>
                                <span class="info-value"><?php echo isset($user['email']) && $user['email'] ? $user['email'] : '<span class="text-danger">Chưa cập nhật</span>'; ?></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="info-card">
                        <div class="card-header">
                            <h6><i class="fas fa-history text-primary mr-2"></i> Hoạt động tài khoản</h6>
                        </div>
                        <div class="card-body">
                            <div class="info-item">
                                <span class="info-label">Ngày tạo tài khoản</span>
                                <span class="info-value"><?php echo $created_at ? $created_at->format('d/m/Y H:i') : 'Không xác định'; ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Đăng nhập gần nhất</span>
                                <span class="info-value"><?php echo $last_login ? $last_login->format('d/m/Y H:i') : 'Chưa có'; ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Đổi Email -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h4><i class="fas fa-envelope"></i> Đổi Email</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="/profile" class="needs-validation" novalidate>
                        <input type="hidden" name="action" value="change_email">
                        <div class="form-group">
                            <label for="current_email">Email hiện tại</label>
                            <input type="email" class="form-control" id="current_email" value="<?php echo $user['email'] ?? ''; ?>" readonly>
                        </div>
                        <div class="form-group">
                            <label for="new_email">Email mới</label>
                            <input type="email" class="form-control" id="new_email" name="new_email" required>
                            <div class="invalid-feedback">
                                Vui lòng nhập email mới hợp lệ.
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="email_password">Mật khẩu</label>
                            <input type="password" class="form-control" id="email_password" name="email_password" required>
                            <div class="invalid-feedback">
                                Vui lòng nhập mật khẩu để xác nhận.
                            </div>
                            <small class="form-text text-muted">Nhập mật khẩu hiện tại để xác nhận thay đổi email.</small>
                        </div>
                        <button type="submit" class="btn btn-primary">Cập nhật Email</button>
                    </form>
                </div>
            </div>
            <!-- Đổi Mật Khẩu -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h4><i class="fas fa-key"></i> Đổi Mật Khẩu</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="/profile" class="needs-validation" novalidate>
                        <input type="hidden" name="action" value="change_password">
                        <div class="form-group">
                            <label for="current_password">Mật khẩu hiện tại</label>
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                            <div class="invalid-feedback">
                                Vui lòng nhập mật khẩu hiện tại.
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="new_password">Mật khẩu mới</label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required>
                            <div class="invalid-feedback">
                                Mật khẩu mới phải có ít nhất 8 ký tự.
                            </div>
                            <div class="progress mt-2">
                                <div id="password-strength" class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="confirm_password">Nhập lại mật khẩu mới</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <div class="invalid-feedback">
                                Mật khẩu không khớp.
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">Đổi Mật Khẩu</button>
                    </form>
                </div>
            </div>
            <!-- Lịch Sử Đăng Nhập -->
            <!-- Login History -->
            <div class="login-history-card mb-4">
                <div class="login-history-header">
                    <h6><i class="fas fa-history text-primary mr-2"></i> Lịch sử đăng nhập</h6>
                    <button class="toggle-btn" type="button" data-toggle="collapse" data-target="#loginHistoryCollapse" aria-expanded="true" aria-controls="loginHistoryCollapse">
                        <i class="fas fa-chevron-up mr-1"></i> Ẩn / Hiện
                    </button>
                </div>
                <div class="collapse show" id="loginHistoryCollapse">
                    <div class="table-responsive">
                        <table class="login-history-table">
                            <thead>
                                <tr>
                                    <th>Thời gian</th>
                                    <th>IP</th>
                                    <th>Quốc gia</th>
                                    <th>Nguồn</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                // Lấy lịch sử đăng nhập
                                $loginHistory = $nlogin->getLoginHistory($_SESSION['user_id']);

                                if (empty($loginHistory)) {
                                    echo '<tr><td colspan="4" class="text-center py-4">Chưa có lịch sử đăng nhập</td></tr>';
                                } else {
                                    foreach ($loginHistory as $login) {
                                        $loginTime = new DateTime($login['login_time']);
                                        echo '<tr>';
                                        echo '<td>' . $loginTime->format('H:i d-m-Y') . '</td>';
                                        echo '<td>' . htmlspecialchars($login['ip_address']) . '</td>';
                                        echo '<td>' . htmlspecialchars($login['country_code']) . '</td>';
                                        echo '<td>' . htmlspecialchars($login['source']) . '</td>';
                                        echo '</tr>';
                                    }
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Lịch Sử Đổi Mật Khẩu -->
            <div class="login-history-card mb-4">
                <div class="login-history-header">
                    <h6><i class="fas fa-key text-primary mr-2"></i> Lịch sử đổi mật khẩu</h6>
                    <button class="toggle-btn" type="button" data-toggle="collapse" data-target="#passwordHistoryCollapse" aria-expanded="true" aria-controls="passwordHistoryCollapse">
                        <i class="fas fa-chevron-up mr-1"></i> Ẩn / Hiện
                    </button>
                </div>
                <div class="collapse show" id="passwordHistoryCollapse">
                    <div class="table-responsive">
                        <table class="login-history-table">
                            <thead>
                                <tr>
                                    <th>Thời gian</th>
                                    <th>IP</th>
                                    <th>Quốc gia</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                if (empty($passwordHistory)) {
                                    echo '<tr><td colspan="3" class="text-center py-4">Tài khoản chưa từng thay đổi mật khẩu.</td></tr>';
                                } else {
                                    foreach ($passwordHistory as $change) {
                                        echo '<tr>';
                                        echo '<td>' . date('H:i d-m-Y', strtotime($change['date'])) . '</td>';
                                        echo '<td>' . htmlspecialchars($change['ip']) . '</td>';
                                        echo '<td>' . htmlspecialchars($change['country'] ?? '-') . '</td>';
                                        echo '</tr>';
                                    }
                                }
                                ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#loginHistoryCollapse, #passwordHistoryCollapse').on('show.bs.collapse', function () {
        $(this).prev().find('.toggle-btn i').removeClass('fa-chevron-down').addClass('fa-chevron-up');
    });
    $('#loginHistoryCollapse, #passwordHistoryCollapse').on('hide.bs.collapse', function () {
        $(this).prev().find('.toggle-btn i').removeClass('fa-chevron-up').addClass('fa-chevron-down');
    });
});
</script>
