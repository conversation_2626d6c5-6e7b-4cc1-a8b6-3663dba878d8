/**
 * Minecraft Skin Viewer Enhancement
 * Adds interactive 3D skin viewing with drag-to-rotate functionality
 */

// Biến toàn cục để theo dõi trạng thái xoay
let skinRotation = {
    currentAngle: 0,
    isDragging: false,
    startX: 0,
    startAngle: 0
};

// Các hướng xem có sẵn
const viewDirections = ['left', 'front', 'right', 'back'];

document.addEventListener('DOMContentLoaded', function() {
    // Tìm tất cả các container skin Minecraft
    const skinContainers = document.querySelectorAll('.minecraft-skin-container');

    // Thêm hiệu ứng tên người chơi
    addUsernameEffects();

    // Thiết lập tính năng kéo để xoay
    setupDragToRotate();

    // Thiết lập các nút điều khiển (sẽ bị ẩn do showControls = false)
    setupControlButtons();

    // Thiết lập hướng mặc định
    const skinImage = document.getElementById('skin-image');
    if (skinImage) {
        const username = skinImage.getAttribute('data-username');
        if (username) {
            // Đặt hướng mặc định là 'right' để nhìn thấy mặt bên
            skinImage.src = `https://mc-heads.net/body/${username}/right`;

            // Đặt góc xoay ban đầu
            skinRotation.currentAngle = 90; // Góc tương ứng với hướng 'right'
        }
    }
});

/**
 * Thiết lập tính năng kéo để xoay skin
 */
function setupDragToRotate() {
    const skinViewer = document.getElementById('skin-viewer');
    if (!skinViewer) return;

    const skinImage = document.getElementById('skin-image');
    if (!skinImage) return;

    const username = skinImage.getAttribute('data-username');

    // Thêm thông báo hướng dẫn
    const dragHint = document.createElement('div');
    dragHint.className = 'drag-hint';
    dragHint.innerHTML = '<i class="fas fa-arrows-alt-h"></i> Kéo để xoay';
    dragHint.style.position = 'absolute';
    dragHint.style.bottom = '10px';
    dragHint.style.left = '50%';
    dragHint.style.transform = 'translateX(-50%)';
    dragHint.style.color = 'white';
    dragHint.style.backgroundColor = 'rgba(0,0,0,0.5)';
    dragHint.style.padding = '5px 10px';
    dragHint.style.borderRadius = '15px';
    dragHint.style.fontSize = '12px';
    dragHint.style.opacity = '0.8';
    dragHint.style.zIndex = '5';
    skinViewer.appendChild(dragHint);

    // Hiển thị thông báo trong 3 giây rồi ẩn đi
    setTimeout(() => {
        dragHint.style.opacity = '0';
        dragHint.style.transition = 'opacity 1s ease';
    }, 3000);

    // Xử lý sự kiện chuột
    skinViewer.addEventListener('mousedown', function(e) {
        e.preventDefault(); // Ngăn chặn hành vi mặc định
        skinRotation.isDragging = true;
        skinRotation.startX = e.clientX;
        skinRotation.startAngle = skinRotation.currentAngle;

        // Thay đổi con trỏ
        skinViewer.style.cursor = 'grabbing';

        // Ẩn thông báo hướng dẫn khi người dùng bắt đầu kéo
        if (dragHint) {
            dragHint.style.opacity = '0';
        }
    });

    document.addEventListener('mousemove', function(e) {
        if (!skinRotation.isDragging) return;

        // Tính toán góc xoay dựa trên khoảng cách kéo
        const deltaX = e.clientX - skinRotation.startX;
        const sensitivity = 1.0; // Tăng độ nhạy

        // Cập nhật góc xoay
        skinRotation.currentAngle = skinRotation.startAngle + deltaX * sensitivity;

        // Xác định hướng xem dựa trên góc xoay
        updateSkinDirection(username);
    });

    document.addEventListener('mouseup', function() {
        if (!skinRotation.isDragging) return;

        skinRotation.isDragging = false;

        // Khôi phục con trỏ
        if (skinViewer) {
            skinViewer.style.cursor = 'grab';
        }

        // Căn chỉnh về hướng gần nhất
        snapToNearestDirection(username);
    });

    // Xử lý sự kiện cảm ứng cho thiết bị di động
    skinViewer.addEventListener('touchstart', function(e) {
        if (e.touches.length === 1) {
            skinRotation.isDragging = true;
            skinRotation.startX = e.touches[0].clientX;
            skinRotation.startAngle = skinRotation.currentAngle;

            // Ẩn thông báo hướng dẫn khi người dùng bắt đầu kéo
            if (dragHint) {
                dragHint.style.opacity = '0';
            }

            e.preventDefault();
        }
    });

    document.addEventListener('touchmove', function(e) {
        if (!skinRotation.isDragging || e.touches.length !== 1) return;

        const deltaX = e.touches[0].clientX - skinRotation.startX;
        const sensitivity = 1.0; // Tăng độ nhạy

        skinRotation.currentAngle = skinRotation.startAngle + deltaX * sensitivity;

        updateSkinDirection(username);
        e.preventDefault();
    });

    document.addEventListener('touchend', function() {
        if (skinRotation.isDragging) {
            skinRotation.isDragging = false;
            snapToNearestDirection(username);
        }
    });
}

/**
 * Cập nhật hướng xem của skin dựa trên góc xoay hiện tại
 */
function updateSkinDirection(username) {
    const skinImage = document.getElementById('skin-image');
    if (!skinImage) return;

    // Chuẩn hóa góc về khoảng 0-360
    let normalizedAngle = skinRotation.currentAngle % 360;
    if (normalizedAngle < 0) normalizedAngle += 360;

    // Xác định hướng xem dựa trên góc
    let direction;
    if (normalizedAngle >= 315 || normalizedAngle < 45) {
        direction = 'front';
    } else if (normalizedAngle >= 45 && normalizedAngle < 135) {
        direction = 'right';
    } else if (normalizedAngle >= 135 && normalizedAngle < 225) {
        direction = 'back';
    } else {
        direction = 'left';
    }

    // Chỉ cập nhật URL hình ảnh nếu hướng thay đổi để tránh nạp lại hình ảnh liên tục
    if (skinImage.getAttribute('data-current-direction') !== direction) {
        // Lưu hướng hiện tại
        skinImage.setAttribute('data-current-direction', direction);

        // Cập nhật URL hình ảnh
        skinImage.src = `https://mc-heads.net/body/${username}/${direction}`;

        // Thêm hiệu ứng xoay nhẹ khi chuyển hướng
        skinImage.style.transition = 'transform 0.2s ease';
        skinImage.style.transform = 'scale(1.5) rotate(' + (normalizedAngle / 10) + 'deg)';

        // Khôi phục hiệu ứng float sau khi chuyển hướng
        setTimeout(() => {
            skinImage.style.transition = 'transform 0.3s ease';
        }, 200);
    }
}

/**
 * Căn chỉnh về hướng xem gần nhất
 */
function snapToNearestDirection(username) {
    // Chuẩn hóa góc về khoảng 0-360
    let normalizedAngle = skinRotation.currentAngle % 360;
    if (normalizedAngle < 0) normalizedAngle += 360;

    // Tìm hướng gần nhất
    const directionAngles = [0, 90, 180, 270, 360];
    let closestAngle = directionAngles.reduce(function(prev, curr) {
        return (Math.abs(curr - normalizedAngle) < Math.abs(prev - normalizedAngle) ? curr : prev);
    });

    // Nếu góc gần nhất là 360, đặt lại thành 0
    if (closestAngle === 360) closestAngle = 0;

    // Cập nhật góc hiện tại
    skinRotation.currentAngle = closestAngle;

    // Cập nhật hướng xem
    updateSkinDirection(username);
}

/**
 * Thiết lập các nút điều khiển
 */
function setupControlButtons() {
    // Kiểm tra biến showControls để quyết định có hiển thị nút điều khiển hay không
    if (typeof showControls !== 'undefined' && showControls === false) {
        // Ẩn tất cả các nút điều khiển
        const controlsContainers = document.querySelectorAll('.minecraft-skin-controls');
        controlsContainers.forEach(function(container) {
            container.style.display = 'none';
        });
        return;
    }

    // Nếu showControls = true hoặc không được định nghĩa, hiển thị các nút điều khiển
    const buttons = document.querySelectorAll('.minecraft-skin-control-btn');

    buttons.forEach(function(button) {
        button.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(255, 255, 255, 0.4)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
        });
    });
}

/**
 * Xoay skin theo hướng chỉ định
 */
function rotateSkin(direction) {
    const skinImage = document.getElementById('skin-image');
    if (!skinImage) return;

    const username = skinImage.getAttribute('data-username');

    if (direction === 'left') {
        skinRotation.currentAngle -= 90;
    } else if (direction === 'right') {
        skinRotation.currentAngle += 90;
    } else if (direction === 'reset') {
        skinRotation.currentAngle = 0;
    }

    // Cập nhật hướng xem
    updateSkinDirection(username);
}

/**
 * Thêm hiệu ứng cho tên người chơi
 */
function addUsernameEffects() {
    const usernames = document.querySelectorAll('.minecraft-skin-username');

    usernames.forEach(function(username) {
        // Thêm hiệu ứng hover
        username.addEventListener('mouseenter', function() {
            this.style.textShadow = '2px 2px 0 #3498db';
        });

        username.addEventListener('mouseleave', function() {
            this.style.textShadow = '2px 2px 0 #000';
        });
    });
}

/**
 * Thêm hiệu ứng nền động cho skin container
 */
function addDynamicBackground() {
    const containers = document.querySelectorAll('.minecraft-skin-container');

    containers.forEach(function(container) {
        // Tạo canvas cho hiệu ứng nền
        const canvas = document.createElement('canvas');
        canvas.className = 'minecraft-background-canvas';
        canvas.style.position = 'absolute';
        canvas.style.top = '0';
        canvas.style.left = '0';
        canvas.style.width = '100%';
        canvas.style.height = '100%';
        canvas.style.zIndex = '-1';
        canvas.style.opacity = '0.2';
        canvas.style.borderRadius = '10px';

        // Thêm canvas vào container
        container.style.position = 'relative';
        container.insertBefore(canvas, container.firstChild);

        // Thiết lập kích thước canvas
        canvas.width = container.offsetWidth;
        canvas.height = container.offsetHeight;

        // Vẽ hiệu ứng nền
        const ctx = canvas.getContext('2d');

        // Tạo gradient nền
        const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
        gradient.addColorStop(0, '#001f3f');
        gradient.addColorStop(1, '#000000');

        // Vẽ nền
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // Thêm các ngôi sao
        for (let i = 0; i < 50; i++) {
            const x = Math.random() * canvas.width;
            const y = Math.random() * canvas.height;
            const radius = Math.random() * 1.5;
            const opacity = Math.random() * 0.8 + 0.2;

            ctx.beginPath();
            ctx.arc(x, y, radius, 0, Math.PI * 2);
            ctx.fillStyle = `rgba(255, 255, 255, ${opacity})`;
            ctx.fill();
        }

        // Thêm hiệu ứng sao băng
        setInterval(function() {
            addShootingStar(ctx, canvas.width, canvas.height);
        }, 2000);
    });
}

/**
 * Thêm hiệu ứng sao băng
 */
function addShootingStar(ctx, width, height) {
    // Vị trí bắt đầu
    const startX = Math.random() * width;
    const startY = Math.random() * height / 3;

    // Vị trí kết thúc
    const endX = startX + Math.random() * 100 - 50;
    const endY = startY + Math.random() * 100 + 50;

    // Vẽ sao băng
    const duration = 1000; // 1 giây
    const startTime = Date.now();

    const drawStar = function() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // Xóa vết vẽ cũ
        ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
        ctx.fillRect(0, 0, width, height);

        // Vẽ sao băng
        const x = startX + (endX - startX) * progress;
        const y = startY + (endY - startY) * progress;

        ctx.beginPath();
        ctx.arc(x, y, 2, 0, Math.PI * 2);
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.fill();

        // Vẽ đuôi sao băng
        ctx.beginPath();
        ctx.moveTo(x, y);
        ctx.lineTo(startX + (x - startX) * 0.3, startY + (y - startY) * 0.3);
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.4)';
        ctx.lineWidth = 1;
        ctx.stroke();

        // Tiếp tục vẽ nếu chưa hoàn thành
        if (progress < 1) {
            requestAnimationFrame(drawStar);
        }
    };

    drawStar();
}
