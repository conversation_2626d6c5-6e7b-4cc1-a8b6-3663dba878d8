// Form validation
document.addEventListener('DOMContentLoaded', function() {
    // Get all forms with the class 'needs-validation'
    var forms = document.querySelectorAll('.needs-validation');

    // Loop over them and prevent submission
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }

            form.classList.add('was-validated');
        }, false);
    });

    // Password strength meter
    var passwordInput = document.getElementById('password');
    var passwordStrength = document.getElementById('password-strength');

    if (passwordInput && passwordStrength) {
        passwordInput.addEventListener('input', function() {
            var strength = 0;
            var password = passwordInput.value;

            if (password.length >= 8) strength += 1;
            if (password.match(/[a-z]+/)) strength += 1;
            if (password.match(/[A-Z]+/)) strength += 1;
            if (password.match(/[0-9]+/)) strength += 1;
            if (password.match(/[^a-zA-Z0-9]+/)) strength += 1;

            switch (strength) {
                case 0:
                case 1:
                    passwordStrength.className = 'progress-bar bg-danger';
                    passwordStrength.style.width = '20%';
                    passwordStrength.textContent = 'Very Weak';
                    break;
                case 2:
                    passwordStrength.className = 'progress-bar bg-warning';
                    passwordStrength.style.width = '40%';
                    passwordStrength.textContent = 'Weak';
                    break;
                case 3:
                    passwordStrength.className = 'progress-bar bg-info';
                    passwordStrength.style.width = '60%';
                    passwordStrength.textContent = 'Medium';
                    break;
                case 4:
                    passwordStrength.className = 'progress-bar bg-primary';
                    passwordStrength.style.width = '80%';
                    passwordStrength.textContent = 'Strong';
                    break;
                case 5:
                    passwordStrength.className = 'progress-bar bg-success';
                    passwordStrength.style.width = '100%';
                    passwordStrength.textContent = 'Very Strong';
                    break;
            }
        });
    }

    // Profile image preview
    var profileImageInput = document.getElementById('profile_image');
    var profileImagePreview = document.getElementById('profile-image-preview');

    if (profileImageInput && profileImagePreview) {
        profileImageInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                var reader = new FileReader();

                reader.onload = function(e) {
                    profileImagePreview.src = e.target.result;
                }

                reader.readAsDataURL(this.files[0]);
            }
        });
    }
});

/**
 * Thay đổi kiểu hiển thị skin Minecraft
 * @param {string} type - Loại hiển thị (bust, cube, body, armor/body)
 */
function changeSkinView(type) {
    // Lấy tên người dùng từ tiêu đề trang
    var username = document.querySelector('h4, h5').textContent.trim();

    // Lấy tất cả các hình ảnh skin
    var skinImages = document.querySelectorAll('.profile-image');

    // Cập nhật URL cho mỗi hình ảnh
    skinImages.forEach(function(img) {
        var baseUrl = 'https://mc-heads.net/' + type + '/' + username;

        // Thêm kích thước nếu là kiểu avatar
        if (type === 'avatar') {
            baseUrl += '/150';
        }

        img.src = baseUrl;

        // Cập nhật alt text
        img.alt = 'Minecraft ' + type + ' Skin';
    });

    // Cập nhật các nút để hiển thị nút đang được chọn
    var buttons = document.querySelectorAll('.btn-group-sm .btn');
    buttons.forEach(function(btn) {
        btn.classList.remove('active');
        if (btn.getAttribute('onclick').includes(type)) {
            btn.classList.add('active');
        }
    });
}