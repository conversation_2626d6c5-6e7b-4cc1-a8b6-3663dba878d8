<?php
// Debug script để kiểm tra mapping tên người dùng
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<h2>Debug User Mapping</h2>";

// Kiểm tra kết nối
if (!$conn) {
    die("❌ Không thể kết nối database chính!");
}

if (!$card_conn) {
    die("❌ Không thể kết nối database lịch sử nạp thẻ!");
}

echo "✅ Kết nối database thành công!<br><br>";

// Form để test user cụ thể
$test_user_id = isset($_GET['user_id']) ? (int)$_GET['user_id'] : 1;

echo "<form method='GET' style='margin-bottom: 20px;'>";
echo "User ID để test: <input type='number' name='user_id' value='" . $test_user_id . "' min='1'>";
echo " <input type='submit' value='Debug'>";
echo "</form>";

// Debug user cụ thể
if ($test_user_id) {
    echo "<h3>Debug User ID: " . $test_user_id . "</h3>";
    
    $debug_info = debugUserMapping($test_user_id);
    
    if (isset($debug_info['error'])) {
        echo "❌ Lỗi: " . $debug_info['error'] . "<br>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
        echo "<tr><th>Thông tin</th><th>Giá trị</th></tr>";
        
        if ($debug_info['nlogin_data']) {
            echo "<tr><td>Last Name (nlogin)</td><td>" . htmlspecialchars($debug_info['nlogin_data']['last_name'] ?? 'NULL') . "</td></tr>";
            echo "<tr><td>Unique ID (nlogin)</td><td>" . htmlspecialchars($debug_info['nlogin_data']['unique_id'] ?? 'NULL') . "</td></tr>";
        }
        
        echo "<tr><td>UUID tìm thấy</td><td>" . ($debug_info['found_uuid'] ? $debug_info['found_uuid'] : 'Không tìm thấy') . "</td></tr>";
        echo "<tr><td>Player Name tìm thấy</td><td>" . ($debug_info['found_player_name'] ? htmlspecialchars($debug_info['found_player_name']) : 'Không tìm thấy') . "</td></tr>";
        echo "</table>";
        
        echo "<h4>Chi tiết quá trình tìm kiếm:</h4>";
        echo "<ul>";
        foreach ($debug_info['search_attempts'] as $attempt) {
            echo "<li>" . htmlspecialchars($attempt) . "</li>";
        }
        echo "</ul>";
        
        // Test lấy lịch sử nạp thẻ
        if ($debug_info['found_uuid']) {
            echo "<h4>Test lịch sử nạp thẻ:</h4>";
            $history = getCardRechargeHistory($debug_info['found_uuid'], 5, 0);
            $count = getCardRechargeHistoryCount($debug_info['found_uuid']);
            
            echo "📊 Tổng số giao dịch: " . $count . "<br>";
            
            if (!empty($history)) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-top: 10px;'>";
                echo "<tr><th>Thời gian</th><th>Loại thẻ</th><th>Mệnh giá</th><th>Trạng thái</th><th>Server</th></tr>";
                
                foreach ($history as $record) {
                    echo "<tr>";
                    echo "<td>" . $record['formatted_time'] . "</td>";
                    echo "<td>" . $record['type_name'] . "</td>";
                    echo "<td>" . $record['formatted_price'] . "</td>";
                    echo "<td>" . $record['status_text'] . "</td>";
                    echo "<td>" . ($record['server'] ?? 'N/A') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "ℹ️ Không có giao dịch nạp thẻ<br>";
            }
        }
    }
}

echo "<hr>";

// Hiển thị danh sách user để test
echo "<h3>Danh sách User để test:</h3>";

try {
    $stmt = $conn->query("SELECT ai, last_name, unique_id FROM nlogin WHERE last_name IS NOT NULL AND last_name != '' ORDER BY ai LIMIT 20");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>User ID</th><th>Last Name</th><th>Unique ID</th><th>Action</th></tr>";
    
    foreach ($users as $user) {
        echo "<tr>";
        echo "<td>" . $user['ai'] . "</td>";
        echo "<td>" . htmlspecialchars($user['last_name']) . "</td>";
        echo "<td>" . htmlspecialchars($user['unique_id'] ?? 'NULL') . "</td>";
        echo "<td><a href='?user_id=" . $user['ai'] . "'>Debug</a></td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch(PDOException $e) {
    echo "❌ Lỗi lấy danh sách user: " . $e->getMessage();
}

echo "<hr>";

// Hiển thị một số player từ dotman_player_info
echo "<h3>Mẫu Player Names trong dotman_player_info:</h3>";

try {
    $stmt = $card_conn->query("SELECT uuid, name FROM dotman_player_info ORDER BY last_updated DESC LIMIT 20");
    $players = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>UUID</th><th>Player Name</th><th>Có prefix _?</th></tr>";
    
    foreach ($players as $player) {
        $has_prefix = strpos($player['name'], '_') === 0 ? 'Có' : 'Không';
        echo "<tr>";
        echo "<td>" . substr($player['uuid'], 0, 8) . "...</td>";
        echo "<td>" . htmlspecialchars($player['name']) . "</td>";
        echo "<td>" . $has_prefix . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch(PDOException $e) {
    echo "❌ Lỗi lấy danh sách player: " . $e->getMessage();
}

echo "<br><hr>";
echo "<p><strong>Lưu ý:</strong> Sau khi debug xong, hãy xóa file này để bảo mật!</p>";
?>
