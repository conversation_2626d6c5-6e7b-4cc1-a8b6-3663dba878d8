<?php
// Simple API test
header('Content-Type: application/json');

try {
    echo json_encode([
        'success' => true,
        'message' => 'API test successful',
        'timestamp' => time(),
        'php_version' => PHP_VERSION,
        'server_time' => date('Y-m-d H:i:s')
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'API test failed: ' . $e->getMessage()
    ]);
}
?>
