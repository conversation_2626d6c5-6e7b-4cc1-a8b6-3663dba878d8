<?php
// Check if reset email is set in session
if (!isset($_SESSION['reset_email'])) {
    $_SESSION['message'] = 'Phiên làm việc đã hết hạn. Vui lòng yêu cầu đặt lại mật khẩu lại.';
    $_SESSION['message_type'] = 'danger';
    header('Location: /forgot-password');
    exit;
}

$email = $_SESSION['reset_email'];

// Process OTP verification form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get OTP from form
    $otp = '';
    for ($i = 1; $i <= 6; $i++) {
        $otp .= isset($_POST['otp' . $i]) ? $_POST['otp' . $i] : '';
    }

    // Xác minh OTP

    // Validate OTP
    if (strlen($otp) !== 6 || !ctype_digit($otp)) {
        $_SESSION['message'] = '<PERSON><PERSON> lòng nhập đ<PERSON>y đủ mã xác minh 6 số';
        $_SESSION['message_type'] = 'danger';
    } else {
        // Verify OTP
        try {
            global $conn;

            // Find token with matching OTP
            $stmt = $conn->prepare("SELECT `key`, value FROM nlogin_data WHERE `key` LIKE 'password_reset_token_%'");
            $stmt->execute();

            // Reset statement for reuse
            $stmt = $conn->prepare("SELECT `key`, value FROM nlogin_data WHERE `key` LIKE 'password_reset_token_%'");
            $stmt->execute();

            $found = false;
            $token = '';
            $tokenKey = '';

            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $tokenData = json_decode($row['value'], true);

                if (isset($tokenData['otp']) && $tokenData['otp'] === $otp) {
                    // Get user_id from token data or from key
                    $user_id = null;
                    // Tìm thấy OTP khớp

                    if (isset($tokenData['user_id'])) {
                        $user_id = $tokenData['user_id'];
                    } else {
                        // Extract user_id from key
                        $keyParts = explode('_', $row['key']);
                        if (count($keyParts) >= 3) {
                            $user_id = $keyParts[2];
                        }
                    }

                    if ($user_id) {
                        $userStmt = $conn->prepare("SELECT email FROM nlogin WHERE ai = :id");
                        $userStmt->bindParam(':id', $user_id);
                        $userStmt->execute();

                        if ($userStmt->rowCount() > 0) {
                            $user = $userStmt->fetch(PDO::FETCH_ASSOC);

                            if ($user['email'] === $email) {
                                // Check if token is expired
                                if (strtotime($tokenData['expires']) < time()) {
                                    $_SESSION['message'] = 'Mã xác minh đã hết hạn. Vui lòng yêu cầu mã mới.';
                                    $_SESSION['message_type'] = 'danger';
                                    break;
                                }

                                // Check if token has been used
                                if (isset($tokenData['used']) && $tokenData['used']) {
                                    $_SESSION['message'] = 'Mã xác minh đã được sử dụng. Vui lòng yêu cầu mã mới.';
                                    $_SESSION['message_type'] = 'danger';
                                    break;
                                }

                                // Mark token as verified
                                $tokenData['verified'] = true;
                                $token = $tokenData['token'];
                                $tokenKey = $row['key'];

                                // Đánh dấu token đã được xác minh

                                // Update token data
                                $updateStmt = $conn->prepare("UPDATE nlogin_data SET value = :value WHERE `key` = :key");
                                $updateStmt->bindParam(':value', json_encode($tokenData));
                                $updateStmt->bindParam(':key', $tokenKey);
                                $updateStmt->execute();

                                $found = true;
                                break;
                            }
                        }
                    }
                }
            }

            if ($found) {
                // Redirect to reset password page with token
                $_SESSION['message'] = 'Xác minh thành công. Vui lòng đặt lại mật khẩu mới.';
                $_SESSION['message_type'] = 'success';
                header('Location: /reset-password?token=' . $token);
                exit;
            } else {
                $_SESSION['message'] = 'Mã xác minh không chính xác. Vui lòng thử lại.';
                $_SESSION['message_type'] = 'danger';
            }
        } catch(PDOException $e) {
            error_log("DEBUG - Error verifying OTP: " . $e->getMessage());
            $_SESSION['message'] = 'Đã xảy ra lỗi. Vui lòng thử lại sau.';
            $_SESSION['message_type'] = 'danger';
        }
    }
}
?>

<div class="row">
    <div class="col-md-6 offset-md-3">
        <div class="auth-form">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-key"></i> Xác minh mã OTP</h4>
                </div>
                <div class="card-body">
                    <p class="text-center">Vui lòng nhập mã xác minh 6 số đã được gửi đến email <strong><?php echo htmlspecialchars($email); ?></strong></p>

                    <?php if (isset($_SESSION['debug_otp'])): ?>
                    <div class="alert alert-info">
                        <p class="text-center"><strong>Mã OTP:</strong> <?php echo $_SESSION['debug_otp']; ?></p>
                        <p class="text-center text-muted"><small>Hiển thị mã OTP trên trang web chỉ dành cho mục đích kiểm thử.</small></p>
                    </div>
                    <?php endif; ?>

                    <form method="POST" action="/verify-otp" class="needs-validation" novalidate>
                        <div class="form-group">
                            <label for="otp">Mã xác minh</label>
                            <div class="d-flex justify-content-between mb-3">
                                <?php for ($i = 1; $i <= 6; $i++): ?>
                                <input type="text" class="form-control text-center otp-input" id="otp<?php echo $i; ?>" name="otp<?php echo $i; ?>" maxlength="1" pattern="[0-9]" required style="width: 50px;">
                                <?php endfor; ?>
                            </div>
                            <div class="invalid-feedback">
                                Vui lòng nhập đầy đủ mã xác minh 6 số.
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary btn-block">Xác minh</button>

                        <div class="form-text text-center mt-3">
                            <p>Không nhận được mã? <a href="/forgot-password">Gửi lại mã</a></p>
                            <p><a href="/login">Quay lại đăng nhập</a></p>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-focus next input when typing
document.addEventListener('DOMContentLoaded', function() {
    const otpInputs = document.querySelectorAll('.otp-input');

    otpInputs.forEach((input, index) => {
        input.addEventListener('input', function() {
            if (this.value.length === 1) {
                if (index < otpInputs.length - 1) {
                    otpInputs[index + 1].focus();
                }
            }
        });

        input.addEventListener('keydown', function(e) {
            if (e.key === 'Backspace' && this.value.length === 0) {
                if (index > 0) {
                    otpInputs[index - 1].focus();
                }
            }
        });
    });

    // Focus first input on page load
    otpInputs[0].focus();


});
</script>
